import moment from 'moment'
const rules = { required, gt, lt, custom, min, max, integer, phoneNumber, zipCode }

async function custom({ field, form, config, preload }) {
  if (typeof config !== 'function') {
    console.error('Argument config should be a function')
    return false
  }
  let outcome = await config(form)
  if (outcome) {
    return { key: field.key, msg: outcome.msg }
  }
  return false
}

/**
 * error: {
 *  key: '',
 *  msg: []
 * }
 */
function required({ field, form, config, preload }) {
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    const msg = `${selfLabel}为必输项, 不能为空`
    switch (field.component) {
      case 'Attachment':
      case 'ImageUploader':
        if (config && (form[field.key] == undefined || form[field.key].length === 0)) {
          resolve({ key: field.key, msg })
        }
        break
      case 'Switch':
      case 'Switch3':
        if (config && form[field.key] == undefined) {
          resolve({ key: field.key, msg })
        }
        break
      case 'Number':
        if (config && Number.isNaN(parseInt(form[field.key]))) {
          resolve({ key: field.key, msg })
        }
        break
      default:
        if (config && !form[field.key]) {
          resolve({ key: field.key, msg })
        }
    }
    resolve(false)
  })
}

function gt(props) {
  return glt({ ...props, great: true })
}

function lt(props) {
  return glt({ ...props, great: false })
}

function glt({ field, form, config, preload, fields, great }) {
  return new Promise((resolve, reject) => {
    const target = fields.filter(field => field.key == config)[0]
    const targetLabel = preload.label(target, form)
    const selfLabel = preload.label(field, form)
    let selfNumber, targetNumber
    let msg = `${selfLabel}必须${great ? '大' : '小'}于${targetLabel}`
    switch (field.component) {
      case 'Date':
      case 'DateTime':
        msg = `${selfLabel}必须${great ? '晚' : '早'}于${targetLabel}`
        const selfDate = new Date(form[field.key])
        const targetDate = new Date(form[config])
        if ((great && selfDate <= targetDate) || (!great && selfDate >= targetDate)) {
          return resolve({ key: field.key, msg })
        }
        break
      default:
        selfNumber = Number(form[field.key])
        targetNumber = Number(form[config])
        if ((great && (selfNumber <= targetNumber)) || (!great && (selfNumber <= targetNumber))) {
          return resolve({ key: field.key, msg })
        }
    }
    resolve(false)
  })
}

function min({ field, form, config, preload }) {
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    let msg = `${selfLabel}不能小于${config}`
    switch (field.component) {
      case 'Date':
      case 'DateTime':
        msg = `${selfLabel}不能早于${config}`
        if (moment(config).isValid() && moment(form[field.key]).isBefore(config)) {
          return resolve({ key: field.key, msg })
        }
        break
      case 'ImageUploader':
        msg = `${selfLabel}数量不能小于${config}`
        if (!form[field.key] || (form[field.key] && form[field.key].length < config)) {
          resolve({ key: field.key, msg })
        }
      default:
        if (form[field.key] < config) {
          resolve({ key: field.key, msg })
        }
    }
    resolve(false)
  })
}

function max({ field, form, config, preload }) {
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    let msg = `${selfLabel}不能大于${config}`
    switch (field.component) {
      case 'Date':
      case 'DateTime':
        msg = `${selfLabel}不能晚于${config}`
        if (moment(config).isValid() && moment(form[field.key]).isAfter(config)) {
          return resolve({ key: field.key, msg })
        }
        break
      default:
        if (form[field.key] > config) {
          resolve({ key: field.key, msg })
        }
    }
    resolve(false)
  })
}

function integer({ field, form, config, preload }) {
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    const msg = `${selfLabel}不能是小数`
    if (config && field.component === 'Number' && !isNaN(parseInt(form[field.key])) && !Number.isInteger(Number(form[field.key]))) {
      resolve({ key: field.key, msg })
    }
    resolve(false)
  })
}

function phoneNumber({ field, form, config, preload }) {
  const reg = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    const msg = `请输入合法的${selfLabel}`
    if (config && form[field.key] !== '' && form[field.key] && !reg.test(form[field.key])) {
      resolve({ key: field.key, msg })
    }
    resolve(false)
  })
}

function zipCode({ field, form, config, preload }) {
  const reg = /^[1-9]\d{5}$/;
  return new Promise((resolve, reject) => {
    const selfLabel = preload.label(field, form)
    const msg = `请输入合法的${selfLabel}`
    if (config && form[field.key] !== '' && form[field.key] && !reg.test(form[field.key])) {
      resolve({ key: field.key, msg })
    }
    resolve(false)
  })
}
/**
 * rule: {
 *  field: {},
 *  form: whole form value,
 *  config: one rule content,
 *  fields: all fields in form,
 *  preload: for get value or label of field
 * }
 */
function validation(form, fields, preload) {
  const promises = []
  fields.forEach(field => {
    if (field.rule) {
      Object.keys(field.rule).forEach(key => {
        promises.push(rules[key]({ field, form, config: field.rule[key], fields, preload }))
      })
    }
  })
  return Promise.all(promises).then(errors => {
    const error = {}
    errors.forEach(item => {
      if (item) {
        if (error[item.key]) {
          error[item.key].push(item.msg)
        } else {
          error[item.key] = [item.msg]
        }
      }
    })
    return Promise.resolve(error)
  }, err => console.log(err))
}

export default {
  rules,
  validation
}
