import { route_urls, route_config } from './routes'
import urls from './urls'
import { date } from './util'

const cards = {
  get: key => {
    const pick = (labels = []) => {
      const card = JSON.parse(JSON.stringify(cards[key]))
      card.items = card.items.filter(item => labels.includes(item.label))
      return card
    }
    const remove = (labels = []) => {
      const card = JSON.parse(JSON.stringify(cards[key]))
      card.items = card.items.filter(item => !labels.includes(item.label))
      return card
    }
    return {
      ...cards[key],
      pick,
      remove
    }
  },
  //notAcctCards
  apply: {
    module: 'asset',
    title: '申请',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '人工申请',
        warning: true,
        // count: { pathname: urls.queryZKbStatisticsList,
        target: { pathname: route_urls.wx.zkb_apply, query: { orderType: 'Artificial' } }
      },
      {
        component: 'Link',
        label: '备件申请',
        warning: true,
        // count: { pathname: urls.queryZKbStatisticsList,
        target: { pathname: route_urls.wx.zkb_apply, query: { orderType: 'Parts' } }
      },
      {
        component: 'Link',
        label: '探头申请',
        warning: true,
        // count: { pathname: urls.queryZKbStatisticsList,
        target: { pathname: route_urls.wx.zkb_apply, query: { orderType: 'Probe' } }
      }
    ]
  },
  //notAcctCards
  zkb_apply: {
    module: 'asset',
    title: 'ZKB FE维修',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '待办',
        warning: true,
        count: {
          pathname: urls.queryZKbStatisticsList,
          query: { queryType: 'pending', page: 0, pageSize: 1 },
          type: 'post'
        },
        target: { pathname: route_urls.wx.zkb_list, query: { type: 'zkb', queryType: 'pending' } }
      },
      {
        component: 'Link',
        label: '全部订单',
        count: {
          pathname: urls.queryZKbStatisticsList,
          query: { queryType: 'all', page: 0, pageSize: 1 },
          type: 'post'
        },
        target: { pathname: route_urls.wx.zkb_list, query: { type: 'zkb', queryType: 'all' } }
      }
    ]
  },
  //notAcctCards
  fe_apply: {
    module: 'asset',
    title: 'GE医疗 FE维修',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '待办',
        warning: true,
        count: { pathname: urls.zkbFePartPage, query: { queryType: 'pending', page: 0, pageSize: 1 }, type: 'post' },
        target: { pathname: route_urls.wx.zkb_list, query: { type: 'ge', queryType: 'pending' } }
      },
      {
        component: 'Link',
        label: '全部订单',
        count: { pathname: urls.zkbFePartPage, query: { queryType: 'all', page: 0, pageSize: 1 }, type: 'post' },
        target: { pathname: route_urls.wx.zkb_list, query: { type: 'ge', queryType: 'all' } }
      }
    ]
  },
  application_specified: {
    module: 'asset',
    title: '积分申请',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '待办',
        warning: true,
        count: {
          pathname: urls.queryZkbIntehralOrderList,
          query: { queryType: 'pending', page: 0, pageSize: 1 },
          type: 'post'
        },
        target: { pathname: route_urls.wx.zkbspecified, query: { type: 'specified', queryType: 'pending' } }
      },
      {
        component: 'Link',
        label: '全部订单',
        count: {
          pathname: urls.queryZkbIntehralOrderList,
          query: { queryType: 'all', page: 0, pageSize: 1 },
          type: 'post'
        },
        target: { pathname: route_urls.wx.zkbspecified, query: { type: 'specified', queryType: 'all' } }
      }
    ]
  },
  no_asset_sr: {
    module: 'incident',
    title: '无台账报修',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.serviceRequestsNoAsset + '/summary' },
    items: [
      {
        component: 'Link',
        label: '未完成',
        functions: 'REPAIR_NO_ASSET_ORDER_MOBILE_WAIT',
        summaryItem: 'unfinished',
        target: { pathname: route_urls.home_wx + '/no-asset-wo-list', query: { status: 0 } }
      },
      {
        component: 'Link',
        label: '待派工',
        functions: 'REPAIR_NO_ASSET_ORDER_MOBILE_DISPATCH',
        summaryItem: 'assignPending',
        target: { pathname: route_urls.home_wx + '/no-asset-wo-list', query: { status: 2 } }
      },
      {
        component: 'Link',
        label: '待接单',
        functions: 'REPAIR_NO_ASSET_ORDER_MOBILE_ACCEPT',
        summaryItem: 'acceptPending',
        target: { pathname: route_urls.home_wx + '/no-asset-wo-list', query: { status: 3 } }
      },
      {
        component: 'Link',
        label: '待关单',
        functions: 'REPAIR_NO_ASSET_ORDER_MOBILE_CLOSE_PENDING',
        summaryItem: 'closePending',
        target: { pathname: route_urls.home_wx + '/no-asset-wo-list', query: { status: 4 } }
      },
      {
        component: 'Link',
        label: '已关单',
        functions: 'REPAIR_NO_ASSET_ORDER_MOBILE_CLOSED',
        summaryItem: 'finished',
        target: { pathname: route_urls.home_wx + '/no-asset-wo-list', query: { status: 5 } }
      }
    ]
  },
  my_sr: {
    module: 'incident',
    title: '我的报修',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.requestSummaryMine },
    items: [
      {
        component: 'Link',
        label: '所有未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 0 } },
        summaryItem: 'mySrAllUnfinished',
        warning: true
      },
      {
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/consoleSrList',
          state: { from: 'workorderList' },
          query: { step: 4 }
        },
        summaryItem: 'mySrPendingReview'
      },
      {
        component: 'Link',
        label: '待验收',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 5 } },
        summaryItem: 'mySrPendingAcceptance'
      },
      {
        component: 'Link',
        label: '已完成报修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 0 } },
        summaryItem: 'mySrFinished'
      },
      {
        component: 'Link',
        label: '已取消报修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 3, submittedBy: 0 } },
        summaryItem: 'mySrConsoled'
      },
      {
        component: 'Link',
        label: '我的科室',
        target: { pathname: route_urls.home_wx + '/partsDeptAudit', query: {} },
        count: {}
      }
    ]
  },
  configurable_my_sr: {
    module: 'incident',
    title: '我的报修',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    workflowConfig: 'enableSendRepair',
    summary: { pathname: urls.requestSummaryMine },
    items: [
      {
        component: 'Link',
        label: '所有未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 0 } },
        summaryItem: 'mySrAllUnfinished',
        warning: true
      },
      {
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/consoleSrList',
          state: { from: 'workorderList' },
          query: { step: 4 }
        },
        summaryItem: 'mySrPendingReview'
      },
      {
        component: 'Link',
        label: '待验收',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 5 } },
        summaryItem: 'mySrPendingAcceptance'
      },
      {
        component: 'Link',
        label: '待送修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { stepId: 10, submittedBy: 0 } },
        summaryItem: 'mySrWaitSending'
      },
      {
        component: 'Link',
        label: '待领取',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { stepId: 11, submittedBy: 0 } },
        summaryItem: 'mySrWaitReturn'
      },
      {
        component: 'Link',
        label: '已完成报修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 0 } },
        summaryItem: 'mySrFinished'
      },
      {
        component: 'Link',
        label: '已取消报修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 3, submittedBy: 0 } },
        summaryItem: 'mySrConsoled'
      },
      {
        component: 'Link',
        label: '我的科室',
        target: { pathname: route_urls.home_wx + '/partsDeptAudit', query: {} },
        count: {}
      }
    ]
  },
  team_sr: {
    module: 'incident',
    title: '我的报修',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.requestSummaryTeam },
    items: [
      {
        component: 'Link',
        label: '我的 - 待审核',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 4 } },
        summaryItem: 'teamSrPendingReviewOne'
      },
      {
        component: 'Link',
        label: '我的 - 待验收',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 5 } },
        summaryItem: 'teamSrPendingAcceptanceOne'
      },
      {
        component: 'Link',
        label: '我的 - 未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 0 } },
        summaryItem: 'teamSrUnfinishedOne',
        warning: true
      },
      {
        component: 'Link',
        label: '我的 - 已完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 0 } },
        summaryItem: 'teamSrFinishedOne'
      },
      {
        component: 'Link',
        label: '科室 - 未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 1 } },
        summaryItem: 'teamSrUnfinishedTwo',
        warning: true
      },
      {
        component: 'Link',
        label: '科室 - 已完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 1 } },
        summaryItem: 'teamSrFinishedTwo'
      },
      {
        component: 'Link',
        label: '已取消',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 3, submittedBy: 0 } },
        summaryItem: 'teamSrConsoled'
      },
      {
        component: 'Link',
        label: '我的科室',
        target: { pathname: route_urls.home_wx + '/partsDeptAudit', query: {} },
        count: {}
      }
    ]
  },
  configurable_team_sr: {
    module: 'incident',
    title: '我的报修',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    workflowConfig: 'enableSendRepair',
    summary: { pathname: urls.requestSummaryTeam },
    items: [
      {
        component: 'Link',
        label: '我的 - 待审核',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 4 } },
        summaryItem: 'teamSrPendingReviewOne'
      },
      {
        component: 'Link',
        label: '我的 - 待验收',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { step: 5 } },
        summaryItem: 'teamSrPendingAcceptanceOne'
      },
      {
        component: 'Link',
        label: '我的 - 待送修',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { stepId: 10, submittedBy: 0 } },
        summaryItem: 'mySrWaitSending'
      },
      {
        component: 'Link',
        label: '我的 - 待领取',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { stepId: 11, submittedBy: 0 } },
        summaryItem: 'mySrWaitReturn'
      },
      {
        component: 'Link',
        label: '我的 - 未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 0 } },
        summaryItem: 'teamSrUnfinishedOne',
        warning: true
      },
      {
        component: 'Link',
        label: '我的 - 已完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 0 } },
        summaryItem: 'teamSrFinishedOne'
      },
      {
        component: 'Link',
        label: '科室 - 未完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 1, submittedBy: 1 } },
        summaryItem: 'teamSrUnfinishedTwo',
        warning: true
      },
      {
        component: 'Link',
        label: '科室 - 已完成',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 2, submittedBy: 1 } },
        summaryItem: 'teamSrFinishedTwo'
      },
      {
        component: 'Link',
        label: '已取消',
        target: { pathname: route_urls.home_wx + '/consoleSrList', query: { status: 3, submittedBy: 0 } },
        summaryItem: 'teamSrConsoled'
      },
      {
        component: 'Link',
        label: '我的科室',
        target: { pathname: route_urls.home_wx + '/partsDeptAudit', query: {} },
        count: {}
      }
    ]
  },
  asset: {
    module: 'asset',
    title: '设备查询',
    color: '#3573c5',
    expend: true,
    offline: true,
    columns: 2,
    summary: { pathname: urls.assetSummary },
    // features: 'ASSET_INFO',
    items: [
      {
        functions: 'ASSET_MOBILE_SEARCH_NORMAL',
        component: 'Link',
        label: '正常',
        target: { pathname: route_urls.wx.device, query: { status: '[1]' } },
        summaryItem: 'normalNum'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_HALT',
        component: 'Link',
        label: '停机',
        target: { pathname: route_urls.wx.device, query: { status: '[2]' } },
        summaryItem: 'faultNum',
        warning: true
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_RESTRICTED_USE',
        component: 'Link',
        label: '限制功能使用',
        target: { pathname: route_urls.wx.device, query: { status: '[3]' } },
        summaryItem: 'acceptableNum'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_TO_BE_SCRAPPED',
        component: 'Link',
        label: '待报废',
        target: { pathname: route_urls.wx.device, query: { status: '[4]' } },
        summaryItem: 'toDeprecate'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_DEACTIVATED',
        component: 'Link',
        label: '已停用',
        target: { pathname: route_urls.wx.device, query: { status: '[5]' } },
        summaryItem: 'outOfService'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_ALL',
        offline: true,
        component: 'Link',
        label: '所有设备',
        target: { pathname: route_urls.wx.device, query: { status: '[1,2,3,4,5]' } },
        summaryItem: 'validNum'
      },
      {
        component: 'Link',
        functions: 'SPECIALASSET_MOBILE_SEARCH_ALL',
        label: '特种设备',
        target: { pathname: route_urls.wx.device, query: { isSpecial: true } },
        summaryItem: 'specialAssetNum'
      }
    ]
  },
  //notAcctCards
  asset_ib: {
    module: 'assetIb',
    title: 'IB设备查询',
    color: '#3573c5',
    expend: true,
    columns: 2,
    summary: { pathname: urls.assetSummaryIB },
    items: [
      {
        component: 'Link',
        label: '报警',
        target: { pathname: route_urls.wx.device, query: { ibDeviceStatus: 2, hasParent: false } },
        summaryItem: 'inAlarm'
      },
      {
        component: 'Link',
        label: '维修中',
        target: { pathname: route_urls.wx.device, query: { ibDeviceStatus: 3, hasParent: false } },
        summaryItem: 'inMaintenance'
      },
      {
        component: 'Link',
        label: '连通',
        target: { pathname: route_urls.wx.device, query: { connectToInsite: 2, hasParent: false } },
        summaryItem: 'connecting'
      },
      {
        component: 'Link',
        label: '未连通',
        target: { pathname: route_urls.wx.device, query: { connectToInsite: 1, hasParent: false } },
        summaryItem: 'notConnect'
      },
      {
        component: 'Link',
        label: '远程守护',
        target: { pathname: route_urls.wx.device, query: { ibStatus: [1, 4], hasParent: false } },
        summaryItem: 'remoteWatch'
      },
      {
        component: 'Link',
        label: '全部设备',
        target: { pathname: route_urls.wx.device, query: { hasParent: false } },
        summaryItem: 'total'
      }
    ]
  },
  my_wo: {
    //assetStaff
    module: 'incident',
    title: '我的维修工单',
    color: '#e8a736',
    offline: true,
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.workOrderSummary },
    items: [
      {
        component: 'Link',
        label: '待接单',
        target: { pathname: route_urls.home_wx + '/unAcceptList', query: { con: 0 } },
        summaryItem: 'accept',
        warning: true
      },
      {
        offline: true,
        component: 'Link',
        label: '维修中',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 0 }, from: 'repairList' }
        },
        summaryItem: 'repair'
      },
      {
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 5 }, from: 'repairList' }
        },
        summaryItem: 'pendingReview'
      },
      {
        component: 'Link',
        label: '待验收',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 4 }, from: 'repairList' }
        },
        summaryItem: 'pendingAcceptance'
      },
      {
        component: 'Link',
        label: '待关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 1 }, from: 'repairList' }
        },
        summaryItem: 'waitClosed'
      },
      {
        component: 'Link',
        label: '已关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 2 }, from: 'repairList' }
        },
        summaryItem: 'closed'
      }
      // { component: 'Link', label: '已取消', target: { pathname: route_urls.home_wx + '/consoleWoList', state: {url: urls.woPickedup, cons: {status: 3}, from: 'repairList'} }, count: { pathname: urls.woPickedup, query: {status : 3, pageSize: 1} } }
    ]
  },
  configurable_my_wo: {
    //assetStaff
    module: 'incident',
    title: '我的维修工单',
    color: '#e8a736',
    expand: true,
    offline: true,
    columns: 2, // row/column
    workflowConfig: 'enableSendRepair',
    summary: { pathname: urls.workOrderSummary },
    items: [
      {
        component: 'Link',
        label: '待接单',
        target: { pathname: route_urls.home_wx + '/unAcceptList', query: { con: 0 } },
        summaryItem: 'accept',
        warning: true
      },
      {
        component: 'Link',
        label: '维修中',
        offline: true,
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 0 }, from: 'repairList' }
        },
        summaryItem: 'repair'
      },
      {
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 5 }, from: 'repairList' }
        },
        summaryItem: 'pendingReview'
      },
      {
        component: 'Link',
        label: '待验收',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 4 }, from: 'repairList' }
        },
        summaryItem: 'pendingAcceptance'
      },
      {
        component: 'Link',
        label: '待送修',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 10 }, from: 'repairList' }
        },
        summaryItem: 'waitSending'
      },
      {
        component: 'Link',
        label: '待返还',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 11 }, from: 'repairList' }
        },
        summaryItem: 'waitReturn'
      },
      {
        component: 'Link',
        label: '待关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 1 }, from: 'repairList' }
        },
        summaryItem: 'waitClosed'
      },
      {
        component: 'Link',
        label: '已关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 2 }, from: 'repairList' }
        },
        summaryItem: 'closed'
      }
      // { component: 'Link', label: '已取消', target: { pathname: route_urls.home_wx + '/consoleWoList', state: {url: urls.woPickedup, cons: {status: 3}, from: 'repairList'} }, count: { pathname: urls.woPickedup, query: {status : 3, pageSize: 1} } }
    ]
  },
  wo_new: {
    //assetStaff    no use
    module: 'incident',
    title: '我的待接工单',
    color: '#e53838',
    expand: true,
    columns: 2, // row/column
    items: [
      {
        component: 'Link',
        label: '我的',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woToPickup, cons: { type: 0 }, from: 'repairList' }
        },
        count: { pathname: urls.woToPickup, query: { type: 0, pageSize: 1 } },
        warning: true
      },
      {
        component: 'Link',
        label: '同组',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woToPickup, cons: { type: 1 }, from: 'repairList' }
        },
        count: { pathname: urls.woToPickup, query: { type: 1, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '同院',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woToPickup, cons: { type: 2 }, from: 'repairList' }
        },
        count: { pathname: urls.woToPickup, query: { type: 2, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '他院',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woToPickup, cons: { type: 3 }, from: 'repairList' }
        },
        count: { pathname: urls.woToPickup, query: { type: 3, pageSize: 1 } }
      }
    ]
  },
  mgr_wo: {
    //assetHead
    module: 'incident',
    title: '报修管理',
    color: '#e8a736',
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.dispatcherCount },
    items: [
      {
        component: 'Link',
        label: '待派工',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 0 }, from: 'workorderList' }
        },
        summaryItem: 'noDispatch',
        warning: true
      },
      {
        component: 'Link',
        label: '已派工',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 4 }, from: 'workorderList' }
        },
        summaryItem: 'doneDispatch'
      },
      {
        component: 'Link',
        label: '已关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 3 }, from: 'workorderList' }
        },
        summaryItem: 'closeDispatch'
      },
      {
        component: 'Link',
        label: '已取消',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 5 }, from: 'assethead' }
        },
        summaryItem: 'cancelDispatch'
      }
    ]
  },
  dis_wo: {
    //dispatcher
    module: 'incident',
    title: '报修管理',
    color: '#e8a736',
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.dispatcherCount },
    items: [
      {
        component: 'Link',
        label: '待派工',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 0 }, from: 'workorderList' }
        },
        summaryItem: 'noDispatch',
        warning: true
      },
      {
        component: 'Link',
        label: '已派工',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 4 }, from: 'workorderList' }
        },
        summaryItem: 'doneDispatch'
      },
      {
        component: 'Link',
        label: '已关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 3 }, from: 'assethead' }
        },
        summaryItem: 'closeDispatch'
      },
      {
        component: 'Link',
        label: '已取消',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 5 }, from: 'assethead' }
        },
        summaryItem: 'cancelDispatch'
      }
    ]
  },
  dis_my_wo: {
    //assetHead and assetStaff
    module: 'incident',
    title: '我的维修工单',
    color: '#e8a736',
    offline: true,
    expand: true,
    columns: 2, // row/column
    summary: { pathname: urls.workOrderSummary },
    items: [
      {
        component: 'Link',
        label: '待派工',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.dispatcher, cons: { status: 0 }, from: 'workorderList' }
        },
        count: { pathname: urls.dispatcher, query: { status: 0, pageSize: 1 } },
        warning: true
      },
      {
        component: 'Link',
        label: '待接单',
        target: { pathname: route_urls.home_wx + '/unAcceptList', query: { con: 0 } },
        summaryItem: 'accept',
        warning: true
      },
      {
        offline: true,
        component: 'Link',
        label: '维修中',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 0 }, from: 'repairList' }
        },
        summaryItem: 'repair'
      },
      {
        component: 'Link',
        label: '待验收',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 4 }, from: 'repairList' }
        },
        summaryItem: 'pendingAcceptance'
      },
      {
        component: 'Link',
        label: '待关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 1 }, from: 'repairList' }
        },
        summaryItem: 'waitClosed'
      },
      {
        component: 'Link',
        label: '已关单',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 2 }, from: 'repairList' }
        },
        summaryItem: 'closed'
      }
      // { component: 'Link', label: '已取消', target: { pathname: route_urls.home_wx + '/consoleWoList', state: {url: urls.dispatcher, cons: {status: 5}, from: 'assethead'} }, count: { pathname: urls.dispatcher, query: {status : 5, pageSize: 1} } }
    ]
  },
  mgr_ispt: {
    module: 'incident',
    title: '我的巡检工单',
    color: '#0a9ec1',
    expand: true,
    columns: 2, // row/column
    items: [
      {
        component: 'Link',
        label: '计划中',
        target: { pathname: route_urls.home_wx + '/inspection/ins', query: { orderStatus: 'open' } },
        count: { pathname: urls.inspection, query: { orderType: 1, orderStatus: 'open', page: 0, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '已延期',
        target: { pathname: route_urls.home_wx + '/inspection/ins', query: { orderStatus: 'postponed' } },
        count: { pathname: urls.inspection, query: { orderType: 1, orderStatus: 'postponed', page: 0, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '已完成',
        target: { pathname: route_urls.home_wx + '/inspection/ins', query: { orderStatus: 'latestdone' } },
        count: { pathname: urls.inspection, query: { orderType: 1, orderStatus: 'latestdone', page: 0, pageSize: 1 } }
      }
    ]
  },
  mgr_met: {
    module: 'dispatch',
    title: '我的计量工单',
    color: '#3573c5',
    expand: true,
    columns: 2, // row/column
    items: [
      {
        component: 'Link',
        label: '计划中',
        target: { pathname: route_urls.home_wx + '/inspection/met', query: { orderStatus: 'open' } },
        count: { pathname: urls.inspection, query: { orderType: 2, orderStatus: 'open', page: 0, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '已延期',
        target: { pathname: route_urls.home_wx + '/inspection/met', query: { orderStatus: 'postponed' } },
        count: { pathname: urls.inspection, query: { orderType: 2, orderStatus: 'postponed', page: 0, pageSize: 1 } }
      },
      {
        component: 'Link',
        label: '已完成',
        target: { pathname: route_urls.home_wx + '/inspection/met', query: { orderStatus: 'latestdone' } },
        count: { pathname: urls.inspection, query: { orderType: 2, orderStatus: 'latestdone', page: 0, pageSize: 1 } }
      }
    ]
  },
  mgr_pm_clinic: {
    module: 'maintain',
    title: '保养备件审核',
    color: '#086e87',
    expand: true,
    columns: 2,
    summary: { pathname: urls.pmOrderClinicCount },
    items: [
      {
        component: 'Link',
        label: '待审核',
        summaryItem: 'approval',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForClinic, cons: { status: 3 }, from: '?' }
        },
        warning: true
      },
      {
        component: 'Link',
        label: '待验收',
        summaryItem: 'acceptance',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 6, batch: 0, acceptance: 0 }, from: '?' }
        }
      }
    ]
  },
  mgr_pm_head: {
    module: 'maintain',
    title: '保养管理',
    color: '#086e87',
    expand: true,
    columns: 2,
    summary: { pathname: urls.pmOrderHeadCount },
    items: [
      {
        // functions: 'MAINTAIN_ORDER_DISPATCH',
        component: 'Link',
        label: '待派工',
        summaryItem: 'assign',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForHead, cons: { status: 0 }, from: '?' }
        },
        warning: true
      },
      {
        // functions: 'MAINTAIN_ORDER_DISPATCH',
        component: 'Link',
        label: '已派工',
        summaryItem: 'assigned',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForHead, cons: { status: 3 }, from: '?' }
        }
      },
      {
        // functions: 'MAINTAIN_ORDER_APPROVE',
        component: 'Link',
        label: '待审核',
        summaryItem: 'approval',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForClinic, cons: { status: 3 }, from: '?' }
        }
      },
      {
        // functions: 'MAINTAIN_ORDER_ACCEPT',
        component: 'Link',
        label: '待验收',
        summaryItem: 'acceptance',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForHead, cons: { status: 6, batch: 0, acceptance: 0 }, from: '?' }
        }
      },
      {
        // functions: 'MAINTAIN_ORDER_VIEW_CLOSED',
        component: 'Link',
        label: '已关单',
        summaryItem: 'finished',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForHead, cons: { status: 4 }, from: '?' }
        }
      },
      {
        // functions: 'MAINTAIN_ORDER_CANCEL',
        component: 'Link',
        label: '已取消',
        summaryItem: 'cancel',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForHead, cons: { status: 5 }, from: '?' }
        }
      }
    ]
  },
  mgr_pm_staff: {
    module: 'maintain',
    title: '我的保养工单',
    color: '#086e87',
    offline: true,
    expand: true,
    summary: { pathname: urls.pmOrderPickUpCount },
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '待接单',
        summaryItem: 'accept',
        target: {
          pathname: route_urls.home_wx + '/unAcceptPmList',
          state: { url: urls.pmOrderToPickUp, cons: { status: 0 }, from: '?' }
        },
        warning: true
      },
      {
        offline: true,
        component: 'Link',
        label: '已计划',
        summaryItem: 'repair',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          query: { con: 0 },
          state: { url: urls.pmOrderPickedUp, cons: { status: 0, batch: 0 }, from: '?' }
        }
      },
      {
        component: 'Link',
        label: '待审核',
        summaryItem: 'approval',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 2 }, from: '?' }
        }
      },
      {
        component: 'Link',
        label: '待验收',
        summaryItem: 'acceptance',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 6, batch: 0, acceptance: 0 }, from: '?' }
        }
      },
      // { component: 'Link', label: '待关单', summaryItem: 'close', target: { pathname: route_urls.home_wx + '/consolePmList', state: { url: urls.pmOrderPickedUp, cons: { status: 1 }, from: '?' } }, warning: true },
      {
        component: 'Link',
        label: '已关单',
        summaryItem: 'finished',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 4 }, from: '?' }
        }
      },
      {
        component: 'Link',
        label: '已取消',
        summaryItem: 'cancel',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 5 }, from: '?' }
        }
      }
    ]
  },
  performance: {
    module: 'performance',
    title: '综合绩效分析',
    columns: 1,
    items: [
      {
        functions: 'HISPERFORM_EFFICIENCY_MOBILE_EQUIPMENT',
        component: 'Link',
        label: '设备综合绩效分析',
        target: { pathname: '/wx/device/performance/single', query: {} }
      },
      {
        functions: 'HISPERFORM_EFFICIENCY_MOBILE_DEPARTMENT',
        component: 'Link',
        label: '综合绩效科室汇总',
        target: `/reports/officebenefit?view=mobile`
      }
    ]
  },
  mgr_inv: {
    module: 'inventory',
    title: '盘点工单',
    offline: true,
    color: '#e8a736',
    expand: true,
    columns: 2, // row/column
    items: [
      {
        functions: 'ASSET_INVENTORY_MOBILE_CREATE',
        component: 'Link',
        label: '新建盘点工单',
        target: { pathname: route_urls.wx.inventory + '/newOrder', query: {} }
      },
      {
        functions: 'ASSET_INVENTORY_MOBILE_CLOSE',
        component: 'Link',
        label: '关闭盘点工单',
        target: { pathname: route_urls.wx.inventory + '/openOrderList', query: {} }
      },
      {
        functions: 'ASSET_INVENTORY_MOBILE_STAFF_SELECT',
        component: 'Link',
        label: '查看盘点工单',
        target: { pathname: route_urls.wx.inventory + '/orderList', query: {} }
      },
      {
        functions: 'ASSET_INVENTORY_MOBILE_STAFF_SELECT',
        offline: true,
        component: 'Button',
        label: '扫码盘点',
        type: 'primary',
        target: { pathname: route_urls.wx.inventory + '/scan', query: {} }
      }
    ]
  },
  staff_inv: {
    module: 'inventory',
    title: '盘点工单',
    color: '#e8a736',
    expand: true,
    offline: true,
    columns: 2, // row/column
    items: [
      {
        functions: 'ASSET_INVENTORY_MOBILE_SELECT',
        component: 'Button',
        label: '查看盘点工单',
        target: { pathname: route_urls.wx.inventory + '/orderList', query: {} }
      },
      {
        functions: 'ASSET_INVENTORY_MOBILE_SCAN',
        offline: true,
        component: 'Button',
        label: '扫码盘点',
        type: 'primary',
        target: { pathname: route_urls.wx.inventory + '/scan', query: {} }
      }
    ]
  },
  alarm_asset: {
    module: 'alarm',
    title: '设备报警',
    color: '#dd6b1f',
    expand: true,
    columns: 1,
    items: [
      {
        component: 'Link',
        label: '今日温湿度与电气环境报警',
        target: { pathname: route_urls.wx.alarm, query: {} },
        count: { pathname: urls.ee_category, query: date.day },
        warning: true
      }
      // { component: 'Link', label: '30日内MR冷头报警', target: { pathname: route_urls.wx.coldHead, query: {} }, count: { pathname: urls.coldHead, query: date.month }, warning: true }
    ]
  },
  //notAcctCards
  edu_apm: {
    title: 'APM专区',
    expand: true,
    column: 1,
    items: [{ component: 'Link', label: '敬请期待...' }]
  },
  //notAcctCards
  edu_user: {
    title: '临床专区',
    expand: true,
    column: 1,
    items: [{ component: 'Link', label: 'E学E用课程', target: urls.goGeEdu }]
  },
  mvs_wo: {
    enableMVS: true,
    module: 'incident',
    title: '我的维修工单',
    color: '#e8a736',
    expand: true,
    offline: true,
    summary: { pathname: urls.mvsWoCounts },
    columns: 2, // row/column
    items: [
      {
        component: 'Link',
        label: '待接单',
        mvsType: 1,
        summaryItem: 'accept',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woToPickup, cons: { type: 0 }, from: 'repairList' }
        },
        warning: true
      },
      {
        component: 'Link',
        label: '维修中',
        offline: true,
        mvsType: 2,
        summaryItem: 'repair',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 0 }, from: 'repairList' }
        }
      },
      {
        component: 'Link',
        label: '待验收',
        mvsType: 3,
        summaryItem: 'ack',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 4 }, from: 'repairList' }
        }
      },
      {
        component: 'Link',
        label: '待审核',
        mvsType: 4,
        summaryItem: 'approval',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 5 }, from: 'repairList' }
        }
      },
      {
        component: 'Link',
        label: '待关单',
        mvsType: 5,
        summaryItem: 'close',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 1 }, from: 'repairList' }
        }
      },
      {
        component: 'Link',
        label: '已关单',
        mvsType: 6,
        summaryItem: 'finished',
        target: {
          pathname: route_urls.home_wx + '/consoleWoList',
          state: { url: urls.woPickedup, cons: { status: 2 }, from: 'repairList' }
        }
      }
    ]
  },
  mvs_pm: {
    enableMVS: true,
    module: 'maintain',
    title: '我的保养工单',
    color: '#086e87',
    expand: true,
    summary: { pathname: urls.mvsPoCounts },
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '待接单',
        mvsType: 7,
        summaryItem: 'accept',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderToPickUp, cons: { type: 0 }, from: '?' }
        },
        warning: true
      },
      {
        component: 'Link',
        label: '已计划',
        mvsType: 8,
        summaryItem: 'maintain',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          query: { con: 0 },
          state: { url: urls.pmOrderPickedUp, cons: { status: 0, batch: 0 }, from: '?' }
        }
      },
      {
        component: 'Link',
        label: '待审核',
        mvsType: 9,
        summaryItem: 'approval',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderForClinic, cons: { status: 3 }, from: '?' }
        }
      },
      // { component: 'Link', label: '待关单', mvsType: 10, summaryItem: 'close', target: { pathname: route_urls.home_wx + '/consolePmList', state: { url: urls.pmOrderPickedUp, cons: { status: 1 }, from: '?' } }, warning: true },
      {
        component: 'Link',
        label: '已关单',
        mvsType: 11,
        summaryItem: 'finished',
        target: {
          pathname: route_urls.home_wx + '/consolePmList',
          state: { url: urls.pmOrderPickedUp, cons: { status: 4 }, from: '?' }
        }
      }
    ]
  },
  mvs_asset: {
    enableMVS: true,
    module: 'asset',
    title: '设备查询',
    color: '#3573c5',
    expend: true,
    columns: 2,
    summary: { pathname: urls.mvsAssetCounts },
    items: [
      {
        functions: 'ASSET_MOBILE_SEARCH_NORMAL',
        component: 'Link',
        label: '正常',
        mvsType: 12,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 0 } },
        summaryItem: 'normal'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_HALT',
        component: 'Link',
        label: '停机',
        mvsType: 13,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 1 } },
        summaryItem: 'fault',
        warning: true
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_RESTRICTED_USE',
        component: 'Link',
        label: '限制功能使用',
        mvsType: 14,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 2 } },
        summaryItem: 'acceptable'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_ALL',
        component: 'Link',
        label: '所有设备',
        mvsType: 15,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 3 } },
        summaryItem: 'valid'
      }
      // { component: 'Link', label: '已报废', mvsType: 16, target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 4 } }, summaryItem: 'invalid' }
    ]
  },
  //new add fso
  fso_asset: {
    enableMVS: true,
    module: 'asset',
    title: '设备查询',
    color: '#3573c5',
    expend: true,
    columns: 2,
    summary: { pathname: urls.fsoAssetCounts },
    items: [
      {
        functions: 'ASSET_MOBILE_SEARCH_NORMAL',
        component: 'Link',
        label: '正常',
        fsoType: 12,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 0, userType: 'fso' } },
        summaryItem: 'normal'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_HALT',
        component: 'Link',
        label: '停机',
        fsoType: 13,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 1, userType: 'fso' } },
        summaryItem: 'fault',
        warning: true
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_RESTRICTED_USE',
        component: 'Link',
        label: '限制功能使用',
        fsoType: 14,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 2, userType: 'fso' } },
        summaryItem: 'acceptable'
      },
      {
        functions: 'ASSET_MOBILE_SEARCH_ALL',
        component: 'Link',
        label: '所有设备',
        fsoType: 15,
        target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 3, userType: 'fso' } },
        summaryItem: 'valid'
      }
      // { component: 'Link', label: '已报废', mvsType: 16, target: { pathname: route_urls.home_wx + '/deviceMvsList/', query: { status: 4 } }, summaryItem: 'invalid' }
    ]
  },
  asset_dispatch_center_Normal_Manager: {
    module: 'dispatch',
    title: '集中调剂',
    i18n: 'EquipmentSwap',
    color: '#3573c5',
    expend: true,
    columns: 2,
    myself: 'allocationCenter,siteSelectable',
    summary: { pathname: urls.allocationSummary },
    items: [
      //普通调剂，管理员：借用待受理 归还待受理 借用待交付 归还待交付 在借设备 借用已搁置 设备库存统计 扫码强制归还
      {
        functions: 'ADJUST_MOBILE_BORROW_TOBE_ACCEPT',
        component: 'Link',
        label: '借用待受理',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 1 } },
        summaryItem: 'rentRequested'
      },
      {
        functions: 'ADJUST_MOBILE_RETURN_TOBE_ACCEPT',
        component: 'Link',
        label: '归还待受理',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 7 } },
        summaryItem: 'returnRequested'
      },
      {
        functions: 'ADJUST_MOBILE_BORROW_TOBE_RECEIVE',
        component: 'Link',
        label: '借用待交付',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 2 } },
        summaryItem: 'rentAccepted'
      },
      {
        functions: 'ADJUST_MOBILE_RETURN_TOBE_RECEIVE',
        component: 'Link',
        label: '归还待交付',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 8 } },
        summaryItem: 'returnAccepted'
      },
      {
        functions: 'ADJUST_MOBILE_BORROWED_ALL_ASSETS',
        component: 'Link',
        label: '在借设备',
        target: { pathname: route_urls.home_wx + '/rentedDevices' },
        summaryItem: 'inRent'
      },
      {
        functions: 'ADJUST_MOBILE_BORROW_SUSPENDED',
        component: 'Link',
        label: '借用已搁置',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 5 } },
        summaryItem: 'rentSuspended'
      },
      {
        functions: 'ADJUST_MOBILE_STORAGE_COUNT',
        component: 'Link',
        label: '设备库存统计',
        target: { pathname: route_urls.home_wx + '/deviceInventory' }
      },
      {
        functions: 'ADJUST_MOBILE_SCAN_COMPULSORILY_BORROW_RETURN',
        component: 'Button',
        type: 'primary',
        label: '扫码强制借还',
        target: { pathname: route_urls.home_wx + '/forceReturn' }
      }
    ]
  },

  asset_dispatch_center_Normal_Clinical: {
    module: 'dispatch',
    title: '集中调剂',
    i18n: 'EquipmentSwap',
    color: '#3573c5',
    expend: true,
    columns: 2,
    myself: 'allocationCenter,siteSelectable',
    summary: { pathname: urls.allocationSummary },
    items: [
      //普通调剂，临床人员：申请借用，申请归还，借用待确认 归还待确认 借用已完成 归还已完成 已申请 已取消 在借设备
      {
        functions: 'ADJUST_MOBILE_APPLY_RETURN',
        component: 'Button',
        type: 'primary',
        label: '申请借用',
        target: { pathname: route_urls.home_wx + '/rentRequest' }
      },
      {
        functions: 'ADJUST_MOBILE_RETURN_COMPLETED',
        component: 'Button',
        type: 'primary',
        label: '申请归还',
        target: { pathname: route_urls.home_wx + '/returnRequest' }
      },
      {
        functions: 'ADJUST_MOBILE_BORROW_TOBE_CONFIRM',
        component: 'Link',
        label: '借用待确认',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 3 } },
        summaryItem: 'rentDelivered'
      },
      {
        functions: 'ADJUST_MOBILE_RETURN_TOBE_CONFIRM',
        component: 'Link',
        label: '归还待确认',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 9 } },
        summaryItem: 'returnDelivered'
      },
      {
        functions: 'ADJUST_MOBILE_BORROW_COMPLETED',
        component: 'Link',
        label: '借用已完成',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 6 } },
        summaryItem: 'rentConfirmed'
      },
      {
        functions: 'ADJUST_MOBILE_RETURN_COMPLETED',
        component: 'Link',
        label: '归还已完成',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: 12 } },
        summaryItem: 'returnConfirmed'
      },
      {
        functions: 'ADJUST_MOBILE_APPLIED',
        component: 'Link',
        label: '已申请',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: [1, 2, 5, 7, 8] } },
        summaryItem: summary =>
          summary.rentRequested +
          summary.rentAccepted +
          summary.returnRequested +
          summary.returnAccepted +
          summary.rentSuspended
      },
      {
        functions: 'ADJUST_MOBILE_CANCELED',
        component: 'Link',
        label: '已取消',
        target: { pathname: route_urls.home_wx + '/requestList', query: { state: [4, 10] } },
        summaryItem: summary => summary.rentCanceled + summary.returnCanceled
      },
      {
        functions: 'ADJUST_MOBILE_BORROWED_ASSETS',
        component: 'Link',
        label: '在借设备',
        target: { pathname: route_urls.home_wx + '/rentedDevices' },
        summaryItem: 'inRent'
      }
    ]
  },
  asset_dispatch_center_Unattended_Clinical: {
    module: 'dispatch',
    title: '无人值守',
    i18n: 'EquipmentSwap',
    color: '#3573c5',
    expend: true,
    columns: 2,
    configItem: 'whetherToEnableUnattended',
    summary: { pathname: urls.allocationSummary },
    items: [
      //无人值守，临床人员：设备预约 设备补录 我的预约
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_RESERVATION',
        component: 'Link',
        label: '设备预约',
        target: { pathname: route_urls.home_wx + '/reservation' }
      },
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_ADDITIONAL_RECORDING',
        component: 'Link',
        label: '设备补录',
        target: { pathname: route_urls.home_wx + '/addReservation' }
      },
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_MY_RESERVATION',
        component: 'Link',
        label: '我的预约',
        target: { pathname: route_urls.home_wx + '/myReservation' }
      }
    ]
  },
  asset_dispatch_center_Unattended_Manager: {
    module: 'dispatch',
    title: '无人值守',
    color: '#3573c5',
    expend: true,
    columns: 2,
    configItem: 'whetherToEnableUnattended',
    summary: { pathname: urls.allocationSummary },
    items: [
      //无人值守，管理员：我的预约 待清洁保养 设备库存统计
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_MY_RESERVATION',
        component: 'Link',
        label: '我的预约',
        target: { pathname: route_urls.home_wx + '/myReservation' }
      },
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_TOBE_CLEAN_PM',
        component: 'Link',
        label: '待清洁保养',
        target: { pathname: route_urls.home_wx + '/disinfection' },
        summaryItem: 'returnCleaned'
      },
      {
        functions: 'ADJUST_UNATTENDED_MOBILE_STORAGE_COUNT',
        component: 'Link',
        label: '设备库存统计',
        target: { pathname: route_urls.home_wx + '/deviceInventory' }
      }
    ]
  },
  nurse: {
    module: 'nurse',
    title: '临床助手-日常检查',
    color: '#E3628A',
    expand: true,
    columns: 2,
    offline: true,
    summary: { pathname: urls.nurseSummary },
    items: [
      {
        functions: 'NURSESITE_MOBILE_TODAY_CHECK',
        offline: true,
        component: 'Link',
        label: '当日检查',
        target: { pathname: route_urls.wx.nurse, query: { group: 'today' } },
        summaryItem: 'dailyPlanned',
        warning: true
      },
      {
        functions: 'NURSESITE_MOBILE_DELAY_CHECK',
        offline: true,
        component: 'Link',
        label: '逾期检查',
        target: { pathname: route_urls.wx.nurse, query: { group: 'timeout' } },
        summaryItem: 'overdue',
        warning: true
      },
      {
        functions: 'NURSESITE_MOBILE_ERROR',
        offline: true,
        component: 'Link',
        label: '有问题',
        target: { pathname: route_urls.wx.nurse, query: { group: 'issue' } },
        summaryItem: 'problems',
        warning: true
      },
      {
        functions: 'NURSESITE_MOBILE_PASS',
        component: 'Link',
        label: '30天内通过',
        target: { pathname: route_urls.wx.nurse, query: { group: 'pass' } },
        summaryItem: 'passIn30days'
      },
      {
        functions: 'NURSESITE_MOBILE_NOT_TURNED_OFF',
        offline: true,
        component: 'Link',
        label: '未关机列表',
        target: { pathname: route_urls.wx.nursePowerCtrl, query: {} },
        summaryItem: 'powerOn'
      },
      {
        functions: 'NURSESITE_USING_MOBILE_RECORD',
        offline: true,
        component: 'Link',
        label: '使用登记记录',
        target: { pathname: route_urls.wx.assetOptRecords, query: {} },
        summaryItem: 'useRecord'
      }
    ]
  },
  dept_transaction_staff: {
    module: 'dispatch',
    title: '科务管理',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        label: '我的科务记录',
        functions: 'PERFORMANCE_MOBILE_DEPT_TRANSACTION_RECORD_MYSELF',
        target: { pathname: route_urls.home_wx + '/deptTransaction/log' }
      }
    ]
  },
  dept_transaction_head: {
    module: 'dispatch',
    title: '科务管理',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      {
        component: 'Link',
        functions: 'PERFORMANCE_MOBILE_DEPT_TRANSACTION_RECORD_ALL',
        label: '科务汇总',
        target: { pathname: route_urls.home_wx + '/deptTransaction/summary' }
      },
      {
        component: 'Link',
        label: '我的科务记录',
        functions: 'PERFORMANCE_MOBILE_DEPT_TRANSACTION_RECORD_MYSELF',
        target: { pathname: route_urls.home_wx + '/deptTransaction/log' }
      }
    ]
  },
  zkb_docs: items => {
    return {
      module: 'asset',
      title: '智康保文档',
      color: '#3573c5',
      expend: true,
      columns: 2,
      items: items.map(item => ({
        functions: 'KNOWLEDGE_ZKB_DOC_EDIT,KNOWLEDGE_ZKB_DOC_VIEW',
        component: 'Link',
        label: item.valueZh,
        target: { pathname: route_urls.wx.docs, query: { mainDirectory: item.valueZh } },
        count: { pathname: urls.zkbFiles, query: { mainDirectory: item.valueZh, pageSize: 1 } }
      }))
    }
  },
  // notAcctCards
  kpi: {
    module: 'kpi',
    title: '用户反馈',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [{ component: 'Link', label: '医院使用总结', target: { pathname: '/wx/report/kpi' } }]
  },
  // notAcctCards
  direport: {
    module: 'direport',
    title: '保养报告',
    color: '#3573c5',
    expend: true,
    columns: 1,
    items: [
      { component: 'Link', label: '新建微报', target: { pathname: '/wx/report/pm/edit' } },
      {
        component: 'Link',
        label: '修改暂存的临时微报',
        target: { pathname: '/wx/report/pm/list', query: { status: 0 } }
      },
      {
        component: 'Link',
        label: '查看所有完成的微报',
        target: { pathname: '/wx/report/pm/list', query: { status: 1 } }
      }
    ]
  },
  //notAcctCards
  connectivity: {
    module: 'connectivity',
    title: '基础互通',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [
      { component: 'Link', label: '我的克劳顿', target: { pathname: '/wx/connectivity/study/userScore' } },
      { component: 'Link', label: '绩效参考', target: { pathname: '/wx/connectivity/usability/index' } }
    ]
  },
  //notAcctCards
  qrCode: {
    module: 'qrCode',
    title: '一机一码',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [{ component: 'Link', label: '绑定二维码', target: { pathname: '/wx/ib/qrcode' } }]
  },
  //notAcctCards
  gamePlan: {
    module: 'gamePlan',
    title: '商务助手',
    color: '#3573c5',
    expend: true,
    columns: 2,
    items: [{ component: 'Link', label: 'Sales Game Plan', target: { pathname: '/wx/sales/plan' } }]
  },
  // 多级审核 start
  approvals: {
    module: 'approvals',
    title: '多级审批',
    color: '#5478d9',
    expend: true,
    column: 2,
    items: [
      {
        functions: 'MULTILEVEL_APPROVE_MOBILE_TOBE_APPROVE',
        component: 'Link',
        label: '待审批',
        target: { pathname: route_urls.wx.approvals, query: { type: 0 } },
        count: { pathname: urls.approvalsWaitApproval + '/?searchString=' + '' }
      },
      {
        functions: 'MULTILEVEL_APPROVE_MOBILE_MY_APPLY',
        component: 'Link',
        label: '我发起的',
        target: { pathname: route_urls.wx.approvals, query: { type: 1 } },
        count: { pathname: urls.approvalsMyRequest + '/?searchString=' + '' }
      },
      {
        functions: 'MULTILEVEL_APPROVE_MOBILE_MY_APPROVE',
        component: 'Link',
        label: '我审批的',
        target: { pathname: route_urls.wx.approvals, query: { type: 2 } },
        count: { pathname: urls.approvalsMyAudited + '/?searchString=' + '' }
      }
    ]
  },
  // 多级审核 end
  new_inventory: {
    module: 'maintain',
    title: '备件备品',
    columns: 2,
    items: [
      {
        functions: 'MOBILE_PARTS_STORAGE',
        component: 'Link',
        label: '备件库存',
        target: {
          pathname: route_config.backup.children['/list'].path,
          state: { ...route_config.backup.children['/list'] }
        }
      },
      {
        functions: 'MOBILE_PARTS_APPLY',
        component: 'Link',
        label: '备件申领',
        target: {
          pathname: route_config.backup.children['/apply'].path,
          state: { ...route_config.backup.children['/apply'] }
        }
      },
      {
        functions: 'MOBILE_PARTS_PURCHASE',
        component: 'Link',
        label: '备件申购',
        target: {
          pathname: route_config.backup.children['/subscribe'].path,
          state: { ...route_config.backup.children['/subscribe'] }
        }
      },
      {
        functions: 'MOBILE_PARTS_INBOUND',
        component: 'Link',
        label: '备件入库',
        target: {
          pathname: route_config.backup.children['/instock'].path,
          state: { ...route_config.backup.children['/instock'] }
        }
      },
      {
        functions: 'MOBILE_PARTS_OUTBOUND',
        component: 'Link',
        label: '备件出库',
        target: {
          pathname: route_config.backup.children['/outstock'].path,
          state: { ...route_config.backup.children['/outstock'] }
        }
      }
    ]
  },
  adverseEvents_reporter: {
    module: 'adverseReport',
    title: '不良事件上报',
    columns: 2,
    summary: { pathname: urls.adverseEventsReportSummary },
    items: [
      {
        functions: 'ADVERSE_MOBILE_REPORTER_MY',
        component: 'Link',
        label: '我的上报',
        summaryItem: 'myReportCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'reporterAllReports', role: 'reporter' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_REPORTER_TEMPORARY',
        component: 'Link',
        label: '暂存',
        summaryItem: 'stageCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'temporaryStorage', role: 'reporter' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_REPORTER_AUDITING',
        component: 'Link',
        label: '审核中',
        summaryItem: 'approvingCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'approvalReport', role: 'reporter' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_REPORTER_BACKED',
        component: 'Link',
        label: '已退回',
        summaryItem: 'returnedCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'returned', role: 'reporter' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_REPORTER_VOIDED',
        component: 'Link',
        label: '已作废',
        summaryItem: 'invalidCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'invalid', role: 'reporter' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_AUDIT_COMPLETED',
        component: 'Link',
        label: '审核完成',
        summaryItem: 'approveCompleteCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'afterApprovalPassed', role: 'reporter' }
        }
      }
    ]
  },
  adverseEvents_approver: {
    module: 'adverseApproval',
    title: '不良事件审核',
    columns: 2,
    summary: { pathname: urls.adverseEventsApproverSummary },
    items: [
      {
        functions: 'ADVERSE_MOBILE_APPROVER_ALL_ORDERS',
        component: 'Link',
        label: '全部工单',
        summaryItem: 'allCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'hospitalAll', role: 'approver' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_APPROVER_TOBE_APPROVE',
        component: 'Link',
        label: '待审核',
        summaryItem: 'waitApproveCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'pendingApproval', role: 'approver' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_APPROVER_APPROVING',
        component: 'Link',
        label: '审核中',
        summaryItem: 'approvingCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'underApproval', role: 'approver' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_APPROVER_BACKED',
        component: 'Link',
        label: '已退回',
        summaryItem: 'returnedCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'returned', role: 'approver' }
        }
      },
      {
        functions: 'ADVERSE_MOBILE_APPROVER_TOBE_APPEAR',
        component: 'Link',
        label: '待上报',
        summaryItem: 'waitReportCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'toBeReported', role: 'approver' }
        }
      }
    ]
  },
  adverseEvents_supplement: {
    module: 'adverseSupplement',
    title: '不良事件产品信息维护',
    columns: 2,
    summary: { pathname: urls.adverseEventsPurchaserSummary },
    items: [
      {
        functions: 'ADVERSE_MOBILE_SUPPLEMENT_TOBE_EDIT',
        component: 'Link',
        label: '待填写',
        summaryItem: 'waitFillCount',
        target: {
          pathname: route_urls.home_wx + '/adverseEvents',
          query: { eventsState: 'supplement', role: 'purchaser' }
        }
      }
    ]
  },
  departmentSeconded_director: {
    module: 'dispatch',
    title: '科室借调',
    columns: 2,
    workflowConfig: 'enableSiteToSiteAllocation',
    summary: { pathname: urls.secondedSummary },
    items: [
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_IN_BORROW_DETAIL',
        component: 'Link',
        label: '借入明细',
        summaryItem: 'borrowerDetail',
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'borrowDetail', role: 'DeptHead', type: 'borrow', status: 1 }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_OUT_BORROW_DETAIL',
        component: 'Link',
        label: '借出明细',
        summaryItem: 'lendDetail',
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'lendDetail', role: 'DeptHead', type: 'lend', status: 1 }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_OUT_BORROW_TOBE_CONFIRM',
        component: 'Link',
        label: '借出待确认',
        summaryItem: 'lender',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'lend', role: 'DeptHead' }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_RECOVER_TOBE_CONFIRM',
        component: 'Link',
        label: '收回待确认',
        summaryItem: 'returner',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'takeBack', role: 'DeptHead' }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_IN_BORROW_TOBE_CONFIRM',
        component: 'Link',
        label: '借入待确认',
        summaryItem: 'borrower',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'borrow', role: 'DeptHead' }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_RETURN_TOBE_CONFIRM',
        component: 'Link',
        label: '送还待确认',
        summaryItem: 'receiver',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'return', role: 'DeptHead' }
        }
      }
    ]
  },
  departmentSeconded_nursingAndClinical: {
    module: 'dispatch',
    title: '科室借调',
    columns: 2,
    workflowConfig: 'enableSiteToSiteAllocation',
    summary: { pathname: urls.secondedSummary },
    items: [
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_IN_BORROW_DETAIL',
        component: 'Link',
        label: '借入明细',
        summaryItem: 'borrowerDetail',
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'borrowDetail', role: 'ClinicalStaff_NursingStaff', type: 'borrow', status: 1 }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_OUT_BORROW_DETAIL',
        component: 'Link',
        label: '借出明细',
        summaryItem: 'lendDetail',
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'lendDetail', role: 'ClinicalStaff_NursingStaff', type: 'lend', status: 1 }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_IN_BORROW_TOBE_CONFIRM',
        component: 'Link',
        label: '借入待确认',
        summaryItem: 'borrower',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'borrow', role: 'ClinicalStaff_NursingStaff' }
        }
      },
      {
        functions: 'ADJUST_DEPTMENT_MOBILE_RETURN_TOBE_CONFIRM',
        component: 'Link',
        label: '送还待确认',
        summaryItem: 'receiver',
        warning: true,
        target: {
          pathname: route_urls.wx.secondedList,
          query: { eventsState: 'return', role: 'ClinicalStaff_NursingStaff' }
        }
      }
    ]
  },
  asset_scrap: {
    module: 'adverseReport',
    title: '设备报废',
    columns: 1,
    configItem: 'assetScrap',
    summary: { pathname: `${urls.assetScrap}/approve/summary` },
    items: [
      {
        functions: 'ASSET_INFO_SCRAP_MOBILE_TOBE_APPROVE_MYSELF',
        component: 'Link',
        label: '我的申请-待审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/retire',
          query: { status: 1 }
        },
        summaryItem: 'applicationAudit'
      },
      {
        functions: 'ASSET_INFO_SCRAP_MOBILE_APPROVE_MYSELF',
        component: 'Link',
        label: '我的申请-已审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/retire',
          query: { status: 2 }
        },
        summaryItem: 'applicationAudited'
      },
      {
        functions: 'ASSET_INFO_SCRAP_MOBILE_TOBE_APPROVE_ASSET_STAFF',
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/retire',
          query: { status: 3 }
        },
        summaryItem: 'audit'
      },
      {
        functions: 'ASSET_INFO_SCRAP_MOBILE_APPROVE_ASSET_STAFF',
        component: 'Link',
        label: '已审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/retire',
          query: { status: 4 }
        },
        summaryItem: 'audited'
      }
    ]
  },
  asset_transfer: {
    module: 'adverseReport',
    title: '设备转科',
    columns: 1,
    summary: { pathname: `${urls.departmentConvert}/approve/summary` },
    // features: 'ASSET_BATCH_MODIFY',
    items: [
      {
        functions: 'ASSET_BATCH_MODIFY_MOBILE_TRANSFER_DEPT_TOBE_APPROVE_MY_APPLY',
        component: 'Link',
        label: '我的申请-待审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/transfer',
          query: { status: 1 }
        },
        summaryItem: 'applicationAudit'
      },
      {
        functions: 'ASSET_BATCH_MODIFY_MOBILE_TRANSFER_DEPT_APPROVE_MY_APPLY',
        component: 'Link',
        label: '我的申请-已审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/transfer',
          query: { status: 2 }
        },
        summaryItem: 'applicationAudited'
      },
      {
        functions: 'ASSET_BATCH_MODIFY_MOBILE_TRANSFER_DEPT_TOBE_APPROVE',
        component: 'Link',
        label: '待审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/transfer',
          query: { status: 3 }
        },
        summaryItem: 'audit'
      },
      {
        functions: 'ASSET_BATCH_MODIFY_MOBILE_TRANSFER_DEPT_APPROVE',
        component: 'Link',
        label: '已审核',
        target: {
          pathname: route_urls.home_wx + '/approvals/transfer',
          query: { status: 4 }
        },
        summaryItem: 'audited'
      }
    ]
  },
  asset_secondment: {
    module: 'dispatch',
    title: '科室间调剂',
    columns: 2,
    summary: { pathname: `${urls.secondment}/summary` },
    items: [
      {
        functions: 'SECONDMENT_MOBILE_MULTI_DEPARTMENT_EQUIPMENT',
        component: 'Link',
        label: '科室间设备',
        target: {
          pathname: route_urls.home_wx + '/secondment/apply'
        },
        summaryItem: 'applyDevicesCount'
      },
      {
        functions: 'SECONDMENT_MOBILE_SINGLE_DEPARTMENT_EQUIPMENT',
        component: 'Link',
        label: '本科室设备',
        target: {
          pathname: route_urls.home_wx + '/secondment/manage'
        },
        summaryItem: 'manageDevicesCount'
      },
      {
        functions: 'SECONDMENT_MOBILE_DEPARTMENT_IN_BORROW_EQUIPMENT',
        component: 'Link',
        label: '借入管理',
        target: {
          pathname: route_urls.home_wx + '/secondment/borrow'
        },
        summaryItem: 'borrowCount'
      },
      {
        functions: 'SECONDMENT_MOBILE_DEPARTMENT_OUT_BORROW_EQUIPMENT',
        component: 'Link',
        label: '借出管理',
        target: {
          pathname: route_urls.home_wx + '/secondment/lend'
        },
        summaryItem: 'lentCount'
      }
    ]
  }
}

export default cards
