import urls from './urls'

const services = [
  // { key: 'roles', uri: urls.roles, query: { pageSize: 1000 }, default: { key: 'id', value: 'roleDesc' } },
  // { key: 'sites', uri: urls.sites, query: { pageSize: 1000, page: 0 }, default: { key: 'id', value: 'name' } },
  {
    uri: urls.i18n + '/batch',
    query: {
      msgTypes: [
        'workOrderType',
        'measuringStatus',
        'RepairSource',
        'repairReportSendPlace',
        'assetBorrowedStatus',
        'borrowedStatus',
        'equipmentDisposalMethod',
        'spare01',
        'spare02',
        'spare03',
        'spare04',
        'spare05',
        'approvalNodeStatus',
        'productSegment',
        'pmAttachments',
        'geDeviceType',
        'label',
        'assetClass',
        'approveStatus',
        'field_name',
        'installStatus',
        'capitalSource2',
        'isWarrantyNotice',
        'accountingCategory',
        'nurseSiteMaintainStatus',
        'repairForm',
        'deviceStatus',
        'assetStatus',
        'assetUsage',
        'assetGroup',
        'assetFunctionType',
        'assetFunctionGrade',
        'assetValueCategory',
        'capitalSource',
        'riskGrade',
        'riskGradeNew',
        'measureGrade',
        'emergencyEquipment',
        'importEquipment',
        'woSteps',
        'checklistType',
        'casePriority',
        'intExtType',
        'status',
        'repairType',
        'alertType',
        'depreciationMethodList',
        'measuringItemCategory',
        'partsType',
        'repairResult',
        'ruleLevel',
        'ruleLevelFour',
        'TableReportResult',
        'proprietaryClass',
        'mvsType',
        'mvsPriority',
        'zkbMainDirectory',
        'userPosition',
        'connectToInsite',
        'ibDeviceStatus',
        'transaction',
        'zkbPoSteps',
        'zkbPoStatus',
        'zkbPartOrderType',
        'zkbFePartOrder_steps',
        'zkbFePartOrder_status',
        'zkbFePartOrder_orderType',
        'zkbIntegralOrderSteps',
        'zkbIntegralOrderStatus',
        'zkbIntegralOrderType',
        'zkbPoReturnExternalPackaging',
        'zkbPoReturnInternalPackaging',
        'zkbPoReturnQualitySealType',
        'zkbPoReturnQualitySealStatus',
        'zkbPoReturnPartQuality',
        'zkbPoReturnPartQualityIssue',
        'tubewatchScope',
        'networkingStatus'
      ].join(',')
    },
    default: { key: 'msgKey', value: 'valueZh' }
  }
]

export default services
