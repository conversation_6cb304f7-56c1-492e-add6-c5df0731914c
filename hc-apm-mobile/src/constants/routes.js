import { lazy } from 'react'
import { util } from '@/constants'

const base = '/wx'
const route_urls = {
  home_wx: base,
  home_wx_ib: base + '/ib',
  login: base + '/login',
  register: base + '/user/register',
  registerbyParam: base + '/user/register/:assetId',
  registerbyParams: base + '/user/register/:assetId/:userId',
  registerResult: base + '/user/register-result',
  userPoints: base + '/user/points',
  msg: base + '/msg',
  others: base + '/*',

  device: 'device',
  request: 'request',
  workorder: 'workorder',
  inspection: 'inspection',
  pmorder: 'pmorder',
  pmOrderDetail: 'pmOrderDetail',
  inventory: 'inventory',
  alarm: 'alarm',
  monitor: 'monitor',
  coldHead: 'coldhead',
  apm: 'apm',
  user: 'user',
  edu: 'edu',
  docs: 'docs',

  wx: {
    login: 'login',
    user: 'user',
    docs: 'docs',

    device: 'device',
    device_uploader: 'device/uploader',
    device_one: 'device/:id',
    device_one_workorder: 'device/:id/workorder',

    alarm: 'alarm',
    coldHead: 'coldhead',
    monitor: 'monitor',
    nurse: 'nurse',
    nursePowerCtrl: 'nursePowerCtrl',
    assetOptRecords: 'assetOptRecords',

    inspection: 'inspection/:id',
    pmorder: 'pmorder/:id',
    pmOrderDetail: 'pmOrderDetail',
    inventory: 'inventory',
    inventroyDetail: '',
    apm: 'apm',
    srScanList: 'srScanList',
    srMsg: 'srMsg',
    reCreateWo: 'reCreateWo',
    createWo: 'createWo',
    workorder: 'workorder',
    repairList: 'repairList',
    woCost: 'woCost',
    newCreate: 'newCreate',
    telReport: 'telReport',

    scanQrCode: 'scanQrCode',
    updateQrCode: 'updateQrCode',
    viewQrCode: 'viewQrCode',
    uploadedQrCode: 'uploadedQrCode',

    health: 'report/health',
    health_ole: 'report/health/ole',
    health_wo: 'report/health/wo',
    zkb_list: 'zkblist',
    zkbspecified: 'zkbspecified',
    zkb_apply: 'zkbapply',
    approvals: 'approvals',

    secondedDetail: 'departmentSeconded/secondedDetail',
    secondedList: 'departmentSeconded/secondedList',
    secondment: 'secondment',
    chatbot: 'chatbot',
    QASharing: 'QASharing',
    downloadfile: 'downloadfile',
    faq: 'faq',
    csrSurvey: 'survey'
  }
}

const route_config = {
  backup: {
    path: '/wx/backup',
    children: {
      '/list': {
        path: '/wx/backup/list',
        name: '备件库存',
        search: '备件名称'
      },
      '/apply': {
        path: '/wx/backup/apply',
        name: '备件申领',
        search: '申领编号'
      },
      '/subscribe': {
        path: '/wx/backup/subscribe',
        name: '备件申购',
        search: '申购编号'
      },
      '/instock': {
        path: '/wx/backup/instock',
        name: '备件入库',
        search: '入库编号'
      },
      '/outstock': {
        path: '/wx/backup/outstock',
        name: '备件出库',
        search: '出库编号'
      }
    }
  }
}

// add basename
Object.keys(route_urls.wx).forEach(key => (route_urls.wx[key] = base + '/' + route_urls.wx[key]))

const routes = [
  { path: route_urls.login, component: lazy(() => import(/* webpackChunkName: "Login" */ '../containers/user/Login')) },
  /** Demo */
  {
    path: '/wx/test',
    component: lazy(() => import(/* webpackChunkName: "DeviceUS" */ '../containers/common/Test')),
    onEnter: byPass
  },
  {
    path: '/wx/demo/us',
    component: lazy(() => import(/* webpackChunkName: "DeviceUS" */ '../containers/device/DeviceUS')),
    onEnter: byPass
  },
  {
    path: '/wx/demo/list',
    component: lazy(() => import(/* webpackChunkName: "CommonList" */ '../containers/ib/CommonList')),
    onEnter: byPass
  },
  {
    path: '/wx/mrr',
    component: lazy(() => import(/* webpackChunkName: "MRR" */ '../containers/mrr')),
    onEnter: byPass
  },

  {
    path: route_urls.home_wx,
    component: lazy(() => import(/* webpackChunkName: "WX" */ '../containers/WX')),
    indexRoute: { component: lazy(() => import(/* webpackChunkName: "Console" */ '../containers/common/Console')) },
    childRoutes: [
      {
        path: '/wx/no-asset-wo',
        component: lazy(() => import(/* webpackChunkName: "NoAssetWo" */ '../containers/workorder/no-asset/NoAssetWo')),
      },
      {
        path: '/wx/no-asset-wo-list',
        component: lazy(() => import(/* webpackChunkName: "NoAssetWo" */ '../containers/workorder/no-asset/NoAssetWoList')),
      },
      {
        path: '/wx/showpadreport',
        component: lazy(() => import(/* webpackChunkName: "Warranty" */ '../containers/showpadreport/index'))
        // onEnter: byPass
      },
      {
        path: '/wx/warranty/:id',
        component: lazy(() => import(/* webpackChunkName: "Warranty" */ '../containers/warranty/common')),
        onEnter: byPass
      },
      {
        path: '/wx/expire/:id',
        component: lazy(() => import(/* webpackChunkName: "Warranty" */ '../containers/warranty/common')),
        onEnter: byPass
      },
      {
        path: route_urls.wx.downloadfile,
        component: lazy(() => import(/* webpackChunkName: "downloadFile" */ '../containers/downloadFile/index'))
        // onEnter: byPass
      },
      {
        path: '/wx/report/pm/view',
        component: lazy(() => import(/* webpackChunkName: "ReportPmView" */ '../containers/report/pm/view/view')),
        onEnter: byPass
      },
      {
        path: route_urls.register,
        component: lazy(() => import(/* webpackChunkName: "Register" */ '../containers/user/Register')),
        onEnter: byPass
      },
      {
        path: route_urls.registerbyParam,
        component: lazy(() => import(/* webpackChunkName: "Register" */ '../containers/user/Register')),
        onEnter: byPass
      },
      {
        path: route_urls.registerbyParams,
        component: lazy(() => import(/* webpackChunkName: "Register" */ '../containers/user/Register')),
        onEnter: byPass
      },
      {
        path: route_urls.registerResult,
        component: lazy(() => import(/* webpackChunkName: "Register" */ '../containers/user/RegisterResult')),
        onEnter: byPass
      },
      {
        path: base + '/login/qrcode',
        component: lazy(() => import(/* webpackChunkName: "Login" */ '../containers/user/QrCodeLogin'))
      },
      {
        path: base + '/login/vendor',
        component: lazy(() => import(/* webpackChunkName: "Login" */ '../containers/user/QrCodeVendorLogin')),
        onEnter: byPass
      },
      {
        path: base + '/user/approve',
        component: lazy(() => import(/* webpackChunkName: "Login" */ '../containers/user/Approve'))
      },
      /** Reports */
      //  { path: `${base}/ib`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/ib/Sbjk')),onEnter: byPass },
      //  { path: `${base}/list`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/ib/IbList')),onEnter: byPass },
      //  { path: `${base}/sbjk`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/ib/Sbjk')) },
      // 维修事件
      {
        path: `${base}/ib/list/repair`,
        component: lazy(() => import(/* webpackChunkName: "RepairList" */ '../containers/ib/RepairList'))
      },
      {
        path: `${base}/ib/device`,
        component: lazy(() => import(/* webpackChunkName: "IbList" */ '../containers/ib/IbList'))
      },
      //  报警记录(/ib/list/alert)
      {
        path: `${base}/ib/list/alert`,
        component: lazy(() => import(/* webpackChunkName: "AlertList" */ '../containers/ib/AlertList'))
      },
      {
        path: `${base}/ib/collection`,
        component: lazy(() => import(/* webpackChunkName: "Collection" */ '../containers/ib/Collection'))
      },
      {
        path: `${base}/ib/warranty`,
        component: lazy(() => import(/* webpackChunkName: "WarrantyList" */ '../containers/ib/WarrantyList'))
      },
      //  绑定二维码
      {
        path: `${base}/ib/qrcode`,
        component: lazy(() => import(/* webpackChunkName: "Qrcode" */ '../containers/ib/Qrcode'))
      },
      {
        path: `${base}/ib/qrcode/:codeId`,
        component: lazy(() => import(/* webpackChunkName: "Qrcode" */ '../containers/ib/Qrcode'))
      },
      //  活跃度查询
      //  { path: `${base}/report/kpi`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/ib/KpiReport')) },
      //  大放监控(/ib/charts/di)
      {
        path: `${base}/ib/charts/di`,
        component: lazy(() => import(/* webpackChunkName: "DiCharts" */ '../containers/ib/DiCharts'))
      },
      //  阈值调节(/ib/list/limit)
      {
        path: `${base}/ib/list/limit`,
        component: lazy(() => import(/* webpackChunkName: "LimitList" */ '../containers/ib/LimitList'))
      },
      //  保养微报
      {
        path: `${base}/ib/report/pm`,
        component: lazy(() => import(/* webpackChunkName: "PmReport" */ '../containers/ib/PmReport'))
      },
      {
        path: `${base}/ib/report/firstcall`,
        component: lazy(() =>
          import(/* webpackChunkName: "firstcall" */ '../containers/report/pm/firstcall/FirstcallList')
        )
      },
      {
        path: '/wx/report/pm/firstcall/view',
        component: lazy(() => import(/* webpackChunkName: "firstcallView" */ '../containers/report/pm/view/view'))
      },
      //  保养事件
      {
        path: `${base}/ib/list/pm`,
        component: lazy(() => import(/* webpackChunkName: "PmList" */ '../containers/ib/PmList'))
      },
      //  使用手册
      {
        path: `${base}/ib/guide`,
        component: lazy(() => import(/* webpackChunkName: "Guide" */ '../containers/ib/Guide'))
      },
      //  超声状况
      {
        path: `${base}/ib/charts/us`,
        component: lazy(() => import(/* webpackChunkName: "UsCharts" */ '../containers/ib/UsCharts'))
      },
      //  培训服务-签到
      {
        path: `${base}/training/signin`,
        component: lazy(() => import(/* webpackChunkName: "training/signin" */ '../containers/training/Signin')),
        onEnter: byPass
      },
      //  培训服务
      {
        path: `${base}/ib/training`,
        component: lazy(() => import(/* webpackChunkName: "Training" */ '../containers/ib/Training'))
      },
      //  getc验证码
      {
        path: `${base}/getc`,
        component: lazy(() => import(/* webpackChunkName: "GETC" */ '../containers/getcCode/getcCode.js'))
      },
      //  getc / pad qrCode login
      {
        path: `${base}/getc/login`,
        component: lazy(() =>
          import(/* webpackChunkName: "GetcQrCodeLogin" */ '../containers/getcCode/GetcQrCodeLogin.js')
        )
      },
      //帮助中心
      {
        path: route_urls.wx.faq,
        component: lazy(() => import(/* webpackChunkName: "ReportPmView" */ '../containers/FaqCenter/FaqCenter.js'))
      },
      // 年报
      {
        path: '/wx/report/annual',
        component: lazy(() => import(/* webpackChunkName: "annual" */ '../containers/report/annual/annual.js')),
        onEnter: byPass
      },
      // 设备简报（季报）
      {
        path: '/wx/report/quarterly/:hospitalUid',
        component: lazy(() =>
          import(/* webpackChunkName: "quarterly" */ '../containers/report/quarterlyReport/quarterly.js')
        )
      },
      //  设备月报(/ib/report/iwic)
      {
        path: `${base}/ib/report/iwic`,
        component: lazy(() => import(/* webpackChunkName: "IwicReport" */ '../containers/ib/IwicReportOld'))
      },
      {
        path: `${base}/ib/report/all`,
        component: lazy(() => import(/* webpackChunkName: "IwicReportAll" */ '../containers/ib/IwicReport'))
      },
      //短码下载跳转
      {
        path: `${base}/ehr/download/:id`,
        component: lazy(() => import(/* webpackChunkName: "IwicReportAll" */ '../containers/report/download'))
      },
      //  关爱行动(/ib/report/owoc)
      {
        path: `${base}/ib/report/owoc`,
        component: lazy(() => import(/* webpackChunkName: "OwocReport" */ '../containers/ib/OwocReport'))
      },
      // 查找gelist (FE)
      {
        path: `${base}/ib/list/ge`,
        component: lazy(() => import(/* webpackChunkName: "GeList" */ '../containers/ib/GeList'))
      },
      {
        path: `${base}/ib/pdfview`,
        component: lazy(() => import(/* webpackChunkName: "PdfView" */ '../containers/ib/PdfView')),
        onEnter: byPassReg
      },
      {
        path: `${base}/ib/tubewatch`,
        component: lazy(() => import(/* webpackChunkName: "PdfView" */ '../containers/ib/TubewatchList'))
      },
      // 重要客户
      {
        path: '/wx/vipcustomer',
        component: lazy(() => import(/* webpackChunkName: "VipCustomer" */ '../containers/vip/VipCustomer'))
      },
      {
        path: '/wx/vipsearch',
        component: lazy(() => import(/* webpackChunkName: "VipSearch" */ '../containers/vip/VipSearch'))
      },
      /** IB Reports */
      {
        path: route_urls.wx.health,
        component: lazy(() => import(/* webpackChunkName: "DeviceHealth" */ '../containers/report/health')),
        indexRoute: {
          component: lazy(() => import(/* webpackChunkName: "US Report" */ '../containers/report/health/us'))
        },
        childRoutes: [
          {
            path: route_urls.wx.health_ole,
            component: lazy(() => import(/* webpackChunkName: "DeviceHealthOle" */ '../containers/report/health/ole'))
          },
          {
            path: route_urls.wx.health_wo,
            component: lazy(() =>
              import(/* webpackChunkName: "DeviceHealthWorkorder" */ '../containers/report/health/workorder')
            )
          }
        ]
      },
      /** zkb */
      {
        path: '/wx/zkbnewapply',
        component: lazy(() => import(/* webpackChunkName: "ZKBNewList" */ '../containers/zkb/zkbNewList/ZkbNewApply'))
      },
      { path: '/wx/zkb', component: lazy(() => import(/* webpackChunkName: "ZKB" */ '../containers/zkb/index')) },
      {
        path: '/wx/zkbapply',
        component: lazy(() => import(/* webpackChunkName: "ZKBApply" */ '../containers/zkb/zkbApply/ZkbApply'))
      },
      {
        path: '/wx/zkblist',
        component: lazy(() => import(/* webpackChunkName: "ZKBList" */ '../containers/zkb/zkbList/ZkbList'))
      },
      {
        path: '/wx/zkbspecified',
        component: lazy(() =>
          import(/* webpackChunkName: "zkbspecified" */ '../containers/zkb/zkbspecified/zkbspecified')
        )
      },
      {
        path: '/wx/zkbfepartorder',
        component: lazy(() => import(/* webpackChunkName: "ZKB" */ '../containers/zkb/ZkbFro'))
      },
      {
        path: '/wx/zkb/history',
        component: lazy(() => import(/* webpackChunkName: "ZKBHistory" */ '../containers/zkb/ZkbHistory'))
      },

      {
        path: '/wx/report/kpi',
        component: lazy(() => import(/* webpackChunkName: "ReportKpi" */ '../containers/report/kpi'))
      },
      {
        path: '/wx/report/pm/edit',
        component: lazy(() => import(/* webpackChunkName: "ReportPmEdit" */ '../containers/report/pm/edit'))
      },
      {
        path: '/wx/report/pm/list',
        component: lazy(() => import(/* webpackChunkName: "ReportPmList" */ '../containers/report/pm/list'))
      },

      /** Console */
      {
        path: 'console',
        component: lazy(() => import(/* webpackChunkName: "Console" */ '../containers/common/Console'))
      },
      {
        path: 'tenants/:type',
        component: lazy(() => import(/* webpackChunkName: "TenantList" */ '../containers/common/TenantList'))
      },

      /** Alarm */
      {
        path: route_urls.coldHead,
        component: lazy(() => import(/* webpackChunkName: "ColdHeadList" */ '../containers/alarm/ColdHeadList'))
      },
      {
        path: route_urls.alarm,
        component: lazy(() => import(/* webpackChunkName: "EEList" */ '../containers/alarm/EEList'))
      },
      {
        path: route_urls.alarm + '/:assetId/:type',
        component: lazy(() => import(/* webpackChunkName: "EEDetail" */ '../containers/alarm/EEDetail'))
      },

      /** Dynamic Schema (form) */
      {
        path: 'form/viewer',
        component: lazy(() => import(/* webpackChunkName: "FormViewer" */ '../components/FormViewer'))
      },
      {
        path: 'form/preview',
        component: lazy(() => import(/* webpackChunkName: "Preview" */ '../containers/form/Preview')),
        onEnter: byPass
      },
      {
        path: 'form/pmorder/:pmId',
        component: lazy(() => import(/* webpackChunkName: "PmOrder" */ '../containers/form/PmOrder')),
        onEnter: byPass,
        childRoutes: [
          {
            path: 'form/:name',
            onEnter: byPass,
            component: lazy(() => import(/* webpackChunkName: "PmOrderForm" */ '../containers/form/PmOrderForm'))
          }
        ]
      },
      {
        path: 'form/pmorder/:pmId/asset/:assetId',
        component: lazy(() => import(/* webpackChunkName: "PmOrder" */ '../containers/form/PmOrder')),
        onEnter: byPass,
        childRoutes: [
          {
            path: 'form/:name',
            onEnter: byPass,
            component: lazy(() => import(/* webpackChunkName: "PmOrderForm" */ '../containers/form/PmOrderForm'))
          }
        ]
      },

      /** Nurse Site */
      {
        path: route_urls.wx.nurse,
        component: lazy(() => import(/* webpackChunkName: "NurseAssetList" */ '../containers/nurse/NurseAssetList'))
      },
      {
        path: route_urls.wx.assetOptRecords,
        component: lazy(() => import(/* webpackChunkName: "AssetOptRecords" */ `../containers/nurse/AssetOptRecords`))
      },
      {
        path: route_urls.wx.nursePowerCtrl,
        component: lazy(() =>
          import(
            /* webpackChunkName: "AssetPowerOff" */ `../containers/nurse/${
              util.isNursePower() ? 'AssetPowerOff' : 'AssetPowerOffNew'
            }`
          )
        )
      },
      {
        path: route_urls.wx.nursePowerCtrl + '/:assetUid',
        component: lazy(() =>
          import(
            /* webpackChunkName: "AssetPowerOff" */ `../containers/nurse/${
              util.isNursePower() ? 'AssetPowerOff' : 'AssetPowerOffNew'
            }`
          )
        )
      },
      /** Inspection */
      {
        path: '/wx/measuring',
        component: lazy(() => import(/* webpackChunkName: "InspectionCreate" */ '../containers/device/MeasuringDetail'))
      },

      /** Inspection */
      {
        path: '/wx/inspection/create/:uid',
        component: lazy(() =>
          import(/* webpackChunkName: "InspectionCreate" */ '../containers/inspection/InspectionCreate')
        )
      },
      {
        path: route_urls.inspection + '/:type',
        component: lazy(() =>
          import(/* webpackChunkName: "InspectionList" */ '../containers/inspection/InspectionList')
        )
      },
      {
        path: route_urls.inspection + '/:type/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "InspectionDetail" */ '../containers/inspection/InspectionDetail')
        )
      },

      /** Inventory */
      // { path: route_urls.inventory + '/:orderId/org/:clinicalDeptUID', component: Container.Inventory.InventoryMain },
      {
        path: route_urls.inventory,
        component: lazy(() => import(/* webpackChunkName: "InventoryMain" */ '../containers/inventory/InventoryMain'))
      },
      {
        path: route_urls.inventory + '/inventoryDetail/:orderId/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "UndocumentedDetail" */ '../containers/inventory/UndocumentedDetail')
        )
      },
      {
        path: route_urls.inventory + '/exsit/:inventoryStatus',
        component: lazy(() => import(/* webpackChunkName: "InventoryList" */ '../containers/inventory/InventoryList'))
      },
      {
        path: route_urls.inventory + '/unexist',
        component: lazy(() =>
          import(/* webpackChunkName: "UndocumentedList" */ '../containers/inventory/UndocumentedList')
        )
      },
      {
        path: route_urls.inventory + '/:orderId/backlog',
        component: lazy(() =>
          import(/* webpackChunkName: "UndocumentedDetail" */ '../containers/inventory/UndocumentedDetail')
        )
      },
      {
        path: route_urls.inventory + '/scan',
        component: lazy(() =>
          import(/* webpackChunkName: "ScanToInventory" */ '../containers/inventory/ScanToInventory')
        )
      },
      {
        path: route_urls.inventory + '/newOrder',
        component: lazy(() =>
          import(/* webpackChunkName: "NewInventoryOrder" */ '../containers/inventory/NewInventoryOrder')
        )
      },
      {
        path: route_urls.inventory + '/closeOrder/:orderId',
        component: lazy(() =>
          import(/* webpackChunkName: "CloseInventoryOrder" */ '../containers/inventory/CloseInventoryOrder')
        )
      },
      {
        path: route_urls.inventory + '/order',
        component: lazy(() => import(/* webpackChunkName: "InventoryOrder" */ '../containers/inventory/InventoryOrder'))
      },
      {
        path: route_urls.inventory + '/checklist',
        component: lazy(() =>
          import(/* webpackChunkName: "InventoryCheckList" */ '../containers/inventory/InventoryCheckList')
        )
      },
      {
        path: route_urls.inventory + '/orderList',
        component: lazy(() =>
          import(/* webpackChunkName: "InventoryOrderList" */ '../containers/inventory/InventoryOrderList')
        )
      },
      {
        path: route_urls.inventory + '/group/:orderId',
        component: lazy(() =>
          import(/* webpackChunkName: "InventoryGroupList" */ '../containers/inventory/InventoryGroupList')
        )
      },
      {
        path: route_urls.inventory + '/openOrderList',
        component: lazy(() => import(/* webpackChunkName: "OpenOrderList" */ '../containers/inventory/OpenOrderList'))
      },

      /** Service Request and Workorders */
      {
        path: 'sr-submit',
        component: lazy(() =>
          import(/* webpackChunkName: "ServiceRequest" */ '../containers/workorder/ServiceRequest')
        ),
        onEnter: byPass
      },
      {
        path: 'request',
        component: lazy(() =>
          import(/* webpackChunkName: "ServiceRequestList" */ '../containers/workorder/ServiceRequestList')
        )
      },
      {
        path: 'consoleSrList',
        component: lazy(() => import(/* webpackChunkName: "ConsoleSRList" */ '../containers/workorder/ConsoleSRList'))
      },
      {
        path: 'consoleWoList',
        component: lazy(() => import(/* webpackChunkName: "ConsoleWOList" */ '../containers/workorder/ConsoleWOList'))
      },
      {
        path: 'unAcceptList',
        component: lazy(() => import(/* webpackChunkName: "UnAcceptList" */ '../containers/workorder/UnAcceptList'))
      },
      {
        path: 'workorder',
        component: lazy(() => import(/* webpackChunkName: "WorkorderList" */ '../containers/workorder/WorkorderList'))
      },
      {
        path: 'repairList',
        component: lazy(() => import(/* webpackChunkName: "RepairList" */ '../containers/workorder/RepairList'))
      },
      {
        path: 'deptList',
        component: lazy(() => import(/* webpackChunkName: "DeptList" */ '../containers/workorder/DeptList'))
      },
      {
        path: 'newCreate',
        component: lazy(() => import(/* webpackChunkName: "NewCreate" */ '../containers/workorder/wosteps/NewCreate'))
      },
      {
        path: 'workorder/:id',
        component: lazy(() => import(/* webpackChunkName: "WoDetail" */ '../containers/workorder/wosteps/WoDetail'))
      },
      {
        path: 'workOrder/:woId/workOrderSteps',
        component: lazy(() => import(/* webpackChunkName: "StepList" */ '../containers/workorder/wosteps/StepList'))
      },
      {
        path: route_urls.wx.srScanList,
        component: lazy(() =>
          import(/* webpackChunkName: "ScanReportList" */ '../containers/workorder/wosteps/ScanReportList')
        ),
        onEnter: byPass
      },
      {
        path: route_urls.wx.srMsg + '/:id',
        component: lazy(() => import(/* webpackChunkName: "SrMsg" */ '../containers/workorder/SrMsg')),
        onEnter: byPass
      },
      {
        path: route_urls.wx.createWo,
        component: lazy(() => import(/* webpackChunkName: "CreateWo" */ '../containers/workorder/wosteps/CreateWo')),
        onEnter: byPass
      },
      {
        path: route_urls.wx.woCost + '/:id',
        component: lazy(() => import(/* webpackChunkName: "WoCost" */ '../containers/workorder/wosteps/WoCost'))
      },
      {
        path: route_urls.wx.telReport,
        component: lazy(() => import(/* webpackChunkName: "TelReport" */ '../containers/workorder/wosteps/TelReport')),
        onEnter: byPass
      },
      {
        path: 'workorder/external/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "ExternalWoDetail" */ '../containers/workorder/wosteps/ExternalWoDetail')
        )
      },
      {
        path: 'partsDeptAudit',
        component: lazy(() => import(/* webpackChunkName: "PartsDeptAudit" */ '../containers/workorder/PartsDeptAudit'))
      },
      {
        path: 'coreGroupReviewProgress',
        component: lazy(() =>
          import(/* webpackChunkName: "coreGroupReviewProgress" */ '../containers/workorder/CoreGroupReviewProgress')
        )
      },

      /** PM orders */
      {
        path: 'createPmOrder/:assetId',
        component: lazy(() => import(/* webpackChunkName: "CreatePmOrder" */ '../containers/pmorder/CreatePmOrder'))
      },
      {
        path: 'unAcceptPmList',
        component: lazy(() =>
          import(/* webpackChunkName: "UnacceptPmOrderList" */ '../containers/pmorder/UnAcceptPmOrderList')
        )
      },
      {
        path: 'consolePmList',
        component: lazy(() =>
          import(/* webpackChunkName: "ConsolePmOrderList" */ '../containers/pmorder/ConsolePmOrderList')
        )
      },
      {
        path: 'pmOrder/:id',
        component: lazy(() => import(/* webpackChunkName: "PmOrderDetail" */ '../containers/pmorder/PmOrderDetail'))
      },
      {
        path: 'pmOrder/:id/steps',
        component: lazy(() => import(/* webpackChunkName: "StepList" */ '../containers/pmorder/StepList'))
      },
      {
        path: 'pmOrder/:id/price',
        component: lazy(() => import(/* webpackChunkName: "PartDetail" */ '../containers/pmorder/PartDetail'))
      },
      {
        path: 'pmOrder/:id/external',
        component: lazy(() =>
          import(/* webpackChunkName: "ExternalPmDetail" */ '../containers/pmorder/ExternalPmDetail')
        )
      },

      /** Qrcode */
      {
        path: route_urls.wx.updateQrCode,
        component: lazy(() => import(/* webpackChunkName: "QRCodeUpdate" */ '../containers/qrcode/QRCodeUpdate')),
        onEnter: byPass
      },
      {
        path: route_urls.wx.viewQrCode,
        component: lazy(() => import(/* webpackChunkName: "QRCodeViewer" */ '../containers/qrcode/QRCodeViewer')),
        onEnter: byPass
      },
      {
        path: route_urls.wx.uploadedQrCode,
        component: lazy(() => import(/* webpackChunkName: "QRCodeUploaded" */ '../containers/qrcode/QRCodeUploaded')),
        onEnter: byPass
      },

      /** Device */
      {
        path: route_urls.device,
        title: '设备列表',
        component: lazy(() =>
          util.isIbSite()
            ? import(/* webpackChunkName: "IbList" */ '../containers/ib/IbList')
            : import(/* webpackChunkName: "DeviceList" */ '../containers/device/DeviceList')
        )
      },
      {
        path: 'device/performance/single',
        title: '单台设备绩效分析',
        component: lazy(() => import(/* webpackChunkName: "DeviceList" */ '../containers/device/DeviceList'))
      },
      {
        path: 'device/retire',
        component: lazy(() => import(/* webpackChunkName: "DeviceOcr" */ '../containers/device/DeviceRetire'))
      },
      {
        path: 'device/retire/:uid',
        component: lazy(() => import(/* webpackChunkName: "DeviceOcr" */ '../containers/device/DeviceRetire'))
      },
      {
        path: 'device/transfer',
        component: lazy(() => import(/* webpackChunkName: "DeviceOcr" */ '../containers/device/DeviceTransfer'))
      },
      {
        path: 'device/transfer/:uid',
        component: lazy(() => import(/* webpackChunkName: "DeviceOcr" */ '../containers/device/DeviceTransfer'))
      },
      {
        path: 'device/ocr',
        component: lazy(() => import(/* webpackChunkName: "DeviceOcr" */ '../containers/device/DeviceOcr'))
      },
      {
        path: 'device/create',
        component: lazy(() => import(/* webpackChunkName: "DeviceCreate" */ '../containers/device/DeviceCreate'))
      },
      {
        path: 'device/createIb',
        component: lazy(() => import(/* webpackChunkName: "DeviceCreateIB" */ '../containers/device/DeviceCreateIB'))
      },
      {
        path: 'deviceMvsList',
        component: lazy(() => import(/* webpackChunkName: "DeviceMVSList" */ '../containers/device/DeviceMVSList'))
      },
      {
        path: route_urls.device + '/:id',
        component: lazy(() => import(/* webpackChunkName: "Device" */ '../containers/device/Device')),
        indexRoute: {
          component: lazy(() => import(/* webpackChunkName: "DeviceProps" */ '../containers/device/DeviceProps'))
        },
        childRoutes: [
          {
            path: 'props',
            component: lazy(() => import(/* webpackChunkName: "DeviceProps" */ '../containers/device/DeviceProps'))
          },
          {
            path: 'edit',
            component: lazy(() => import(/* webpackChunkName: "DeviceEdit" */ '../containers/device/DeviceEdit'))
          },
          {
            path: route_urls.request,
            component: lazy(() =>
              import(/* webpackChunkName: "ScanReportList" */ '../containers/workorder/wosteps/ScanReportList')
            )
          },
          {
            path: route_urls.alarm,
            component: lazy(() => import(/* webpackChunkName: "DeviceEEList" */ '../containers/device/DeviceEEList'))
          },
          {
            path: route_urls.monitor,
            component: lazy(() => import(/* webpackChunkName: "DeviceMonitor" */ '../containers/device/DeviceMonitor'))
          },
          {
            path: 'special',
            component: lazy(() => import(/* webpackChunkName: "DeviceSpecial" */ '../containers/device/DeviceSpecial'))
          },
          {
            path: 'events',
            component: lazy(() =>
              util.isIbSite()
                ? import(/* webpackChunkName: "Ibevent" */ '../containers/device/IbEvent')
                : import(/* webpackChunkName: "DeviceList" */ '../containers/device/DeviceRecords')
            )
          },
          {
            path: 'ole',
            component: lazy(() =>
              import(/* webpackChunkName: "DeviceHealthReport" */ '../containers/device/DeviceHealthReport')
            )
          },
          {
            path: 'us',
            component: lazy(() => import(/* webpackChunkName: "DeviceUS" */ '../containers/device/DeviceUS'))
          }
        ]
      },

      {
        path: route_urls.docs,
        component: lazy(() => import(/* webpackChunkName: "Document" */ '../containers/common/Document'))
      },
      { path: route_urls.edu, component: lazy(() => import(/* webpackChunkName: "Edu" */ '../containers/common/Edu')) },
      {
        path: route_urls.user,
        component: lazy(() => import(/* webpackChunkName: "UserSignIn" */ '../containers/user/UserSignIn'))
      },
      {
        path: '/wx/user/mobile',
        component: lazy(() => import(/* webpackChunkName: "UserMobileUpdate" */ '../containers/user/MobileUpdate'))
      },
      {
        path: route_urls.wx.scanQrCode,
        component: lazy(() => import(/* webpackChunkName: "ScanQrCode" */ '../containers/common/ScanQrCode')),
        onEnter: byPass
      },
      {
        path: '/wx/scanqrcode/:qrCode',
        component: lazy(() => import(/* webpackChunkName: "ScanQrCode" */ '../containers/common/ScanQrCode')),
        onEnter: byPass
      },
      {
        path: '/wx/scanqrcode/:qrCode/:assetId',
        component: lazy(() => import(/* webpackChunkName: "ScanQrCode" */ '../containers/common/ScanQrCode')),
        onEnter: byPass
      },

      /** Asset Dispatch Center */
      {
        path: 'requestList',
        component: lazy(() =>
          import(/* webpackChunkName: "DispatchOrderList" */ '../containers/assetDispatch/RequestList')
        )
      },
      {
        path: 'reservation',
        component: lazy(() =>
          import(/* webpackChunkName: "deviceOrder" */ '../containers/assetDispatch/DeviceReservation/Reservation')
        )
      },
      {
        path: 'addReservation',
        component: lazy(() =>
          import(/* webpackChunkName: "deviceOrder" */ '../containers/assetDispatch/DeviceReservation/AdditionalRecord')
        )
      },
      {
        path: route_urls.wx.csrSurvey,
        component: lazy(() => import(/* webpackChunkName: "csrSurvey" */ '../containers/CSRsurvey'))
      },
      {
        path: route_urls.wx.csrSurvey + '/list',
        component: lazy(() => import(/* webpackChunkName: "csrSurvey" */ '../containers/CSRsurvey/list'))
      },
      {
        path: 'disinfection',
        component: lazy(() =>
          import(/* webpackChunkName: "disinfection" */ '../containers/assetDispatch/DeviceReservation/Disinfection')
        )
      },
      {
        path: 'myReservation',
        component: lazy(() =>
          import(/* webpackChunkName: "myReservation" */ '../containers/assetDispatch/DeviceReservation/MyReservation')
        )
      },
      {
        path: 'rentRequest',
        component: lazy(() => import(/* webpackChunkName: "RentRequest" */ '../containers/assetDispatch/RentRequest'))
      },
      {
        path: 'rentRequest/:id',
        component: lazy(() => import(/* webpackChunkName: "RentRequest" */ '../containers/assetDispatch/RentRequest'))
      },
      {
        path: 'rentRequestInfo/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "RentRequestInfo" */ '../containers/assetDispatch/RentRequest/RentRequestInfo')
        )
      },
      {
        path: 'returnRequest',
        component: lazy(() =>
          import(/* webpackChunkName: "ReturnRequest" */ '../containers/assetDispatch/ReturnRequest')
        )
      },
      {
        path: 'returnRequest/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "ReturnRequest" */ '../containers/assetDispatch/ReturnRequest')
        )
      },
      {
        path: 'returnRequestInfo/:id',
        component: lazy(() =>
          import(
            /* webpackChunkName: "ReturnRequestInfo" */ '../containers/assetDispatch/ReturnRequest/ReturnRequestInfo'
          )
        )
      },
      {
        path: 'assetDetail/:id',
        component: lazy(() => import(/* webpackChunkName: "AssetDetail" */ '../containers/assetDispatch/AssetDetail'))
      },
      {
        path: 'rentedDevices',
        component: lazy(() =>
          import(/* webpackChunkName: "RentedDevices" */ '../containers/assetDispatch/RentedDevices')
        )
      },
      {
        path: 'deviceInventory',
        component: lazy(() =>
          import(/* webpackChunkName: "DeviceInventory" */ '../containers/assetDispatch/DeviceInventory')
        )
      },
      {
        path: 'forceReturn',
        component: lazy(() =>
          import(/* webpackChunkName: "DeviceInventory" */ '../containers/assetDispatch/ForceReturn')
        )
      },

      /* APM Feedback Page */
      {
        path: 'feedback',
        component: lazy(() => import(/* webpackChunkName: "IssueList" */ '../containers/feedback/IssueList'))
      },
      {
        path: 'feedback/create',
        component: lazy(() => import(/* webpackChunkName: "FeedbackCreate" */ '../containers/feedback/FeedbackCreate'))
      },
      {
        path: 'feedback/handbook',
        component: lazy(() =>
          import(/* webpackChunkName: "HandbookFeedback" */ '../containers/feedback/HandbookFeedback')
        )
      },
      {
        path: 'feedback/article',
        component: lazy(() =>
          import(/* webpackChunkName: "ArticleFeedback" */ '../containers/feedback/ArticleFeedback')
        ),
        onEnter: byPass
      },
      {
        path: 'feedback/:id',
        component: lazy(() => import(/* webpackChunkName: "Feedback" */ '../containers/feedback/Feedback'))
      },

      /* Department Transaction */
      {
        path: 'deptTransaction/log/create',
        component: lazy(() =>
          import(/* webpackChunkName: "DeptTransaction" */ '../containers/deptTransaction/DeptTransactionLogCreate')
        )
      },
      {
        path: 'deptTransaction/log/:id',
        component: lazy(() =>
          import(/* webpackChunkName: "DeptTransaction" */ '../containers/deptTransaction/DeptTransactionLogEdit')
        )
      },
      {
        path: 'deptTransaction/log',
        component: lazy(() =>
          import(/* webpackChunkName: "DeptTransaction" */ '../containers/deptTransaction/DeptTransactionLogList')
        )
      },
      {
        path: 'deptTransaction/summary',
        component: lazy(() =>
          import(/* webpackChunkName: "DeptTransaction" */ '../containers/deptTransaction/DeptTransactionSummary')
        )
      },

      /* 预约培训 */
      {
        path: 'course/:id/reserve',
        component: lazy(() => import(/* webpackChunkName: "CourseReserve" */ '../containers/course/poster'))
      },
      {
        path: 'course/:id/register',
        component: lazy(() => import(/* webpackChunkName: "CourseRegister" */ '../containers/course/reserve'))
      },
      {
        path: 'course/:id/result',
        component: lazy(() => import(/* webpackChunkName: "CourseResult" */ '../containers/course/result'))
      },
      {
        path: 'course/:id/detail',
        component: lazy(() => import(/* webpackChunkName: "CourseDetail" */ '../containers/course/detail'))
      },
      {
        path: 'course',
        component: lazy(() => import(/* webpackChunkName: "CourseMine" */ '../containers/course/mine'))
      },

      /* 基础互通  养、修、学、用*/
      // { path: `${base}/connectivity/maintain`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/connectivity/maintain/index')),onEnter: byPass },
      // { path: `${base}/connectivity/repair`, component:  lazy(() => import(/* webpackChunkName: "" */'../containers/connectivity/repair/index')),onEnter: byPass },
      {
        path: `${base}/connectivity/study`,
        childRoutes: [
          {
            path: `${base}/connectivity/study/studyIndex`,
            component: lazy(() =>
              import(/* webpackChunkName: "userScore" */ '../containers/connectivity/study/studyIndex')
            )
          },
          {
            path: `${base}/connectivity/study/userScore`,
            component: lazy(() =>
              import(/* webpackChunkName: "userScore" */ '../containers/connectivity/study/userScore')
            )
          },
          {
            path: `${base}/connectivity/study/scoreInfo`,
            component: lazy(() =>
              import(/* webpackChunkName: "scoreInfo" */ '../containers/connectivity/study/scoreInfo')
            )
          },
          {
            path: `${base}/connectivity/study/scoreList`,
            component: lazy(() =>
              import(/* webpackChunkName: "scoreList" */ '../containers/connectivity/study/scoreList')
            )
          },
          {
            path: `${base}/connectivity/study/scoreUpdata`,
            component: lazy(() =>
              import(/* webpackChunkName: "scoreUpdata" */ '../containers/connectivity/study/scoreUpdata')
            )
          },
          {
            path: `${base}/connectivity/study/studyStatus`,
            component: lazy(() =>
              import(/* webpackChunkName: "studyStatus" */ '../containers/connectivity/study/studyStatus')
            )
          },
          {
            path: `${base}/connectivity/study/search`,
            component: lazy(() => import(/* webpackChunkName: "userScore" */ '../containers/connectivity/study/search'))
          },
          {
            path: `${base}/connectivity/study/searcCont`,
            component: lazy(() =>
              import(/* webpackChunkName: "userScore" */ '../containers/connectivity/study/searcCont')
            )
          },
          {
            path: `${base}/connectivity/study/channelActicle`,
            component: lazy(() =>
              import(/* webpackChunkName: "userScore" */ '../containers/connectivity/study/channelActicle')
            )
          }
        ]
      },
      {
        path: `${base}/connectivity/usability`,
        childRoutes: [
          {
            path: `${base}/connectivity/usability/index`,
            component: lazy(() =>
              import(/* webpackChunkName: "usability" */ '../containers/connectivity/usability/index')
            )
          },
          {
            path: `${base}/connectivity/usability/item`,
            component: lazy(() =>
              import(/* webpackChunkName: "usabilityItem" */ '../containers/connectivity/usability/dataDetail')
            )
          }
        ]
      },
      // FE
      {
        path: `${base}/fe/invite`,
        component: lazy(() => import(/* webpackChunkName: "" */ '../containers/FE/invite'))
      },
      // FE-report
      {
        path: `${base}/fe/report`,
        component: lazy(() => import(/* webpackChunkName: "" */ '../containers/FE/report'))
      },
      /* Sales game plan */
      {
        path: `${base}/sales/plan/channel`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplansalesPlanChannel" */ '../containers/gamePlan/channel')
        )
      },
      {
        path: `${base}/sales/plan`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplansalesPlan" */ '../containers/gamePlan/salesPlanShell')
        )
      },
      {
        path: `${base}/sales/plan/list`,
        component: lazy(() => import(/* webpackChunkName: "salesplanList" */ '../containers/gamePlan/list'))
      },
      {
        path: `${base}/sales/plan/detail/:id`,
        component: lazy(() => import(/* webpackChunkName: "salesplanDetail" */ '../containers/gamePlan/detail'))
      },
      {
        path: `${base}/sales/plan/coil/:id`,
        component: lazy(() => import(/* webpackChunkName: "salesplancoil" */ '../containers/gamePlan/coil'))
      },
      {
        path: `${base}/sales/plan/probe/:id`,
        component: lazy(() => import(/* webpackChunkName: "salesplanprobe" */ '../containers/gamePlan/probe'))
      },
      {
        path: `${base}/sales/plan/spare-parts`,
        component: lazy(() => import(/* webpackChunkName: "salesplanSpareParts" */ '../containers/gamePlan/spareParts'))
      },
      {
        path: `${base}/sales/plan/us-option-pic`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplanUsOptionPic" */ '../containers/gamePlan/usOptionPic')
        )
      },
      {
        path: `${base}/sales/plan/sv/hospital`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplanSvHospitals" */ '../containers/gamePlan/sv/hospitals')
        )
      },
      {
        path: `${base}/sales/plan/sv/equipment`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplanSvEquipments" */ '../containers/gamePlan/sv/equipments')
        )
      },
      {
        path: `${base}/sales/plan/sv/process`,
        component: lazy(() => import(/* webpackChunkName: "salesplanSvProcess" */ '../containers/gamePlan/sv/process'))
      },
      {
        path: `${base}/sales/plan/sv/detail`,
        component: lazy(() => import(/* webpackChunkName: "salesplanSvDetail" */ '../containers/gamePlan/sv/detail'))
      },
      {
        path: `${base}/sales/plan/ans-result`,
        component: lazy(() => import(/* webpackChunkName: "salesplanAnResult" */ '../containers/gamePlan/ansResult'))
      },
      {
        path: `${base}/sales/plan/ibs/:id`,
        component: lazy(() => import(/* webpackChunkName: "salesplanIBS" */ '../containers/gamePlan/ibs'))
      },
      {
        path: `${base}/sales/plan/iframe`,
        component: lazy(() => import(/* webpackChunkName: "salesplanIframe" */ '../containers/gamePlan/iframe'))
      },
      {
        path: `${base}/sales/plan/businessOpportunity`,
        component: lazy(() =>
          import(
            /* webpackChunkName: "salesplanBusinessOpportunity" */ '../containers/gamePlan/businessOpportunity/list'
          )
        )
      },
      {
        path: `${base}/sales/plan/businessOpportunity/detail`,
        component: lazy(() =>
          import(
            /* webpackChunkName: "salesplanBusinessOpportunity/detail" */ '../containers/gamePlan/businessOpportunity/detail'
          )
        )
      },
      {
        path: `${base}/sales/plan/probe/search/detail`,
        component: lazy(() =>
          import(/* webpackChunkName: "salesplanProbeSearch/detail" */ '../containers/gamePlan/probeSearchResult')
        )
      },
      {
        path: `${base}/sales/plan/mrIbs/:id`,
        component: lazy(() => import(/* webpackChunkName: "salesplanmrIbs" */ '../containers/gamePlan/mrIbs'))
      },

      // iconnect
      {
        path: `${base}/iconnect`,
        component: lazy(() => import(/* webpackChunkName: "iconnect" */ '@containers/iconnect/iconnect'))
      },

      /* APM Quora 云上大咖 */
      {
        path: 'quora',
        component: lazy(() => import(/* webpackChunkName: "quora" */ '../containers/quora/index'))
      },
      {
        path: 'quora/questionDetail/:id',
        component: lazy(() => import(/* webpackChunkName: "quora" */ '../containers/quora/QuestionDetail'))
      },
      /* approval */
      { path: 'approvals/transfer', component: lazy(() => import('../containers/approvals/ApprovalsTransferList')) },
      { path: 'approvals/retire', component: lazy(() => import('../containers/approvals/ApprovalsRetireList')) },
      { path: 'approvals', component: lazy(() => import('../containers/approvals/ApprovalsList')) },
      { path: 'approvals/:id', component: lazy(() => import('../containers/approvals/ApprovalDetailAndAudit')) },

      /* backup */
      {
        path: route_config.backup.path,
        component: lazy(() => import(/* webpackChunkName: "Backup" */ '../containers/backup')),
        indexRoute: {
          component: lazy(() => import(/* webpackChunkName: "BackupList" */ '../containers/backup/backupList'))
        },
        childRoutes: [
          {
            path: route_config.backup.children['/list'].path,
            component: lazy(() => import(/* webpackChunkName: "BackupList" */ '../containers/backup/backupList'))
          },
          {
            path: route_config.backup.children['/apply'].path,
            component: lazy(() => import(/* webpackChunkName: "backupApply" */ '../containers/backup/backupApply'))
          },
          {
            path: route_config.backup.children['/subscribe'].path,
            component: lazy(() => import(/* webpackChunkName: "backupApply" */ '../containers/backup/backupSubscribe'))
          },
          {
            path: route_config.backup.children['/instock'].path,
            component: lazy(() => import(/* webpackChunkName: "backupApply" */ '../containers/backup/backupInStock'))
          },
          {
            path: route_config.backup.children['/outstock'].path,
            component: lazy(() => import(/* webpackChunkName: "backupApply" */ '../containers/backup/backupOutStock'))
          }
        ]
      },

      /* adverseEvents 不良事件上报与审核 */
      {
        path: 'adverseEvents',
        component: lazy(() => import(/* webpackChunkName: "adverseEvents" */ '@containers/adverseEvents/EventsList'))
      },
      {
        path: 'adverseEvents/detail',
        component: lazy(() =>
          import(/* webpackChunkName: "adverseEventsDetail" */ '@containers/adverseEvents/EventsDetail')
        )
      },
      {
        path: 'adverseEvents/reportResult',
        component: lazy(() =>
          import(/* webpackChunkName: "adverseEventsReportResult" */ '@containers/adverseEvents/EventsReportResult')
        )
      },

      /* 'departmentSeconded [Acct 科室借调] */
      {
        path: route_urls.wx.secondedDetail,
        component: lazy(() => import('@containers/departmentSeconded/SecondedDetail'))
      },
      {
        path: route_urls.wx.secondedDetail + '/:assetUid',
        component: lazy(() => import('@containers/departmentSeconded/SecondedDetail'))
      },
      {
        path: route_urls.wx.secondedList,
        component: lazy(() => import('@containers/departmentSeconded/SecondedList'))
      },
      {
        path: route_urls.wx.secondment + '/:page',
        component: lazy(() => import('@containers/departmentSeconded/SecondmentList'))
      },
      {
        path: route_urls.wx.secondment + '/:page/:id',
        component: lazy(() => import('@containers/departmentSeconded/SecondmentDetail'))
      },
      /*备件验真*/
      {
        path: `${base}/spare/:part_info`,
        component: lazy(() => import(/* webpackChunkName: "spareParts" */ '@containers/spare/spareParts')),
        onEnter: byPassReg
      },
      {
        path: `${base}/spare`,
        component: lazy(() => import(/* webpackChunkName: "spareParts" */ '@containers/spare/spareParts')),
        onEnter: byPassReg
      },
      {
        path: route_urls.wx.chatbot,
        component: lazy(() => import(/* webpackChunkName: "chatbot" */ '../containers/chatbot'))
      },
      {
        path: route_urls.wx.QASharing,
        component: lazy(() => import(/* webpackChunkName: "chatbot" */ '../containers/chatbot/QASharing')),
        onEnter: byPassReg
      },
      {
        path: route_urls.userPoints,
        component: lazy(() => import(/* webpackChunkName: "chatbot" */ '../containers/user/userPoints'))
      }
    ]
  },

  {
    path: route_urls.others,
    component: lazy(() => import(/* webpackChunkName: "GeneralMsg" */ '../containers/GeneralMsg'))
  }
]

function byPass(nextState, replaceState) {
  setState(nextState, replaceState, 'any')
}

function byPassReg(nextState, replaceState) {
  setState(nextState, replaceState, 'reg')
}

function setState(nextState, replaceState, byPass) {
  let { location } = nextState
  if (location.state && !location.state.byPass) {
    location.state.byPass = byPass
    replaceState(location)
  } else if (!location.state) {
    location.state = { byPass }
    replaceState(location)
  }
}

export default routes
export { route_urls, route_config }
