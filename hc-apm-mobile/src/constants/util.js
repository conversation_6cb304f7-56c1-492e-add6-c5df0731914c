import moment from 'moment'
import { urls, rest, storage } from '../constants'
import { Dialog } from '../components'
import { Modal } from 'antd-mobile'
import igsMap from './igs_map.json'
import { browserHistory as history } from 'react-router'
import { route_urls } from './routes'
import queryString from 'query-string'
import { isWxBrowser, isAPMBrowser, isAndroid } from '../actions/jsApi'
import { message } from 'antd'
/**
 * state: redux store state
 * preload(state).zh('woSteps', 1)                  # 报修
 * preload(state).type('woSteps')                   # 所有woSteps列表
 * preload(state).get('sites', 2, 'aliasName')      # 医联体
 */
export function preload(state = window.___store___.getState()) {
  function get(type, id, column) {
    if (state && state.preload && state.preload[type]) {
      const obj = state.preload[type].filter(item => item.id == id)[0]
      if (obj && obj[column]) return obj[column]
      else return 'no id of column in preload'
    }
  }

  function zh(type, id) {
    if (state && state.preload && state.preload[type]) {
      const msg = state.preload[type].filter(item => item.msgKey == id)[0]
      if (msg && msg.valueZh) return msg.valueZh
      else return id
    }
  }

  function en(type, id) {
    const msg = state.preload[type].filter(item => item.msgKey == id)[0]
    if (msg && msg.valueEn) return msg.valueEn
    else return id
  }

  function list(type) {
    return state.preload[type] || []
  }

  /**
   * key/label/type
   */
  function val(obj, data) {
    if (obj.key) {
      let value = obj.key.indexOf('.') > -1 ? obj.key.split('.').reduce((prev, key) => prev[key], data) : data[obj.key]

      if (obj.type && state && state.preload) {
        const type = typeof obj.type === 'function' ? obj.type(data) : obj.type
        if (state.preload[type] && state.preload[type] instanceof Array) {
          const record = state.preload[type].filter(item => item.__default.key == value)[0]
          if (record) {
            value = record.__default.value
          }
        }
      }
      if (typeof value === 'boolean') {
        return value ? '是' : '否'
      }
      return value
    } else {
      return null
    }
  }

  function label(obj, data) {
    if (obj.label) return obj.label
    obj.i18n = obj.i18n || obj.key

    let label = obj.i18n

    if (state && state.preload && state.preload.label && state.preload.label instanceof Array) {
      const record = state.preload.label.filter(item => item.msgKey == obj.i18n)[0]
      if (record) {
        label = record.valueZh
      } else {
        if (state.preload.field_name && state.preload.field_name instanceof Array) {
          const item = state.preload.field_name.filter(item => item.__default.key == obj.i18n)[0]
          if (item) {
            label = item.__default.value
          }
        }
      }
    }
    return label
  }

  return {
    label,
    val,
    get,
    en,
    zh,
    list
  }
}

const day = {
  from: moment().format('YYYY-MM-DD'),
  to: moment().format('YYYY-MM-DD')
}
const week = {
  from: moment().add(-7, 'days').format('YYYY-MM-DD'),
  to: moment().format('YYYY-MM-DD')
}
const month = {
  from: moment().add(-1, 'months').format('YYYY-MM-DD'),
  to: moment().format('YYYY-MM-DD')
}

export const date = { day, week, month }

/**
 *
 * @param {object} org - The orgnization object. Usually is the result returned from rest.get(urls.org + '/' + instituteUID).
 * @param {org_type} key - The key of the value.
 * @param {*} value - The value(id) used to target the org name.
 */
export function findOrgName(org, key, value) {
  let orgName
  if (org[key] == value) {
    // 如果查询科室, 但orgType不是4(科室), 则orgName显示空
    if (key == 'id') {
      if (org.orgType == 4) orgName = org.name
      else orgName = ''
    } else {
      orgName = org.name
    }
  } else if (org.subOrgs) {
    for (let i = org.subOrgs.length - 1; i >= 0; i--) {
      let sub = org.subOrgs[i]
      let tempName = this.findOrgName(sub, key, value)
      if (tempName) {
        orgName = tempName
        break // break immediately once the orgname is found
      }
    }
  }
  return orgName
}

export function scrollToBottom() {
  window.scrollTo(0, document.body.scrollHeight || document.documentElement.scrollHeight)
}

export function transformDateTimeAPIFaced(datetime) {
  if (datetime) {
    return datetime.replace('T', ' ')
  }
  return ''
}

export function transformDateTimeComponentFaced(datetime) {
  if (datetime) {
    return datetime.replace(' ', 'T')
  }
  return ''
}

export function isNotUndefined(v) {
  return typeof v !== 'undefined'
}

export async function parseQrCode(qrCodeStr) {
  const qrCodeObj = await rest.post(urls.qrCodeRule, { qrCodeStr })
  let asset
  if (qrCodeObj) {
    if (Array.isArray(qrCodeObj.listQrCodeLib)) {
      asset = qrCodeObj.listQrCodeLib[0].assetInfo
    }
    if (qrCodeObj.codeStr === 'GEHC_iConnect') {
      asset && isIbTenant(asset.siteId)
        ? registerOrRepair(asset, goOneClickRepair.bind(null, asset))
        : goOneClickRepair(asset)
    }
    return { asset, qrCode: qrCodeObj.codeStr, qrCodeObj }
  } else {
    return { asset, qrCode: null, qrCodeObj }
  }
}

export async function assetExternalRepair(asset) {
  if (isIbSite()) goOneClickRepair(asset)
  // 22: CT Distribution, 3: US Distribution
  // YHAAO-7183 all US direct to 外修 400
  const geDistribution = [22].includes(asset.geDeviceType)
  const isDistribution = !geDistribution || moment(asset.warrantyDate).isAfter(moment())

  // 检查设备报修类别 repairForm, 1: 内修, 2: 外修, 3: 混合. 如果是外修直接跳转, 如果是混合, 需要选择
  // 检查设备报修类别 ibStatus不等于null或者0 (1,2,3...);  并且repairForm, 1: 内修, 2: 外修, 3: 混合. 如果是外修直接跳转, 如果是混合, 需要选择
  // if (asset.ibStatus && isDistribution && (asseet.comments === 'zkb')) {
  if (asset.ibStatus && isDistribution) {
    switch (asset.repairForm) {
      case 2:
        goOneClickRepair(asset)
        return Promise.resolve(true)
      case 3:
        Dialog.confirm('此设备修理类别', '请选择', { cancel: '内修', ok: '外修' }).onDismiss(result => {
          console.log({ result })
          if (result) {
            goOneClickRepair(asset)
          } else {
            return Promise.resolve(false)
          }
        })
      default:
        // 默认内修
        return Promise.resolve(false)
    }
  } else return Promise.resolve(false)
}

// 跳转到报修
export function goOneClickRepair(asset = {}, type = 'replace') {
  if (isAPMBrowser()) {
    message.error('app 不支持外修,可在资产智管家公众号报修')
    return
  }
  // type 用于区别新老版本改变url的方式
  let productNo = asset.systemId || asset.qrCode || ''
  let userAccount = myself().userAccount || {}
  let hospitalName = asset.siteName || ''
  // 对未绑定的账号，username做空处理
  let username = userAccount.id === 0 ? '' : userAccount.name || ''
  let mobilephonenum = userAccount.id === 0 ? '' : userAccount.telephone || ''
  let apmOpenID = wxUserInfo() ? wxUserInfo().openId : userAccount.weChatId || ''

  const pathname = `/iconnect`
  const query = { apmOpenID, productNo, hospitalName, mobilephonenum, username, repairType: 3 }
  const paramsStr = queryString.stringify(query)
  if (type === 'push') {
    return (window.location.href = `${pathname}?${paramsStr}`)
  }
  window.location.replace(`${pathname}?${paramsStr}`)
}

export function registerOrRepair(asset, repairCallback) {
  Modal.alert('请选择', '', [
    {
      text: '注册',
      onPress: () => {
        history.replace({ pathname: route_urls.register, state: { asset } })
      }
    },
    {
      text: '报修',
      onPress: () => {
        if (typeof repairCallback === 'function') repairCallback()
      }
    }
  ])
}

export function fixModalBug() {
  if (document.getElementsByClassName('iosBugFixCaret').length > 0) {
    if (document.getElementsByClassName('modal-open').length > 0) {
      document.getElementsByTagName('body')[0].classList.remove('modal-open')
    } else {
      document.getElementsByTagName('body')[0].classList.add('modal-open')
    }
  }
}

export function arrayToObject(items) {
  const obj = {}
  if (items) {
    items.forEach(item => {
      Object.keys(item).forEach(key => {
        const value = isNaN(item[key]) === false ? toDecimal(item[key]) : item[key]
        if (value !== null) {
          if (obj[key] !== undefined) {
            obj[key].push(value)
          } else {
            obj[key] = [value]
          }
        }
      })
    })
  }
  return obj
}

function toDecimal(x) {
  var f = parseFloat(x)
  if (isNaN(f)) {
    return
  }
  f = Math.round(x * 100) / 100
  return f
}

export function hasRole(roleList = [], ...roles) {
  roles = new Set(roles)
  roleList.forEach(role => {
    roles.delete(role.name)
  })
  return roles.size === 0
}

export function hasAtLeastOneRole(roleList = [], ...roles) {
  let count = roles.length
  roles = new Set(roles)
  roleList.forEach(role => {
    roles.delete(role.name)
  })
  return roles.size < count
}

export function getIgsCnName(en) {
  const cn = igsMap.filter(item => item.FIELD3 == en)[0]
  return (cn && cn.FIELD4) || en
}

export function setViewPortScalable(yesOrNo) {
  const viewport = document.querySelector('meta[name=viewport]')
  const content =
    yesOrNo === 'yes'
      ? viewport.getAttribute('content').replace('user-scalable=no', 'user-scalable=yes')
      : viewport.getAttribute('content').replace('user-scalable=yes', 'user-scalable=no')
  viewport.setAttribute('content', content)
}

export function create_UUID() {
  let dt = new Date().getTime()
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = (dt + Math.random() * 16) % 16 | 0
    dt = Math.floor(dt / 16)
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}

export function isIbTenant(id) {
  if (id) {
    return id === 46
  } else {
    const { myself } = window.___store___.getState().preload
    return myself.userAccount.siteId === 46
  }
}

export function isAcctSite() {
  if (['stg', 'stg2', 'prod', 'pre'].includes(SERVER_GATEWAY)) return true
  const sites = [
    'https://apm.fehmg.com',
    'https://apm-stg.gechs.cn',
    'https://apm-acct-preprd.gehcedison.cn',
    'https://apm-acct-preprd.gehealthcloud.cn',
    'https://hcapm.gehealthcloud.cn',
    'http://127.0.0.1',
    'https://apm-acct-stg.gehealthcloud.cn'
  ]
  return sites.some(item => location.origin.indexOf(item) === 0)
}

export const ibSites = [
  'https://apm-ib-preprd.gehcedison.cn',
  'https://apm-ib-preprd.gehealthcloud.cn',
  'https://apm-ib-stg.gehealthcloud.cn',
  'https://apm-ib-stg.gechs.cn',
  'https://geapm.gechs.cn',
  'https://apm.gehealthcloud.cn'
]

export function isValidIbQrCode(url) {
  try {
    let a = new URL(url)
    const has_q_number = a.href.match(/\/[q,r]\/[0-9]*/) // https:/xxxx/q/1234 or https:/xxxx/r/1234
    if (ibSites.some(site => a.origin === site) && has_q_number) {
      a.hostname = a.hostname === 'geapm.gechs.cn' ? 'apm.gehealthcloud.cn' : a.hostname
      return a.href
    } else {
      return false
    }
  } catch (e) {
    console.error(e)
    return false
  }
}

export function isFarEastSite() {
  const sites = ['https://apm.fehmg.com']
  return sites.some(item => location.origin.indexOf(item) === 0)
}

export function isIbSite() {
  if (['stg_ib', 'ib', 'pre_ib'].includes(SERVER_GATEWAY)) return true
  return ibSites.some(item => location.origin.indexOf(item) === 0)
}

export function isUniSite() {
  const sites = ['https://amtest.universalmsm.com', 'https://am.universalmsm.com/']
  return sites.some(item => location.origin.indexOf(item) === 0)
}

export function isInternationalSite() {
  const site = 'http://apm-uat.hcdigital.com.cn'
  return site === location.origin
}

export function isSupplier(myTenant) {
  const { myself } = window.___store___.getState().preload
  return myself.userAccount.loginName.indexOf('hc-apm-supplier') === 0
}

export function isZTB(asset) {
  if (asset) {
    return asset.ibStatus === 5
  } else {
    const { myself } = window.___store___.getState().preload
    const myTenant = myself.userAccount.tenantInfo
    return myTenant.type === 4
  }
}

/**
 * @description check if user is ZKB's supplier
 */
export function isZKB(myTenant) {
  if (!myTenant) {
    const { myself } = window.___store___.getState().preload
    myTenant = myself.userAccount.tenantInfo
  }
  return myTenant.type === 3
}

export function isAiUser() {
  const { myself } = window.___store___.getState().preload
  if (myself && myself.userAccount && Array.isArray(myself.userAccount.tags)) {
    if (myself.userAccount.tags.includes('AI')) {
      return true
    }
  }

  return false
}

export function isSuperUser() {
  const { myself } = window.___store___.getState().preload
  if (myself && myself.userAccount && Array.isArray(myself.userAccount.tags)) {
    if (myself.userAccount.tags.includes('super')) {
      return true
    }
  }

  return false
}

export function isTrialUser() {
  const myselfConf = myself()
  if (myselfConf && myselfConf.userAccount && Array.isArray(myselfConf.userAccount.tags)) {
    if (myselfConf.userAccount.tags.includes('试用机账号标签')) {
      return true
    }
  }
  return false
}

export function isIbConnectUser() {
  const { userConnInfo } = window.___store___.getState().preload
  if (Array.isArray(userConnInfo)) {
    const ibInfo = userConnInfo.find(item => item.envType === 'IB')
    const acctInfo = userConnInfo.find(item => item.envType === 'ACCT')
    if (ibInfo && acctInfo && ibInfo.connUserAccountInfo && acctInfo.connUserAccountInfo) {
      const { loginName = '' } = ibInfo.connUserAccountInfo
      const { tenantType } = acctInfo.connUserAccountInfo
      if (loginName.indexOf('ibconnect-') === 0 || tenantType === 3) {
        return true
      }
    }
  }
  return false
}

export function hasFeature(featureName) {
  const { myself } = window.___store___.getState().preload
  if (myself && Array.isArray(myself.featureList)) {
    const has = myself.featureList.includes(featureName)
    if (has) {
      return true
    }
  }
  return false
}

export function hasFunction(functionName) {
  const { myself } = window.___store___.getState().preload
  if (myself && Array.isArray(myself.functionList)) {
    return myself.functionList.includes(functionName)
  }
  return false
}

export function isNursePower() {
  // true: 开关机登记页面 false: 使用登记页面 二选一
  const { myself } = window.___store___.getState().preload
  if (myself && myself.userAccount) {
    const featureList = JSON.parse(myself.userAccount.tenantInfo.featureList)
    const hasUseRECORD = featureList.includes('NURSESITE_RECORD')
    if (hasUseRECORD) {
      return true
    }
  }
  return false
}

export function hasSpecialAsset() {
  const { myself } = window.___store___.getState().preload
  const roleList = new Set(myself.userRoleList.map(r => r.name))

  return roleList.has('SpecialEquipmentRole') && hasFeature('SPECIAL_ASSET')
}
export function showOneClickInventory() {
  // 显示一键盘点按钮
  return hasFunction('ASSET_INVENTORY_MOBILE_BATH_INVENTORY')
}

export function showAssetRecords() {
  // 显示使用登记而不显示开关机登记
  return hasFunction('NURSESITE_USING_RECORD_VIEW')
}

export function isEnableAssetTransfer() {
  // 资产转科入口逻辑判断
  return hasFunction('ASSET_DEPARTMENT_CONVERT')
}

export function myself() {
  return window.___store___.getState().preload.myself
}

export function wxUserInfo() {
  return window.___store___.getState().preload.wxUserInfo
}

export function myConfig() {
  return window.___store___.getState().preload.myConfig
}

export function userConnInfo() {
  return window.___store___.getState().preload.userConnInfo
}

export function log(dataset) {
  const eventArray = [
    {
      ...dataset,
      assetUid: dataset.uid,
      time: moment().format('YYYY-MM-DD HH:mm:ss')
    }
  ]

  if (isWxBrowser()) {
    return rest.log(eventArray)
  } else {
    let trackData = JSON.parse(localStorage.getItem(storage.tracking)) || []
    trackData = trackData.concat(eventArray)
    localStorage.setItem(storage.tracking, JSON.stringify(trackData))
    return eventArray
  }
}

export { isWxBrowser }

export function hideAssetStatus4(item) {
  const {
    myself: { customRoles },
    assetStatus
  } = window.___store___.getState().preload
  const cfg = window.___store___.getState().workflowConfig.get('workflowConfig')
  let result = true
  if (cfg && cfg.enableScrappedControl) {
    const shouldShow = customRoles.find(role => role.authorities.indexOf('assetScrapped.tobe') > -1)
    if (!shouldShow) {
      result = item.key !== 4
    }
  }
  return result
}

export async function getReleaseVersion() {
  const res = await rest.get('/wx/releaseVersion.txt')
  const config = {}
  if (res) {
    console.log('getReleaseVersion', res)
    res.text
      .trim()
      .split('\n')
      .forEach(line => {
        config[line.split(': ')[0]] = line.split(': ')[1]
      })
  }
  console.log('getReleaseVersion Config', config)
  return config
}

export const reload = props => {
  history.replace({
    pathname: '/wx/msg',
    query: { code: 203 },
    state: { desc: (props && props.desc) || '', next: history.getCurrentLocation() },
    ...props
  })
}

const clientId = (Math.random() + 1).toString(36).substring(7)
export const download = async (id, expiredT) => {
  if (id) {
    if (isWxBrowser()) {
      const authCode = await rest.get(`${urls.getAuthCode}/${id}/${expiredT ? 'maintain' : ''}${clientId}`, _, true)
      if (authCode) {
        return `${urls.downloadByAuthCode}/${authCode}/${expiredT ? 'maintain' : ''}${clientId}`
      } else {
        return null
      }
    } else {
      return `${urls.objDownload}/${id}`
    }
  }
}

/**
 * 通用二进制流下载函数，支持自动获取文件名
 * @param {string} url 下载地址
 */
export function downloadBlobFile(url) {
  return rest.get(url, {}, true).then(res => {
    if (res.body && res.body.objectId) {
      download(res.body.objectId).then(url => location.href = url)
    }
  })
}

export const COPYRIGHT = `Copyright © ${moment().year()} GE HealthCare`
