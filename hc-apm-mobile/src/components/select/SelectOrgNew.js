import React, { useState, useEffect } from 'react'
import { FormCell, CellBody, CellHeader, CellFooter, Label } from 'react-weui'
import OrgFilter from '@/components/OrgFilter'
import { Drawer, Icon } from 'antd'

const NULL_ORG = {
  tenantUID: null,
  institutionUID: null,
  hospitalUID: null,
  siteUID: null,
  clinicalDeptId: null,
  clinicalDeptUID: null
}

const INITIAL_NAME = '请选择'

const SelectOrgNew = props => {
  const { warn, label, defaultValue, org, style, onChange, showOrg, disableOrg, drawerHeight, clearable, hasVirtualDepartment={}, showCount, disabled } = props
  const [showDrawer, setShowDrawer] = useState(false)
  const [name, setName] = useState(INITIAL_NAME)

  const getName = (defaultValue, org) => {
    if ((org && org.uid === defaultValue) || (org && org.id === defaultValue)) {
      return org.name
    } else if (org && org.subOrgs.length > 0) {
      return org.subOrgs.reduce((last, curr) => last + getName(defaultValue, curr), '')
    } else {
      return INITIAL_NAME
    }
  }

  useEffect(() => {
    defaultValue && setName(getName(defaultValue, org))
  }, [defaultValue, org])

  const _onChange = (org, query) => {
    if (org === null) {
      setName(INITIAL_NAME)
      typeof onChange === 'function' && onChange(NULL_ORG, NULL_ORG)
      return
    }
    setName(org.name)
    setShowDrawer(false)
    typeof onChange === 'function' && onChange(query, org)
  }

  return (
    <FormCell select selectPos='after' warn={warn && true}>
      {warn && <CellFooter>{warn}</CellFooter>}
      <CellHeader>
        {clearable && name && name !== INITIAL_NAME && <Icon
          type='close-circle'
          style={{ position: 'absolute', right: '2rem', fontSize: '1rem', top: '50%', marginTop: '-0.5rem' }}
          onClick={() => _onChange(null)} />}
        <Label>{label}</Label>
      </CellHeader>
      <CellBody>
        <div style={{...style, color: disabled ? 'rgba(0, 0, 0, 0.65)' : '#000'}} onClick={() => !disabled && setShowDrawer(true)}>{name}</div>
        <Drawer
          className='drawer'
          placement='bottom'
          closable={false}
          visible={showDrawer}
          onClose={() => setShowDrawer(false)}
          height={`${drawerHeight}vh` || '50vh'}
        >
          <OrgFilter
            showCount={showCount}
            showOrg={showOrg}
            disableOrg={disableOrg}
            style={{ height: drawerHeight ? `${drawerHeight - 14.5}vh` : '40vh', overflow: 'auto', marginTop: '15px' }}
            org={org}
            defaultValue={defaultValue}
            hasVirtualDepartment={hasVirtualDepartment}
            onChange={_onChange} />
        </Drawer>
      </CellBody>
      <CellFooter />
    </FormCell>
  )
}

export default SelectOrgNew
