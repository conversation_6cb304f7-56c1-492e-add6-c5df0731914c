import React, { useState, useEffect, useRef } from 'react'
import { Drawer, Input, Spin, Empty, Icon } from 'antd'
import { rest, urls } from '../constants'
import { QrCodeScan, List } from '@components'

const Circle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <circle cx="5" cy="5" r="5" fill={color} />
  </svg>
)
const TriAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 5 0 L 10 10 L 0 10 L 5 0" />
  </svg>
)
const DoAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 5 0 L 5 10 L 10 5 L 0 5 L 5 0" />
  </svg>
)
const DdAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 0 5 L 5 10 L 10 5 L 5 0 L 0 5" />
  </svg>
)
const Square = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 2 2 L 8 2 L 8 8 L 8 2 L 2 2" />
    <rect x={0} y={0} width="10" height="10" stroke={color} fill={color} strokeWidth="2" />
  </svg>
)

const badgeStyle = {
  1: { color: '#04DA6E', icon: <Circle color="#04DA6E" /> },
  2: { color: '#FF2323', icon: <TriAngle color="#FF2323" /> },
  报警: { color: '#FF2323', icon: <TriAngle color="#FF2323" /> },
  3: { color: '#F37C00', icon: <DdAngle color="#F37C00" /> },
  4: { color: '#FF2323', icon: <DoAngle color="#FF2323" /> },
  5: { color: '#FF2323', icon: <Square color="#FF2323" /> }
}

const { Search } = Input
const AssetSearch = props => {
  const inputEl = useRef(null)
  useEffect(() => {
    inputEl.current.focus()
  }, [])
  const onScan = () => {
    props.setDrawer && props.setDrawer(false)
    QrCodeScan('asset').then(asset => {
      console.log('扫码结果', asset)
      props.onChange && props.onChange(asset)
    })
  }
  return (
    <Search
      ref={inputEl}
      allowClear={true}
      placeholder={props.searchPlaceholder || '搜索主设备'}
      prefix={<Icon type="qrcode" onClick={onScan} />}
      onSearch={props.onSearch}
    />
  )
}

const Assets = ({ data, onChange }) => {
  return (
    <List
      data={data}
      itemHeader={{ key: 'name' }}
      badge={{ key: 'status', type: 'assetStatus' }}
      badgeStyle={badgeStyle}
      itemContent={[
        { key: 'assetName' },
        { key: 'departNum' },
        { key: 'financingNum' },
        { key: 'serialNum' },
        { key: 'qrCode' },
        { key: 'clinicalDeptName' },
      ]}
      onClick={onChange}
    />
  )
}

const MasterAsset = props => {
  const { editable, value, onChange, searchPlaceholder } = props
  const style = Object.assign(props.style || {}, editable && { color: '#00B9E6' })

  const [name, setName] = useState(editable && '请选择')
  const [assets, setAssets] = useState([])
  const [drawer, setDrawer] = useState(false)
  const showSpin = value && name === '请选择'
  const [search, setSearch] = useState('init')

  const showDrawer = () => {
    if (editable) {
      setDrawer(true)
      // searchAssets()
    }
  }
  const showAssetName = (
    <div style={style} onClick={showDrawer}>
      {name}
    </div>
  )

  const getAssetNameById = () => {
    if (value && Number.isInteger(value)) {
      rest.get(`${urls.assets}/${value}`).then(asset => {
        if (asset && asset.name) {
          setName(asset.name)
        }
      })
    }
  }

  const searchAssets = (search = '') => {
    const { siteUID, clinicalDeptId } = props
    const query = {
      isValid: true,
      status: '[1,2,3]',
      assetName: search,
      hasParent: false,
      pageSize: 100,
      siteUID
    }
    if (clinicalDeptId && clinicalDeptId !== -1) {
      Object.assign(query, { clinicalDeptId })
    }
    setSearch('wip')
    rest.list(urls.assets, query).then(res => {
      setSearch('done')
      const { data, rowCount } = res
      if (rowCount > 100) {
        // message.warn('只显示前100条结果, 请精确查询')
      }
      setAssets(data)
    })
  }

  const selectAsset = asset => {
    console.log('selectAsset', asset)
    setName(asset.name)
    setDrawer(false)

    if (typeof onChange === 'function') {
      onChange(asset)
    }
  }

  useEffect(getAssetNameById, value)

  return (
    <span>
      {showSpin ? <Spin /> : showAssetName}
      <Drawer
        closable={false}
        visible={drawer}
        onClose={() => setDrawer(false)}
        title={
          <AssetSearch
            onSearch={searchAssets}
            searchPlaceholder={searchPlaceholder}
            onChange={selectAsset}
            setDrawer={setDrawer}
          />
        }
        placement="bottom"
        height="50vh"
        bodyStyle={{
          overflowY: 'auto',
          padding: 0,
          margin: 0
        }}
      >
        {search === 'init' && <Empty description="请搜索" />}
        {search === 'wip' && (
          <Center>
            <Spin tip="检索中" />
          </Center>
        )}
        {search === 'done' && assets.length > 0 && <Assets data={assets} onChange={selectAsset} />}
        {search === 'done' && assets.length === 0 && <Empty description="无结果" />}
      </Drawer>
    </span>
  )
}

const Center = props => (
  <div
    style={{
      width: '100%',
      height: '20vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}
  >
    {props.children}
  </div>
)

export default MasterAsset
