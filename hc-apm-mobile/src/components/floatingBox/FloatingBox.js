import React, { useRef, useEffect } from 'react'
import './index.less'
import { storage } from '@constants'

export default function FloatingBox(props) {
  const children = props.children
  const floatingDom = useRef(null)
  const space = props.space || 0
  const attractspace = space + 10
  const fullDrag = props.fullDrag || false
  const startX = useRef(null)
  const startY = useRef(null)
  const style = props.style

  // 加载初始位置（按比例）
  useEffect(() => {
    const setBoxPosition = () => {
      const pos = localStorage.getItem(storage.homePos)
      if (pos && floatingDom.current) {
        try {
          const { leftRatio, topRatio } = JSON.parse(pos)
          const winW = document.documentElement.clientWidth
          const winH = document.documentElement.clientHeight
          const boxW = floatingDom.current.offsetWidth
          const boxH = floatingDom.current.offsetHeight
          let left = Math.min(Math.max(leftRatio * winW, 0), winW - boxW)
          let top = Math.min(Math.max(topRatio * winH, 0), winH - boxH)
          floatingDom.current.style.left = left + 'px'
          floatingDom.current.style.top = top + 'px'
          floatingDom.current.style.right = 'auto'
          floatingDom.current.style.bottom = 'auto'
        } catch (e) {
          // ignore parse error
        }
      }
    }
    setBoxPosition()
    window.addEventListener('resize', setBoxPosition)
    return () => {
      window.removeEventListener('resize', setBoxPosition)
    }
  }, [])

  const touchstart = e => {
    console.log('touchstart')
    let touches = e.touches[0]
    startX.current = touches.clientX - floatingDom.current.offsetLeft
    startY.current = touches.clientY - floatingDom.current.offsetTop
    floatingDom.current.style.transition = 'none'
  }
  const touchMove = e => {
    console.log('touchMove')
    e.stopPropagation()
    e.preventDefault()
    let touches = e.touches[0]
    let clientLeft = touches.clientX - startX.current
    let clientTop = touches.clientY - startY.current
    if (clientLeft < attractspace) {
      clientLeft = space
    } else if (clientLeft > document.documentElement.clientWidth - floatingDom.current.offsetWidth - attractspace) {
      clientLeft = document.documentElement.clientWidth - floatingDom.current.offsetWidth - space
    }
    if (clientTop < 0) {
      clientTop = 0
    } else if (clientTop > document.documentElement.clientHeight - floatingDom.current.offsetHeight) {
      clientTop = document.documentElement.clientHeight - floatingDom.current.offsetHeight
    }
    floatingDom.current.style.left = clientLeft + 'px'
    floatingDom.current.style.right = 'auto'
    floatingDom.current.style.top = clientTop + 'px'
    floatingDom.current.style.bottom = 'auto'
  }
  const touchEnd = e => {
    console.log('touchEnd')
    let touches = e.changedTouches[0]
    let clientLeft = touches.clientX - startX.current - space
    let clientTop = touches.clientY - startY.current
    if (!fullDrag) {
      if (clientLeft < (document.documentElement.clientWidth - floatingDom.current.offsetWidth - space) / 2) {
        clientLeft = space
      } else {
        clientLeft = document.documentElement.clientWidth - floatingDom.current.offsetWidth - space
      }
      if (clientTop < 0) {
        clientTop = 0
      } else if (clientTop > document.documentElement.clientHeight - floatingDom.current.offsetHeight) {
        clientTop = document.documentElement.clientHeight - floatingDom.current.offsetHeight
      }
      floatingDom.current.style.left = clientLeft + 'px'
      floatingDom.current.style.right = 'auto'
      floatingDom.current.style.top = clientTop + 'px'
      floatingDom.current.style.bottom = 'auto'
      floatingDom.current.style.transition = 'all 0.3s cubic-bezier(0.03, 1, 0.87, 1.04) 0s'
    }
    // 保存位置到 localStorage（按比例）
    if (floatingDom.current) {
      const left = parseInt(floatingDom.current.style.left, 10) || 0
      const top = parseInt(floatingDom.current.style.top, 10) || 0
      const winW = document.documentElement.clientWidth
      const winH = document.documentElement.clientHeight
      const leftRatio = winW ? left / winW : 0
      const topRatio = winH ? top / winH : 0
      localStorage.setItem(storage.homePos, JSON.stringify({ leftRatio, topRatio }))
    }
  }

  useEffect(() => {
    floatingDom.current && floatingDom.current.addEventListener('touchstart', touchstart)
    floatingDom.current && floatingDom.current.addEventListener('touchmove', touchMove)
    floatingDom.current && floatingDom.current.addEventListener('touchend', touchEnd)
    return () => {
      floatingDom.current.removeEventListener('touchstart', touchstart)
      floatingDom.current.removeEventListener('touchmove', touchMove)
      floatingDom.current.removeEventListener('touchend', touchEnd)
    }
  }, [floatingDom.current])
  return (
    <div className="floating-box" ref={floatingDom} style={style}>
      {children}
    </div>
  )
}
