import React, { useRef, useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { Button } from 'antd'
import { browserHistory as history } from 'react-router'
import { route_urls, storage, util } from '../constants'
import FloatingBox from './floatingBox/FloatingBox'

// 路由白名单和黑名单，支持简单通配符（* 只支持结尾）
const INCLUDE_ROUTES = [
  '/wx/*'
  // ...可添加更多允许显示的路由
]
const EXCLUDE_ROUTES = [
  '/wx',  // 微工作台
  '/wx/', // 微工作台
  '/wx/login', // 登录页
  // ...可添加更多不允许显示的路由
]

// 简单通配符匹配函数，忽略 query string
function matchRoute(path, patterns) {
  // 只取 pathname 部分
  const pathname = path.split('?')[0]
  return patterns.some(pattern => {
    if (pattern.endsWith('/*')) {
      return pathname.startsWith(pattern.slice(0, -1))
    }
    return pathname === pattern
  })
}

const BackToConsole = props => {
  // 通过state强制刷新组件
  const [, setRouteKey] = useState(0)

  useEffect(() => {
    // 监听popstate
    const onPopState = () => setRouteKey(k => k + 1)

    window.addEventListener('popstate', onPopState)

    // 监听history.push/replace
    const rawPush = history.push
    const rawReplace = history.replace

    history.push = function (...args) {
      rawPush.apply(this, args)
      setRouteKey(k => k + 1)
    }
    history.replace = function (...args) {
      rawReplace.apply(this, args)
      setRouteKey(k => k + 1)
    }

    return () => {
      window.removeEventListener('popstate', onPopState)
      history.push = rawPush
      history.replace = rawReplace
    }
  }, [])

  // 获取当前路由（包含pathname和search）
  const currentPath = window.location.pathname + window.location.search

  // 判断是否允许显示
  const canShow =
    matchRoute(currentPath, INCLUDE_ROUTES) &&
    !matchRoute(currentPath, EXCLUDE_ROUTES)

  const onClick = () => {
    if (tempOrGuest(props.myself)) {
      history.push(route_urls.login)
    } else {
      history.push(route_urls.home_wx)
    }
  }
  return !util.isIbSite() && canShow ? (
    <FloatingBox space={20}>
      <Button icon="home" shape="circle" type="primary" onClick={onClick} />
    </FloatingBox>
  ) : null
}

function tempOrGuest(myself) {
  if (myself.userAccount.id == 0 || myself.userRoleList.find(role => role.name == 'Guest')) {
    return true
  } else {
    return false
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    myConfig: state.preload.myConfig
  }
}

export default connect(mapStateToProps)(BackToConsole)
