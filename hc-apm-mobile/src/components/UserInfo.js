import React, { Component } from 'react'
import { browserHistory as history } from 'react-router'
import { Cell, CellBody, MediaBox, MediaBoxHeader, MediaBoxBody, MediaBoxTitle, MediaBoxDescription } from 'react-weui'
import { Icon } from 'antd'
import { connect } from 'react-redux'
import { storage } from '../constants'
import logo from '../images/user.png'
import * as util from '../constants/util'
import { isAppOffline } from '@/actions/jsApi'
import iconWo from '@images/assets/报修.svg'

let clickCount = 0;
let timeout;

export function triClick(callback) {

  function handleClick() {
    clickCount++;

    if (clickCount === 3) {
      clearTimeout(timeout);
      callback();
      clickCount = 0;
    } else {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        clickCount = 0;
      }, 1000);
    }
  }

  return handleClick;
}

export const setVconsoleToggle = () => {
  if (localStorage.getItem(storage.vconsole)) {
    localStorage.removeItem(storage.vconsole)
    window.vconsole && window.vconsole.destroy()
  } else {
    const Vconsole = require('vconsole')
    window.vconsole = new Vconsole()
    const d = new Date()
    localStorage.setItem(storage.vconsole, d.getMonth().toString() + d.getDate().toString())
  }
}

class UserInfo extends Component {
  state = { loginAs: '', defaultLong: 60, userRole: '' }

  componentDidMount() {
    const loginAs = localStorage.getItem(storage.loginAs)
    this.setState({ loginAs })
    this.updateDefaultLong()
    window.addEventListener('resize', this.updateDefaultLong)
    this.getUserRole()
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateDefaultLong)
  }

  updateDefaultLong = () => {
    this.setState({ defaultLong: 60 * (document.body.offsetWidth / 375) })
  }

  getUserRole() {
    const { myself } = this.props
    const { userAccount } = myself
    if (userAccount) {
      const { position, siteId } = userAccount
      if (util.isIbTenant(siteId)) {
        if (position === 'ultrasoundDirector' || position === 'ultrasoundTechnician') {
          this.setState({ userRole: 'master' })
        } else {
          this.setState({ userRole: 'ibCustomer' })
        }
      } else {
        this.setState({ userRole: 'normal' })
      }
    }
  }

  renderRoles() {
    const { userRoleList } = this.props.myself
    userRoleList.sort((a, b) => a.id - b.id)
    const roles = userRoleList.map(role => role.roleDesc).join(', ')
    return roles
  }

  render() {
    const { wxUserInfo, myself, onClick, userOrgInfo, goScanQrCode } = this.props
    const { loginAs, userRole } = this.state
    const head = wxUserInfo && wxUserInfo.headImgUrl ? wxUserInfo.headImgUrl : logo
    const noAssetWo = util.hasFunction('REPAIR_NO_ASSET_ORDER_MOBILE_CREATE')
    return (
      <Cell
        className={`user`}
        style={{
          color: '#fff'
        }}
      >
        <CellBody className={`${userRole}`}>
          <MediaBox type="appmsg" style={{ height: '82px' }}>
            <MediaBoxHeader>
              <img
                onClick={triClick(setVconsoleToggle)}
                src={head}
                style={{
                  borderRadius: '50%',
                  border: '3px solid #fff',
                  height: this.state.defaultLong + 'px',
                  width: this.state.defaultLong + 'px'
                }}
              />
            </MediaBoxHeader>
            <MediaBoxBody style={{ marginLeft: '15px' }}>
              <MediaBoxTitle
                style={{ color: userRole === 'ibCustomer' ? 'rgb(39,96,126)' : 'white', marginTop: '10px' }}
              >
                {myself.userAccount.name}
              </MediaBoxTitle>
              {loginAs && <div style={{ fontSize: '10px', maxWidth: '60vw', color: '#1890ff' }}>{`(由${loginAs}代理)`}</div>}
              {userOrgInfo && userOrgInfo.name && <MediaBoxDescription style={{ color: userRole === 'ibCustomer' ? 'rgb(39,96,126)' : 'white' }}>
                {userOrgInfo.name}
              </MediaBoxDescription>}
            </MediaBoxBody>
            {noAssetWo && <img
              src={iconWo}
              alt="icon"
              style={{
                width: 24,
                height: 24,
                marginRight: 10,
                verticalAlign: 'middle',
                filter: 'invert(100%)', // 使其为白色，与antd Icon风格一致
                display: 'inline-block'
              }}
              onClick={() => history.push({ pathname: '/wx/no-asset-wo' })}
            />}
            {goScanQrCode && !isAppOffline() && (
              <Icon type="scan" style={{ fontSize: '24px', marginRight: '10px' }} onClick={goScanQrCode} />
            )}
            {onClick && !window.gfc && <Icon type="right" style={{ fontSize: '24px' }} onClick={onClick} />}
          </MediaBox>
        </CellBody>
      </Cell>
    )
  }
}

function mapStateToProps(state) {
  return {
    wxUserInfo: state.preload.wxUserInfo,
    userOrgInfo: state.common.get('userOrgInfo'),
    myself: state.preload.myself
  }
}

export default connect(mapStateToProps)(UserInfo)
