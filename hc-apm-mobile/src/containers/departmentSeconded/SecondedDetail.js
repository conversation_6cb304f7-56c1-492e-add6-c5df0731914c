import React, { Component } from 'react'
import { connect } from 'react-redux'
import moment from 'moment'
import { message, Spin, Modal, List } from 'antd'
import { CellBody, Cells, Cell, CellFooter } from 'react-weui'
import { Form, Preview, sign } from '../../components'
import { urls, rest } from '../../constants'
import { browserHistory } from 'react-router'
import { eventsState_role, eventsState_signatureId } from './SecondedList'
import '../workorder/wosteps/WoDetail.scss'

@connect(state => ({
  config: state.workflowConfig,
  myself: state.preload.myself
}))
class SecondedDetail extends Component {
  state = {
    isLoanable: true, //是否可借用
    id: '',
    assetUid: null,
    assetInfo: {},
    secondedInfo: {},
    isLoanable: true,

    from: '',
    eventState: '',
    role: '',
    isBtnDisabled: false,

    loading: false,
    userOrg: {},

    secondedBtnDisabled: false,
    notAllConfirmed: false,
    isDetail: false
  }

  preview = {
    title: '设备基本信息',
    brief: [
      { key: 'name', label: '设备名称' },
      { key: 'departNum', label: '设备编号' },
      { key: 'serialNum', label: '序列号' },
      { key: 'qrCode', label: '二维码' }
    ],
    hasMore: false,
    showMore: false
  }

  async componentDidMount() {
    await this.setLocationSearchParams()
    await this.setAssetInfo()
    await this.setSecondedInfo()
    this.getUserOrg()
  }

  setAssetInfo = async () => {
    this.setState({ loading: true })
    if (this.state.from !== 'list') {
      const { state } = this.props.location

      if (state && state.asset) {
        let assetInfo = state.asset

        this.setState({
          assetUid: assetInfo.uid,
          assetInfo
        })
      } else {
        let assetUid = this.props.params.assetUid
        this.setState({ assetUid })
        const resp = await rest.get(urls.assetOptRecord, { page: 0, pageSize: 1, assetUid })
        if (resp) {
          let assetInfo = resp.assetInfo
          this.setState({ assetInfo })
        }
      }
    }
  }

  nowStr = () => moment().format('YYYY-MM-DDTHH:mm:ss')

  async setSecondedInfo() {

    if (this.state.from === 'list') { //从列表页进
      let id = this.props.params.assetUid
      const { state } = this.props.location
      let secondedInfoResp = state && state.secondedInfo ? state.secondedInfo : await this.getAssetSecondedInfo(id)
      if (!secondedInfoResp) {
        message.error('未查到该设备信息！')
        return
      }

      this.setBtnDisabledOrNot(secondedInfoResp)

      this.setState({
        id: secondedInfoResp.id,
        secondedInfo: {
          ...secondedInfoResp,
          borrowedAt: moment(secondedInfoResp.borrowedAt).format('YYYY-MM-DD HH:mm:ss'),
          returnAt: moment(secondedInfoResp.returnAt).format('YYYY-MM-DD HH:mm:ss')
        },
        assetInfo: {
          name: secondedInfoResp.assetName,
          departNum: secondedInfoResp.assetDeptNum,
          serialNum: secondedInfoResp.assetSerialNum,
          qrCode: secondedInfoResp.assetQrCode
        }
      })
    } else { //从台账进
      const secondedInfoResp = await this.getAssetSecondedInfo(this.state.assetUid)
      let isLoanable = !(secondedInfoResp && secondedInfoResp.id) || secondedInfoResp.returnAt
      let notAllConfirmed = secondedInfoResp && secondedInfoResp.ackAll === false //借方或归还方，加上双方主任确认证实后为true，任何一方未确认为false
      isLoanable = notAllConfirmed ? !isLoanable : isLoanable
      this.setState({ isLoanable, notAllConfirmed })

      if (isLoanable) { //true，代表或者未查询到信息, 或者有归还时间，或者借出设备还未被三方证实，代表可借出
        this.setState({
          secondedInfo: {
            outDeptName: this.state.assetInfo.clinicalDeptName,
            outDeptUid: this.state.assetInfo.clinicalDeptUID,
            lenderName: this.props.myself && this.props.myself.userAccount.name,
            lenderUid: this.props.myself && this.props.myself.userAccount.uid,
            borrowedAt: this.nowStr()
          }
        }, () => {
          if (!this.state.secondedInfo.outDeptUid) {
            this.setState({
              secondedInfo: {
                ...this.state.secondedInfo,
                outDeptName: this.state.assetInfo.clinicalDeptName,
                outDeptUid: this.state.assetInfo.clinicalDeptUID
              }
            })
          }
        })
      } else {  //false，代表查到了借调订单，为归还状态
        this.setState({
          secondedInfo: {
            ...secondedInfoResp,
            returnAt: this.nowStr(),
            returnerAckName: this.props.myself && this.props.myself.userAccount.name,
            returnerAck: this.props.myself && this.props.myself.userAccount.uid
          }
        })
      }

      //如果借调的单子没有被借方或归还方，加上双方主任的共三方完全确认，该设备的借和还都不能继续，按钮置灰
      if (notAllConfirmed) {
        message.error(secondedInfoResp.message, 7)
        this.setState({
          id: secondedInfoResp.id,
          secondedInfo: {
            ...secondedInfoResp,
            borrowedAt: moment(secondedInfoResp.borrowedAt).format('YYYY-MM-DD HH:mm:ss'),
            returnAt: moment(secondedInfoResp.returnAt).format('YYYY-MM-DD HH:mm:ss')
          },
          assetInfo: {
            name: secondedInfoResp.assetName,
            departNum: secondedInfoResp.assetDeptNum,
            serialNum: secondedInfoResp.assetSerialNum,
            qrCode: secondedInfoResp.assetQrCode
          },
          secondedBtnDisabled: true
        })
      }

    }

    this.setState({ loading: false })
  }

  async getAssetSecondedInfo(id) {
    const secondedInfoResp = await rest.get(
      `${this.state.from === 'list' ? urls.secondedInfo : urls.secondedInfoInProcess}/${id}`
    )
    return secondedInfoResp
  }

  // 当前主任的逻辑判断: 判断自己是不是借用/归还人
  isSelfAck = secondedInfo => {
    const { myself } = this.props
    const { eventState } = this.state
    if (eventState === 'borrow') {
      return !secondedInfo.borrowerAck && secondedInfo.borrowerUid === myself.userAccount.uid
    }
    if (eventState === 'return') {
      return !secondedInfo.returnerAck && secondedInfo.returnerUid === myself.userAccount.uid
    }
  }

  setBtnDisabledOrNot(secondedInfo) {
    let { role, eventState } = this.state
    let isBtnDisabled = false
    if (role === 'DeptHead') {
      isBtnDisabled =
        (eventState === 'borrow' && (secondedInfo.borrowerSignatureStorageId && !this.isSelfAck(secondedInfo))) ||
        (eventState === 'lend' && secondedInfo.lenderSignatureStorageId) ||
        (eventState === 'takeBack' && secondedInfo.returnerSignatureStorageId) ||
        (eventState === 'return' && (secondedInfo.receiverSignatureStorageId && !this.isSelfAck(secondedInfo)))
    } else {
      isBtnDisabled =
        (eventState === 'borrow' && secondedInfo.borrowerAck) ||
        (eventState === 'return' && secondedInfo.returnerAck)
    }

    this.setState({ isBtnDisabled })
  }

  setLocationSearchParams() {
    const params = new URLSearchParams(location.search)
    let [from, role, eventState] = [
      params.get('from'),
      params.get('role'),
      params.get('eventState'),
    ]

    let isLoanable = this.state.isLoanable
    let isDetail = this.state.isDetail
    if (from === 'list') {
      isLoanable = eventState === 'lend' || eventState === 'borrow' || eventState === 'borrowDetail' || eventState === 'lendDetail'
      isDetail = eventState === 'borrowDetail' || eventState === 'lendDetail'
    }
    this.setState({ from, role, eventState, isLoanable, isDetail })
  }

  getUserOrg = async () => {
    if (this.state.from !== 'list') {
      let orgUid
      if (this.props.params.assetUid) { // 通过资产获取院区借调的组织结构
        orgUid = await rest.get(`${urls.getOrgByAssetUid}/${this.props.params.assetUid}`)
      }
      if (!orgUid && this.props.myself) { // 获取用户的组织结构
        const { userAccount: { hospitalUID } } = this.props.myself
        orgUid = hospitalUID
      }
      if (orgUid) {
        const res = await rest.get(`${urls.org}/${orgUid}`, '', true)
        res && this.setState({ userOrg: res })
      }
    }
  }

  genApplicationFormConfig = () => {
    let { isLoanable } = this.state

    return {
      groups: [
        {
          title: '科室借调信息',
          items: [
            { key: 'outDeptName', label: '借出科室', component: 'Label' },
            {
              key: isLoanable ? 'lenderName' : 'returnerAckName',
              label: isLoanable ? '借出人' : '归还受理人',
              component: 'Label'
            },
            {
              key: isLoanable ? 'inDeptUid' : 'inDeptName',
              label: isLoanable ? '借用科室' : '归还科室',
              component: isLoanable ? 'SelectOrgNew' : 'Label',
              rule: { required: true },
              props: {
                disableOrg: this.justHospitalDepartmentEnable,
                showOrg: this.showDepartmentAndAboveOrg,
                org: this.state.userOrg,
                drawerHeight: 80
              },
              // transform: org => org.clinicalDeptId
              onChange: (query, formVal, org) => {
                this.setState({
                  secondedInfo: {
                    ...this.state.secondedInfo,
                    inDeptName: org.name
                  }
                })
              },
              transform: org => org.clinicalDeptUID
            },
            {
              key: isLoanable ? 'borrowerUid' : 'returnerUid',
              label: isLoanable ? '借用人' : '归还人',
              component: 'SelectUser',
              query: { orgUid: 'inDeptUid' },
              rule: { required: true },
              transform: user => {
                if (isLoanable) {
                  this.setState({
                    secondedInfo: { ...this.state.secondedInfo, borrowerName: user.name }
                  })
                } else {
                  this.setState({
                    secondedInfo: { ...this.state.secondedInfo, returnerName: user.name }
                  })
                }

                return user.uid
              }
            },
            isLoanable ?
              {
                key: 'borrowedAt',
                label: '借出时间',
                component: 'DateTime',
                rule: { required: true },
                clearable: true
              }
              : {
                key: 'borrowedAt',
                label: '借出时间',
                component: 'Label'
              },
            !isLoanable && {
              key: 'returnAt',
              label: '归还时间',
              component: 'DateTime',
              rule: { required: true, gt: 'borrowedAt' },
              clearable: true
            },
            {
              key: isLoanable ? 'lendRemark' : 'returnRemark',
              label: '备注',
              component: 'TextArea',
              props: { placeholder: '请填写备注', rows: 4 }
            }
          ].filter(Boolean)
        }
      ],
      buttons: [
        {
          type: 'primary',
          label: '提交',
          confirm: `您确定申请${isLoanable ? '借出' : '归还'}此设备吗？`,
          validation: true,
          onClick: this.submitWithSignature.bind(this)
        },
        {
          type: 'warn',
          label: '取消',
          onClick: () => {
            this.props.router.goBack()
          }
        }
      ]
    }
  }


  genNotAllConfirmedFormConfig = () => {
    let { isLoanable } = this.state

    return {
      groups: [
        {
          title: '科室借调信息',
          items: [
            { key: 'outDeptName', label: '借出科室', component: 'Label' },
            {
              key: isLoanable ? 'lenderName' : 'receiverName',
              label: isLoanable ? '借出人' : '归还受理人',
              component: 'Label'
            },
            {
              key: 'inDeptName',
              label: isLoanable ? '借用科室' : '归还科室',
              component: 'Label'
            },
            {
              key: isLoanable ? 'borrowerName' : 'returnerName',
              label: isLoanable ? '借用人' : '归还人',
              component: 'Label'
            },
            {
              key: isLoanable ? 'borrowedAt' : 'returnAt',
              label: isLoanable ? '借出时间' : '归还时间',
              component: 'Label'
            },
            {
              key: isLoanable ? 'lendRemark' : 'returnRemark',
              label: '备注',
              component: 'Label'
            }
          ]
        }
      ],
      buttons: [
        {
          type: 'primary',
          label: '提交',
        },
        {
          type: 'warn',
          label: '取消',
        }
      ]
    }
  }

  showDepartmentAndAboveOrg = (item) => {
    return item.orgType < 5
  }

  justHospitalDepartmentEnable = (item) => {
    return ![4].includes(item.orgType) || item.uid === this.state.assetInfo.clinicalDeptUID
  }

  genAcknowledgeFormConfig = () => {
    let { isLoanable, isBtnDisabled, isDetail, secondedInfo, eventState, role } = this.state
    const showReject = secondedInfo.cancelable && eventState === 'lendDetail' && role === 'DeptHead'
    return {
      groups: [
        {
          title: '科室借调信息',
          items: [
            { key: 'outDeptName', label: '借出科室', component: 'Label' },
            {
              key: isLoanable ? 'lenderName' : 'receiverName',
              label: isLoanable ? '借出人' : '归还受理人',
              component: 'Label'
            },
            {
              key: 'inDeptName',
              label: isLoanable ? '借用科室' : '归还科室',
              component: 'Label'
            },
            {
              key: isLoanable ? 'borrowerName' : 'returnerName',
              label: isLoanable ? '借用人' : '归还人',
              component: 'Label'
            },
            {
              key: isLoanable ? 'borrowedAt' : 'returnAt',
              label: isLoanable ? '借出时间' : '归还时间',
              component: 'Label'
            },
            {
              key: isLoanable ? 'lendRemark' : 'returnRemark',
              label: '备注',
              component: 'Label'
            }
          ]
        }
      ],
      buttons: [
        !isDetail && {
          type: 'primary',
          label: '确认',
          confirm: '您确定此次借调完成吗？',
          props: { disabled: isBtnDisabled },
          validation: false,
          onClick: this.acknowledgeAndSignature.bind(this)
        },
        showReject && {
          type: 'warn',
          label: '拒绝',
          confirm: '确定拒绝此次借调吗？',
          onClick: () => this.reject(secondedInfo.id)
        },
        {
          type: 'normal',
          label: '退出',
          props: { style: { color: 'unset', overflow: 'unset' } },
          onClick: () => {
            this.props.router.goBack();
          }
        }
      ].filter(Boolean)
    }
  }

  reject = async (uid) => {
    const res = await rest.listPost(`${urls.secondedInfo}/cancel/${uid}`)
    if (res.bizStatusCode === 'OK') {
      message.info('此借调已经被拒绝。')
      this.props.router.goBack();
    }
  }

  lessThenCurrent(val) {
    if (val.borrowedAt && moment(val.borrowedAt).isAfter(moment())) {
      message.warn('借调时间不能晚于当前时间')
      return false
    }
    return true
  }

  submitWithSignature(val) {
    const roleList = new Set(this.props.myself.userRoleList.map(r => r.name))
    if ((this.state.from === 'list' && this.state.role === 'DeptHead') ||
      (this.state.from !== 'list' && roleList.has('DeptHead'))) {
      sign('请您先签名', (objectStorageId) => {
        this.eventSubmit(val, objectStorageId)
      })
    } else {
      this.eventSubmit(val)
    }
  }

  async eventSubmit(val, objectStorageId) {
    let { assetInfo, secondedInfo } = this.state
    // if (this.lessThenCurrent(secondedInfo)) {

    let params = {}

    if (this.state.isLoanable) {
      params = {
        assetUid: assetInfo.uid,
        assetName: assetInfo.name,
        assetDeptNum: assetInfo.departNum,
        assetSerialNum: assetInfo.serialNum,
        assetQrCode: assetInfo.qrCode,
        ...val,
        ...secondedInfo,
        outSiteUid: assetInfo.hospitalUID,
        outSiteName: assetInfo.hospitalName || assetInfo.hospitalNameV2,
        borrowedAt: moment(val.borrowedAt || secondedInfo.borrowedAt).format('YYYY-MM-DD HH:mm:ss'),
        objectStorageId
      }

      if (!params.outDeptUid) {
        message.error('外借设备必须要有借出科室，否则不允许借出！')
        return
      }

      const resp = await rest.post(urls.createSeconded, params)
      if (resp && resp.id) {
        message.success('申请成功')
        browserHistory.replace('/wx?tab=cards')
      } else {
        message.success('申请失败')
      }
    } else {
      params = {
        returnerUid: val.returnerUid,
        returnerName: secondedInfo.returnerName,
        returnRemark: val.returnRemark,
        returnAt: moment(val.returnAt || secondedInfo.returnAt).format('YYYY-MM-DD HH:mm:ss'),
        objectStorageId
      }

      const resp = await rest.put(`${urls.createSeconded}/${secondedInfo.id}`, params)
      if (resp && resp.id) {
        message.success('申请成功')
        browserHistory.replace('/wx?tab=cards')
      } else {
        message.success('申请失败')
      }
    }
  }

  async acknowledgeAndSignature() {

    if (this.state.role === 'DeptHead' && !this.state.secondedInfo[eventsState_signatureId[this.state.eventState]]) {
      sign('请您先签名', (objectStorageId) => {
        let params = {
          objectStorageId,
          allocationId: this.state.secondedInfo.id,
          role: eventsState_role[this.state.eventState]
        }

        rest.listPost(`${urls.secondedSignature}`, params).then((resp) => {
          // console.log('resp', resp)
          if (resp.bizStatusCode === 'OK') {
            message.success('签名后确认成功')
            browserHistory.replace('/wx?tab=cards')
          } else {
            message.error('签名后确认失败')
          }
        })
      })
    } else {
      let url = this.state.eventState === 'borrow' ? urls.borrowerAcknowledge : urls.returneeAcknowledge
      rest.listPut(`${url}?id=${this.state.secondedInfo.id}`).then((resp) => {
        // console.log('resp', resp)
        if (resp.bizStatusCode === 'OK') {
          message.success('确认成功')
          browserHistory.replace('/wx?tab=cards')
        } else {
          message.error('确认失败')
        }
      })
    }
  }

  render() {
    let { assetInfo, secondedInfo, from, loading, isBtnDisabled, notAllConfirmed } = this.state

    let formConfig
    if (from === 'list') {
      formConfig = this.genAcknowledgeFormConfig()
    } else if (!notAllConfirmed) {
      formConfig = this.genApplicationFormConfig()
    } else {
      formConfig = this.genNotAllConfirmedFormConfig()
    }

    return (
      <div className={this.state.secondedBtnDisabled ? "disable_sub_btn" : ""}>
        <Spin spinning={loading}>
          <Preview {...this.preview} data={assetInfo} />
          {!secondedInfo.enableSimple && <ProcessRecords data={secondedInfo.process} />}
          {Object.keys(secondedInfo).length > 0 && <Form {...formConfig} value={secondedInfo} />}
          {isBtnDisabled &&
            <div style={{ color: 'red', fontSize: '0.875rem', padding: '10px 15px' }}>
              注：借调工单已经被确认
            </div>}
        </Spin>
      </div>
    )
  }

}

// const getLatestNodeName = (process) => {
//   let sortedProcess = process.slice().sort((a, b) => new Date(b.operatorTime) - new Date(a.operatorTime));

//   // 获取第一个operatorTime非null的节点
//   let latestNode = sortedProcess.find(item => item.operatorTime !== null);

//   return latestNode ? latestNode.nodeName : '';
// }

// const columns = [
//   {
//     title: '节点名称',
//     dataIndex: 'nodeName',
//     key: 'nodeName',
//   },
//   {
//     title: '确认人',
//     dataIndex: 'operator',
//     key: 'operator',
//   },
//   {
//     title: '时间',
//     dataIndex: 'operatorTime',
//     key: 'operatorTime',
//   }
// ];

const title = '流程节点记录'

function ProcessRecords(props) {
  const { data } = props
  const onClick = () => {
    Modal.info({
      title,
      content: <List
        itemLayout='horizontal' // 水平布局模式
        dataSource={data}
        renderItem={item => (
          <List.Item>
            <List.Item.Meta
              title={item.nodeName}   //节点名称
              description={`时间: ${item.operatorTime ? item.operatorTime : '暂无'}`} //确认人和时间
            />
            <div>{item.operator}</div>
          </List.Item>
        )}
      />
      // content: <Table
      //   dataSource={data}
      //   columns={columns}
      //   rowKey={'nodeName'}
      //   pagination={false}
      //   bordered
      // />
    })
  }
  if (Array.isArray(data) && data.length > 0) {
    const latestNodeName = '' // getLatestNodeName(data)
    return (
      <Cells>
        <Cell access onClick={onClick}>
          <CellBody>{title}</CellBody>
          <CellFooter>{latestNodeName}</CellFooter>
        </Cell>
      </Cells>
    )
  } else {
    return null
  }
}

export default SecondedDetail
