import React from 'react'
import queryString from 'query-string'

import './components.less'
import PdfView from '@/containers/ib/PdfView'

const IframeBox = props => {
  return (
    <div className={props.data ? 'iframe-box iframe-show' : 'iframe-box'}>
      {props.data ? creatViewDom(props.data) : null}
    </div>
  )
}
const creatViewDom = data => {
  if (data && ~data.indexOf('/wx/ib/pdfview')) {
    let locationQuery = queryString.parse(data.split('?')[1])
    const locationObj = { query: locationQuery, pathname: data.split('?')[0] }
    return <PdfView location={locationObj} />
  } else if (data) {
    return <iframe src={data} width="100%" height="100%" />
  }
}

export default IframeBox
