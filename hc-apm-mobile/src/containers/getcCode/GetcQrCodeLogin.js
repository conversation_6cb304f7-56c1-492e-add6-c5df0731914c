import React, { useState } from 'react';
import { Spin, message, Input, Button, Icon, Modal, Alert } from 'antd';
import { urls, rest } from '@/constants';
import { connect } from 'react-redux';
import './GetcQrCodeLogin.less';
import logo from '@/images/gehealthcare/logo_h.svg';

// 修改后的权限判断函数
const hasPermission = (myself) => {
  if (!myself || !myself.userAccount) return false
  const { geUserType, tags } = myself.userAccount
  return [1, 41].includes(geUserType) || (tags && tags.includes('super')) || (tags && tags.includes('pfe'))
  // FE 1, 41，或者tags数组中包含'super'，则有权限
  //增加 tags 数组里面 pfe 标签也有权限
}

const getSystemInfo = async (systemId) => {
  try {
    const response = await rest.get(`${urls.getECinfo}${systemId}`);
    if (response) {
      return response;
    } else {
      message.error('未查询到该设备的信息');
      return null;
    }
  } catch (error) {
    console.error('获取系统信息失败:', error);
    message.error('获取系统信息失败');
    return null;
  }
};

const login = async (clientId) => {
  try {
    const response = await rest.get(`${urls.socketLogin}?clientId=${clientId}`);
    console.log('socket client login', response);
    if (response) {
      message.success('扫码登录成功');
      return true;
    } else {
      message.error('扫码登录失败');
      return false;
    }
  } catch (error) {
    console.error('登录失败:', error);
    message.error('登录失败，请重试');
    return false;
  }
};

const addDevice = async (clientId, systemId, captcha) => {
  try {
    const response = await rest.get(`${urls.noticeAddDevice}?clientId=${clientId}&systemId=${systemId}&captcha=${captcha}`);
    console.log('add device', response);
    if (response) {
      message.success('设备添加成功');
      return true;
    } else {
      // message.error('设备添加失败');
      return false;
    }
  } catch (error) {
    console.error('添加设备失败:', error);
    message.error('添加设备失败，请重试');
    return false;
  }
};

const GetcQrCodeLogin = (props) => {
  const { location: { query }, myself } = props;
  const [systemId, setSystemId] = useState('');
  const [systemInfo, setSystemInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [addDeviceModalVisible, setAddDeviceModalVisible] = useState(false);

  // 检查用户是否有权限
  if (!hasPermission(myself)) {
    return (
      <div id="getcCode">
        <img src={logo} className="logo" />
        <div className="title mr153">装备守护登录验证</div>
        <Alert
          message="权限不足"
          description="当前用户无法使用扫码登录功能"
          type="error"
          showIcon
          style={{ marginTop: '20px' }}
        />
      </div>
    );
  }

  const handleSystemIdChange = (e) => {
    setSystemId(e.target.value);
    setHasSearched(false);
  };

  const handleSearch = async () => {
    if (!systemId) {
      message.warning('请输入System ID');
      return;
    }

    setLoading(true);
    setHasSearched(true);
    const info = await getSystemInfo(systemId);
    setLoading(false);

    if (info) {
      setSystemInfo(info);
    } else {
      setSystemInfo(null);
    }
  };

  const showLoginConfirm = () => {
    setLoginModalVisible(true);
  };

  const handleLoginConfirm = async () => {
    setLoginModalVisible(false);

    if (!query || !query.clientId) {
      message.error('缺少必要的参数，请重新扫描二维码');
      return;
    }

    setLoading(true);
    const success = await login(query.clientId);
    setLoading(false);

    if (success) {
      setIsLoggedIn(true);
    }
  };

  const handleLoginCancel = () => {
    setLoginModalVisible(false);
  };

  const showAddDeviceConfirm = () => {
    setAddDeviceModalVisible(true);
  };

  const handleAddDeviceConfirm = async () => {
    setAddDeviceModalVisible(false);

    if (!query || !query.clientId || !systemId) {
      message.error('缺少必要的参数');
      return;
    }

    setLoading(true);
    await addDevice(query.clientId, systemId, systemInfo.captcha);
    setLoading(false);
  };

  const handleAddDeviceCancel = () => {
    setAddDeviceModalVisible(false);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // 初始界面 - 只显示登录按钮
  if (query && query.clientId && !isLoggedIn) {
    return (
      <div id="getcCode">
        <img src={logo} className="logo" />
        <div className="title mr153">装备守护登录验证</div>
        <div className="login-section">
          <Button
            type="primary"
            size="large"
            block
            onClick={showLoginConfirm}
            loading={loading}
            className="purple-btn"
          >
            确认登录
          </Button>
        </div>

        <Modal
          title="登录确认"
          visible={loginModalVisible}
          onOk={handleLoginConfirm}
          onCancel={handleLoginCancel}
          okText="确认"
          cancelText="取消"
          okButtonProps={{ className: 'purple-btn' }}
        >
          <p>您确定要登录装备守护系统吗？</p>
        </Modal>
      </div>
    );
  }

  // 登录后的界面 - 显示输入、查询和结果
  return (
    <div id="getcCode">
      <img src={logo} className="logo" />
      {loading && (
        <div className="loading-box mr153">
          <Spin />
        </div>
      )}
      {!loading && (
        <div>
          {!query.clientId && <div className="title mr153">（离线扫码登录）</div>}
          <div className="title mr153">装备守护设备录入</div>

          <div className="info">
            <div className="info-title">请输入System ID</div>
            <div className="input-section">
              <Input
                placeholder="请输入System ID"
                value={systemId}
                onChange={handleSystemIdChange}
                onPressEnter={handleSearch}
                style={{ width: '70%' }}
              />
              <Button
                type="primary"
                onClick={handleSearch}
                loading={loading}
                style={{ marginLeft: '10px' }}
                className="purple-btn"
              >
                查询
              </Button>
            </div>

            {systemInfo && (
              <div className="system-info">
                <div className="info-title">请确认以下设备信息</div>
                <div className="info-body">
                  <div className="label">医院名称:</div>
                  <div className="value">{systemInfo.hospitalName}</div>
                </div>
                <div className="info-body">
                  <div className="label">Modality:</div>
                  <div className="value">{systemInfo.modality}</div>
                </div>
                <div className="info-body">
                  <div className="label">System ID:</div>
                  <div className="value">{systemInfo.systemId}</div>
                </div>
                <div className="info-body">
                  <div className="label">设备名称:</div>
                  <div className="value">{systemInfo.psiDescription}</div>
                </div>
                {systemInfo.modality === 'CT' &&
                <div className="info-body">
                  <div className="label">抓数命令:</div>
                  <div className="value">{systemInfo.fetchDataCommand}</div>
                </div>
                }
                <div className="title mr153">安装验证码</div>
                <div className="title">{systemInfo.captcha}</div>

                {!query || !query.clientId ? (
                  <React.Fragment>
                    {systemInfo.loginName && (
                      <div className="info-body">
                        <div className="label">用户名:</div>
                        <div className="value">{systemInfo.loginName}</div>
                      </div>
                    )}

                    {systemInfo.password && (
                      <div className="info-body">
                        <div className="label">密码:</div>
                        <div className="value password-field">
                          {showPassword ? systemInfo.password : '••••••••'}
                          <span className="password-toggle" onClick={togglePasswordVisibility}>
                            <Icon type={showPassword ? 'eye-invisible' : 'eye'} />
                          </span>
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                ) : (
                  <div className="login-section">
                    <Button
                      type="primary"
                      size="large"
                      block
                      onClick={showAddDeviceConfirm}
                      loading={loading}
                      className="purple-btn"
                    >
                      添加设备
                    </Button>
                  </div>
                )}
              </div>
            )}

            {!systemInfo && !loading && systemId && hasSearched && (
              <div className="err-box">
                <p>
                  未查询到<span style={{ color: '#F38367' }}>{systemId}</span>设备的信息
                </p>
                <p>请确认您输入的System ID正确无误</p>
              </div>
            )}
          </div>
        </div>
      )}

      <Modal
        title="您确定要添加此设备吗？"
        visible={addDeviceModalVisible}
        onOk={handleAddDeviceConfirm}
        onCancel={handleAddDeviceCancel}
        okText="确认"
        cancelText="取消"
        okButtonProps={{ className: 'purple-btn' }}
      >
        {systemInfo && (
          <div>
            <p>System ID: {systemInfo.systemId}</p>
            <p>医院名称: {systemInfo.hospitalName}</p>
            <p>设备名称: {systemInfo.psiDescription}</p>
          </div>
        )}
      </Modal>
    </div>
  )
};

export default connect(state => ({
  myself: state.preload.myself
}))(GetcQrCodeLogin);
