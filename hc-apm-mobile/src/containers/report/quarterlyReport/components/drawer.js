import React, { useState, useEffect } from 'react'
import { rest, urls } from '@constants'
import '../quarterly.less'
import { But<PERSON>, Drawer } from 'antd'

const LinkDrawer = props => {

  const closeDrawer = () => {
    props.setVisible(false)
  }

  return (
    <Drawer
        title={
          <div onClick={closeDrawer}>返回</div>
        }
        placement="bottom"
        closable={false}
        onClose={closeDrawer}
        destroyOnClose
        visible={props.visible}
        height={props.height || '100%'}
        className='quarterlyDrawer'
      >
        <div style={{ height: '100%'}}>
          <iframe
            src={`${props.linkurl}`}
            frameBorder="0"
            height={'100%'}
            width={'100%'}
            scrolling="auto"
            sandbox="allow-forms allow-scripts allow-same-origin allow-popups allow-downloads"
          ></iframe>
        </div>
      </Drawer>
  )
}

export { LinkDrawer }
