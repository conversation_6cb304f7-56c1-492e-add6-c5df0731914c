import React from "react";
import moment from "moment";
import { connect } from 'react-redux'
import {browserHistory as history} from "react-router";
import { ActionSheet, Accordion, Card, InputItem, List } from 'antd-mobile'
import {Form as GeneralForm, sign, Attachment, ImageUploader} from "@components";
import {rest, route_urls, util, urls} from "@constants";
import './approvalsList.scss'

const workOrderTypeLabel = {
  1: '内部'
}

@connect(state => ({ config: state.workflowConfig }))
class ApprovalDetail extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showType: undefined,
      multiLevelApproval:null,
      multiLevelApprovals: [],
      partsRequestLineItems: [],
      assetInfoApproveList:[],
      formVal: {},
      repairs:[],
      borrowList:[],
      receiveList:[],
      workOrderLists:[],
      pmOrderLists:[],
      workOrderDetails:[],
      borrowReturnList:[],
      receiveReturnList:[],
      transferAssetList:[],
      assetScrap: null,
      approvalOpinion: null
    }
  }

  componentWillMount() {
    let showType = this.props.location.query.showType;
    let itemId = this.props.params.id
    rest.get(urls.approvalsDetail + '/' + itemId).then(data => {
      this.setState({...data,showType})
    })
    // console.log(this.state)
  }

  chooseNodeNames = () => {
    return new Promise((resolve, reject) => {
      const { approvalNodes } = this.state
      if (Array.isArray(approvalNodes) && approvalNodes.length > 0) {
        const nodeNames = approvalNodes.map(item => item.nodeName)
        nodeNames.push('取消')
        ActionSheet.showActionSheetWithOptions({
          message: '请选择回退到:',
          cancelButtonIndex: nodeNames.length - 1,
          options: nodeNames
        }, index => {
          resolve(nodeNames[index])
        })
      }
    })
  }

  async submitWithSignature(val, type) {
    const wfConfig = this.props.config.get('workflowConfig')
    if (type === 'return') {
      const nodeName = await this.chooseNodeNames()
      if (nodeName === '取消') {
        return
      }
      val.nodeName = nodeName
    }
    const submit = v => {
      if (type === 'agree') {
        this.submitEvent(v, urls.approvalsAgree)
      } else if (type === 'disagree') {
        this.submitEvent(v, urls.approvalsDisagree)
      } else if (type === 'return') {
        this.submitEvent(v, urls.approvalsReturn)
      } else if (type === 'direct') {
        this.submitEvent(v, urls.approvalsDirect)
      }
    }
    if (wfConfig && wfConfig.partsNeedSign) {
      sign('请您先签名', (objectId) => {
        val.auditSignature = objectId
        submit(val)
      })
    } else {
      submit(val)
    }
  }

  submitEvent(val, eventUrl) {
    Object.assign(val, this.state.formVal)
    let itemId = this.props.params.id;
    rest.post(eventUrl + '/' + itemId , val).then( res => {
      if (res) { // res可以是succes和failure
        history.replace({ pathname: route_urls.msg, query: { code: 201 }, state: { desc: '审核数据已保存', next: route_urls.home_wx } })
      }
    }).catch((error) => {
      history.replace({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
    })
  }

  formatDateBeforDisplay = (date) => {
    if (date == undefined) return null;
    return moment(date).format('YYYY-MM-DD')
  }

  formatDateTime = (date) => {
    if (date == undefined) return null;
    return moment(date).format('YYYY-MM-DD HH:mm:ss')
  }

  displayswapLevelName = (approvalLevelNow) => {
    let result = '';
    if(approvalLevelNow == -1) {
      result =  '审核不通过'
    } else if(approvalLevelNow == 0) {
      result = '审核已通过';
    } else {
      result = approvalLevelNow + '级审核人待审批中';
    }
    return result
  }

  getApprovalNodeStatus = (data) => {
    return util.preload().val({ key: 'approvalNodeStatus', type: 'approvalNodeStatus' }, data)
  }

  getPartsRequestStatus(status) {
    let result = '';
    if(status == 1) {
      if(this.state.multiLevelApproval.businessType == '1' || this.state.multiLevelApproval.businessType == '12')
        result = '已出库';
      if(this.state.multiLevelApproval.businessType == '2')
        result = '已入库';
    } else {
      if(this.state.multiLevelApproval.businessType == '1' || this.state.multiLevelApproval.businessType == '12')
        result = '未出库';
      if(this.state.multiLevelApproval.businessType == '2')
        result = '未入库';
    }
    return result;
  }

  onChange(approvalOpinion) {
    this.setState(approvalOpinion)
  }

  renderMultiLevelApproval(multiLevelApproval) {
    if(multiLevelApproval) {
      return(
        <Accordion defaultActiveKey="0" >
          <Accordion.Panel header={'审批单详情'} >
            <Card full>
              <Card.Footer content={'审批单号'} extra={multiLevelApproval.approvalNumber} />
              <Card.Footer content={'业务单号'} extra={multiLevelApproval.businessNumber} />
              <Card.Footer content={'业务类型'} extra={multiLevelApproval.businessTypeName} />
              <Card.Footer content={'处理人'} extra={multiLevelApproval.actualApproverName} />
              <Card.Footer content={'总审批进度'} extra={this.displayswapLevelName(multiLevelApproval.approvalLevelNow)} />
              <Card.Footer content={'审批时间'} extra={this.formatDateBeforDisplay(multiLevelApproval.actualApproverDate)} />
            </Card>
          </Accordion.Panel>
        </Accordion>
      )
    }
  }

  showDirectDelivery() {
    const { multiLevelApprovals } = this.state
    if (Array.isArray(multiLevelApprovals) && multiLevelApprovals.length > 1) {
      const last = multiLevelApprovals[multiLevelApprovals.length - 2]
      if (last && last.approvalNodeStatus === 4) {
        return true
      }
    }
    return false
  }

  renderMultiLevelApprovals(multiLevelApprovals) {
    if (multiLevelApprovals && multiLevelApprovals.length > 0) {
      return (
        <Accordion defaultActiveKey="0" >
          <Accordion.Panel header={'审批节点记录'} >
            {this.renderApprovalsList(multiLevelApprovals)}
          </Accordion.Panel>
        </Accordion>
      )
    }
  }

  renderApprovalsList(multiLevelApprovals) {
    return multiLevelApprovals.map((item) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>{item.approvalLevel + '级审核人'}</div>
          </Card.Body>
          <Card.Footer content='审核级别' extra={item.approvalLevel + '级审核人'}/>
          <Card.Footer content='处理人' extra={item.actualApproverName}/>
          <Card.Footer content='审批意见' extra={item.approvalOpinion}/>
          <Card.Footer content='审批节点状态' extra={this.getApprovalNodeStatus(item)}/>
          <Card.Footer content='审批时间' extra={this.formatDateBeforDisplay(item.actualApproverDate)}/>
          <Attachment title="审批附件" items={item.attachments} />
        </React.Fragment>
      )
    })
  }

  renderPartsRequestLineItems() {
    return (
      <Accordion defaultActiveKey="0" >
        <Accordion.Panel header={'审批内容'} >
          {this.renderPartsRequestList(this.state.partsRequestLineItems)}
          {this.renderAssetInfoApproveList(this.state.assetInfoApproveList)}
          {this.renderRepairs(this.state.repairs)}
          {this.renderBorrowList(this.state.borrowList)}
          {this.renderReceiveList(this.state.receiveList)}
          {this.renderWorkOrderLists(this.state.workOrderLists)}
          {this.renderPmOrderLists(this.state.pmOrderLists)}
          {this.renderWorkOrderDetails(this.state.workOrderDetails)}
          {this.renderBorrowReturnList(this.state.borrowReturnList)}
          {this.renderReceiveReturnList(this.state.receiveReturnList)}
          {this.renderransferAssetList(this.state.transferAssetList)}
          {this.renderAssetScrapRequest(this.state.assetScrap)}
          {this.renderAssetDepartmentConvert(this.state.assetDepartmentConvert)}
          {this.renderBusinessObject(this.state.businessObject)}
        </Accordion.Panel>
      </Accordion>
    )
  }

  renderPartsRequestList(partsRequestLineItems) {
    return Array.isArray(partsRequestLineItems) && partsRequestLineItems.length>0 && partsRequestLineItems.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>备件{index+1}</div>
          </Card.Body>
          <Card.Footer content='备件名称' extra={item.name}/>
          <Card.Footer content='备件数量' extra={item.requestQty}/>
          <Card.Footer content='备件单价' extra={item.unitPrice}/>
          <Card.Footer content='备件总价' extra={item.totalPrice}/>
          <Card.Footer content='申请时间' extra={this.formatDateBeforDisplay(item.proposerDate)}/>
          <Card.Footer content='出入库状态' extra={this.getPartsRequestStatus(item.status)}/>
        </React.Fragment>
      )
    })
  }

  renderAssetInfoApproveList(assetInfoApproveList) {
    return Array.isArray(assetInfoApproveList) && assetInfoApproveList.length>0 && assetInfoApproveList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>修改记录{index+1}</div>
          </Card.Body>
          <Card.Footer content='资产名称' extra={item.assetInfoName}/>
          <Card.Footer content='修改字段' extra={item.columnNameLable}/>
          <Card.Footer content='旧值' extra={item.oldValueLable}/>
          <Card.Footer content='新值' extra={item.newValueLable}/>
          <Card.Footer content='修改人' extra={item.createdBy}/>
          <Card.Footer content='修改时间' extra={this.formatDateBeforDisplay(item.createdDate)}/>
        </React.Fragment>
      )
    })
  }

  renderRepairs(repairs) {
    return Array.isArray(repairs) && repairs.length>0 && repairs.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>维修设备{index+1}</div>
          </Card.Body>
          <Card.Footer content='资产名称' extra={item.assetInfo.name}/>
          <Card.Footer content='制造商序列号' extra={item.assetInfo.serialNum}/>
          <Card.Footer content='设备编号' extra={item.assetInfo.departNum}/>
          <Card.Footer content='设备型号' extra={item.assetInfo.functionType}/>
          <Card.Footer content='工单类型' extra={item.intExtType}/>
          <Card.Footer content='申请时间' extra={this.formatDateBeforDisplay(item.applyDate)}/>
          <Card.Footer content='申请原因' extra={item.repairReason}/>
          <Card.Footer content='预修时间(天)' extra={item.rating}/>
        </React.Fragment>
      )
    })
  }

  renderBorrowList(borrowList) {
    return Array.isArray(borrowList) && borrowList.length>0 && borrowList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>申请{index+1}</div>
          </Card.Body>
          <Card.Footer content='申借部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='申请人' extra={item.applicantName}/>
          <Card.Footer content='借用中心' extra={item.applyCenterName}/>
          <Card.Footer content='申请日期' extra={this.formatDateBeforDisplay(item.applyTime)}/>
          <Card.Footer content='备注' extra={item.remark}/>
        </React.Fragment>
      )
    })
  }

  renderReceiveList(receiveList) {
    return Array.isArray(receiveList) && receiveList.length>0 && receiveList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>申请{index+1}</div>
          </Card.Body>
          <Card.Footer content='申借部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='申请人' extra={item.applicantName}/>
          <Card.Footer content='领用中心' extra={item.applyCenterName}/>
          <Card.Footer content='申请日期' extra={this.formatDateBeforDisplay(item.applyTime)}/>
          <Card.Footer content='申请单类型' extra={item.applyReceiveTypeName}/>
          <Card.Footer content='使用负责人' extra={item.useOwnerName}/>
          <Card.Footer content='备注' extra={item.remark}/>
        </React.Fragment>
      )
    })
  }

  renderBorrowReturnList(borrowReturnList) {
    return Array.isArray(borrowReturnList) && borrowReturnList.length>0 && borrowReturnList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>申请{index+1}</div>
          </Card.Body>
          <Card.Footer content='归还部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='申请人' extra={item.applicantName}/>
          <Card.Footer content='借用中心' extra={item.applyCenterName}/>
          <Card.Footer content='归还日期' extra={this.formatDateBeforDisplay(item.applyTime)}/>
          <Card.Footer content='备注' extra={item.remark}/>
        </React.Fragment>
      )
    })
  }

  renderReceiveReturnList(receiveReturnList) {
    return Array.isArray(receiveReturnList) && receiveReturnList.length>0 && receiveReturnList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>申请{index+1}</div>
          </Card.Body>
          <Card.Footer content='归还部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='申请人' extra={item.applicantName}/>
          <Card.Footer content='领用中心' extra={item.applyCenterName}/>
          <Card.Footer content='归还日期' extra={this.formatDateBeforDisplay(item.applyTime)}/>
          <Card.Footer content='申请单类型' extra={item.applyReceiveTypeName}/>
          <Card.Footer content='使用负责人' extra={item.useOwnerName}/>
          <Card.Footer content='备注' extra={item.remark}/>
        </React.Fragment>
      )
    })
  }

  renderransferAssetList(transferAssetList) {
    return Array.isArray(transferAssetList) && transferAssetList.length>0 && transferAssetList.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Body>
            <div>转移设备{index+1}</div>
          </Card.Body>
          <Card.Footer content='设备名称' extra={item.name}/>
          <Card.Footer content='设备型号' extra={item.functionType}/>
          <Card.Footer content='所属部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='维修负责人' extra={item.assetOwnerName}/>
        </React.Fragment>
      )
    })
  }

  renderWorkOrderLists(workOrderLists) {
    const goDetail = item => {
      history.push({ pathname: `/wx/workorder/${item.id}`})
    }
    return Array.isArray(workOrderLists) && workOrderLists.length>0 && workOrderLists.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Footer content='资产名称' extra={item.assetInfo.name}/>
          <Card.Footer content='制造商序列号' extra={item.assetInfo.serialNum}/>
          <Card.Footer content='设备编号（院方）' extra={item.assetInfo.departNum}/>
          <Card.Footer content='设备型号' extra={item.assetInfo.functionType}/>
          <Card.Footer content='所属部门' extra={item.serviceRequest.fromDeptName}/>
          <Card.Footer content='报修单号' extra={<a onClick={() => goDetail(item)}>{item.serviceRequest.srNum}</a>}/>
          <Card.Footer content='报修人' extra={item.serviceRequest.requestorName}/>
          <Card.Footer content='报修时间' extra={this.formatDateBeforDisplay(item.serviceRequest.requestTime)}/>
          <Card.Footer content='工单类型' extra={workOrderTypeLabel[item.intExtType] ? workOrderTypeLabel[item.intExtType] : item.intExtType}/>
          <Card.Footer content='故障现象' extra={item.serviceRequest.requestReason}/>
          <Card.Footer content='问题描述' extra={item.patProblems}/>
          <Card.Footer content='解决方案' extra={item.patActions}/>
        </React.Fragment>
      )
    })
  }

  renderPmOrderLists(pmOrderLists) {
    return Array.isArray(pmOrderLists) && pmOrderLists.length>0 && pmOrderLists.map((item,index) =>{
      return(
        <React.Fragment>
          <Card.Footer content='业务单元' extra={item.siteName}/>
          <Card.Footer content='资产名称' extra={item.assetInfo.name}/>
          <Card.Footer content='制造商序列号' extra={item.assetInfo.serialNum}/>
          <Card.Footer content='设备编号（院方）' extra={item.assetInfo.departNum}/>
          <Card.Footer content='计量等级' extra={item.ruleLevel}/>
          <Card.Footer content='保养类型' extra={item.pmTypeName}/>
          <Card.Footer content='所属部门' extra={item.clinicalDeptName}/>
          <Card.Footer content='创建人' extra={item.creatorName}/>
          <Card.Footer content='负责人' extra={item.ownerName}/>
          <Card.Footer content='保养编号' extra={item.pmNum}/>
          <Card.Footer content='当前处理步骤' extra={item.currentStepName}/>
          <Card.Footer content='工单类型' extra={item.intExtTypeName}/>
          <Card.Footer content='计划完成时间' extra={this.formatDateBeforDisplay(item.planTime)}/>
        </React.Fragment>
      )
    })
  }

  renderAssetScrapRequest = assetScrap => {
    const { assetInfo } = this.state
    const status = assetInfo ? util.preload().val({key: 'status', type: 'assetStatus'}, assetInfo) : ''
    const equipmentDisposalMethod = assetScrap? util.preload().val({key: 'equipmentDisposalMethod', type: 'equipmentDisposalMethod'}, assetScrap) : ''
    if (assetScrap) {
      return (
        <React.Fragment>
          <Card.Footer content='设备名称' extra={assetInfo.name}/>
          <Card.Footer content='设备型号' extra={assetInfo.functionType}/>
          <Card.Footer content='设备编号' extra={assetInfo.departNum}/>
          <Card.Footer content='资产编号' extra={assetInfo.financingNum}/>
          <Card.Footer content='注册证号' extra={assetInfo.registrationNo}/>
          <Card.Footer content='制造商序列号' extra={assetInfo.serialNum}/>
          <Card.Footer content='所属科室' extra={assetInfo.clinicalDeptName}/>
          <Card.Footer content='安装位置' extra={assetInfo.locationName}/>
          <Card.Footer content='当前状态' extra={status}/>
          <Card.Footer content='采购价格' extra={assetInfo.purchasePrice}/>
          <Card.Footer content='报废单号' extra={assetScrap.businessNumber}/>
          <Card.Footer content='报废申请人' extra={assetScrap.createdBy}/>
          <Card.Footer content='报废申请时间' extra={assetScrap.createdDate}/>
          <Card.Footer content='报废理由' extra={assetScrap.applyReason}/>
          <Card.Footer content='意见建议' extra={assetScrap.approvalReason}/>
          <Card.Footer content='处置方式' extra={equipmentDisposalMethod}/>
          {assetScrap.equipmentDisposalMethod === '0' && <Card.Footer content='其他方式' extra={assetScrap.customDisposalMethod}/>}
          <ImageUploader title="报废图片" previewMode files={assetScrap.attachments || []}/>
        </React.Fragment>
      )
    }
  }

  renderBusinessObject = businessObject => {
    if (businessObject) {
      const { types } = businessObject

      return (
        <div className="business-object">
          <Card.Footer content='申请人' extra={businessObject.applicantName}/>
          <Card.Footer content='申请科室' extra={businessObject.clinicalDeptName}/>
          <Card.Footer content='申请时间' extra={this.formatDateTime(businessObject.applyTime)}/>
          <Card.Footer content='调剂名称' extra={
            <List>
              {types.map(type =>
                <List.Item>
                  <span className="swap-name">{type.swapName} ({type.applyQty})</span>
                </List.Item>
              )}
            </List>
          }/>
          <Card.Footer content='备注' extra={businessObject.remark}/>
        </div>
      )
    }
  }

  renderAssetDepartmentConvert = assetConvert => {
    const { assetInfo } = this.state
    if (assetConvert) {
      return (
        <React.Fragment>
          <Card.Footer content='设备名称' extra={assetConvert.assetName}/>
          <Card.Footer content='设备型号' extra={assetConvert.functionType}/>
          <Card.Footer content='设备编号' extra={assetConvert.assetDeptNum}/>
          <Card.Footer content='资产编号' extra={assetConvert.financingNum}/>
          <Card.Footer content='采购价格' extra={assetInfo.purchasePrice}/>
          <Card.Footer content='转科单号' extra={assetConvert.businessNumber}/>
          <Card.Footer content='转出科室' extra={assetConvert.outDeptName}/>
          <Card.Footer content='转入科室' extra={assetConvert.inDeptName}/>
          <Card.Footer content='转科申请人' extra={assetConvert.transferOutName}/>
          <Card.Footer content='转科理由' extra={assetConvert.applyReason}/>
          <Card.Footer content='转科审批人' extra={assetConvert.approvalBy}/>
          <Card.Footer content='审批时间' extra={assetConvert.approvalDate}/>
          <Card.Footer content='审批备注' extra={assetConvert.approvalReason}/>
        </React.Fragment>
      )
    }
  }

  renderWorkOrderDetails(workOrderDetails) {
    return Array.isArray(workOrderDetails) && workOrderDetails.length>0 && workOrderDetails.map((item,index) =>{
      return (
        <React.Fragment>
          <Card.Body>
            <div>备件{index+1}</div>
          </Card.Body>
          <Card.Footer content='备件名称' extra={item.parts}/>
          <Card.Footer content='数量' extra={item.apartsQuantity}/>
          <Card.Footer content='预估单价(元)' extra={item.partsPrice}/>
          <Card.Footer content='合同价格(元)' extra={item.closedPrice}/>
          <Card.Footer content='备件类别' extra={item.partsTypeName}/>
          <Card.Footer content='序列号' extra={item.serialNum}/>
          <Card.Footer content='发票号' extra={item.invoiceNum}/>
          <Card.Footer content='质保期' extra={this.formatDateBeforDisplay(item.warrantyDate)}/>
          <Card.Footer content='申请时间' extra={this.formatDateBeforDisplay(item.applyTime)}/>
          <Card.Footer content='预计到达时间' extra={this.formatDateBeforDisplay(item.estimatedArriveTime)}/>
          <Card.Footer content='到达确认时间' extra={this.formatDateBeforDisplay(item.arrivedTime)}/>
          <Card.Footer content='是否到货' extra={item.arrived ? '已到货':'未到货'}/>
          <Card.Footer content='审批状态' extra={item.approvalStatusName}/>
          <Attachment title="备件附件" items={item.attachments} />
        </React.Fragment>
      )
    })
  }

  // displayApprovedStatus(approved) {
  //   if (null === approved) {
  //     return '未审核'
  //   }

  //   return (approved ? '通过' : '不通过')
  // }

  renderApprovalOpinion() {
    let showType = this.state.showType;
    if(showType == '0') {
      return <InputItem
        placeholder={'审批意见'}
        type="text"
        editable={true}
        value={this.state.approvalOpinion}
        onChange={approvalOpinion => this.onChange({ approvalOpinion })}
        style={{ textAlign: 'right' }}
      >审批意见</InputItem>
    } else {
      return (
        <div/>
      )
    }
  }

  showReturnDirect = () => {
    if (this.state.assetScrap || this.state.assetDepartmentConvert) {
      return true
    } else if (Array.isArray(this.state.workOrderLists) && this.state.workOrderLists.length > 0) {
      return true
    } else
      return false
  }

  renderButton() {
    this.state.showType
    const groups = [
      {
        items: this.state.showType == 0 ? [
          { key: 'approvalOpinion', label: '审批意见', props: { placeholder: '请输入审批意见' } },
          { key: 'attachments', label: '审批附件', component: 'Attachment' }
        ] : []
      }
    ]
    let buttons = [
      this.showReturnDirect() && this.showDirectDelivery() && { props: { size: 'small' }, type: 'primary', label: '直送', validation: true, onClick: val => this.submitWithSignature(val, 'direct') },
      { props: { size: 'small' }, type: 'primary', label: '同意', validation: true, onClick: val => this.submitWithSignature(val, 'agree') },
      { props: { size: 'small' }, type: 'warn', label: '不同意', validation: true, onClick: val => this.submitWithSignature(val, 'disagree') },
      this.showReturnDirect() && { props: { size: 'small' }, type: 'warn', label: '退回', validation: true, onClick: val => this.submitWithSignature(val, 'return') }
    ].filter(Boolean);
    // 非待审批状态不显示任何审批按钮
    if (this.state.multiLevelApproval && this.state.multiLevelApproval.approvalNodeStatus !== 0) {
      buttons = []
    }
    let showType = this.state.showType;
    if(showType == '0') {
      return (
        <GeneralForm value={this.state.formVal} groups={groups} buttons={buttons} onChange={formVal => this.setState({ formVal })}/>
      );
    } else {
      return (
        <div/>
      )
    }
  }

  render() {
    return (
      <React.Fragment className="approval-detail-and-audit">
        {this.renderMultiLevelApproval(this.state.multiLevelApproval)}
        {this.renderMultiLevelApprovals(this.state.multiLevelApprovals)}
        {this.renderPartsRequestLineItems()}
        {/* {this.renderApprovalOpinion()} */}
        {this.renderButton()}
        <div style={{ height: 20 }}/>
      </React.Fragment>
    );
  }
}

export default ApprovalDetail;
