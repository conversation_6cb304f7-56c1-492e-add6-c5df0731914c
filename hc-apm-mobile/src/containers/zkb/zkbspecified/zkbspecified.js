import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import queryString from 'query-string'
import { SearchBar } from 'antd-mobile'
import { Switch } from 'antd'
import { browserHistory as history } from 'react-router'
import { APMFilter, APMFilterItem } from '@/components/APMFilter'
import PullToRefreshList from '@/containers/ib/common/PullToRefreshList'

import './zkbspecified.less'
import { rest, route_urls, urls } from '@/constants'
import { useQuery, usePaging } from '../zkbList/hooks'
function ZkbList(props) {
  const [sort, setSort] = useState(false)
  const [query, setQuery] = useQuery({})
  const [{ data, total }, next] = usePaging({
    url: query.type === 'specified' ? urls.queryZkbIntehralOrderList : urls.zkbFePartPage,
    query: query,
    content: ['type'],
    type: 'post'
  })
  const onChange = filter => {
    console.log('onChange', filter)
    const {
      zkbPoSteps,
      zkbPoStatus,
      zkbPartOrderType,
      zkbFePartOrder_orderType,
      zkbFePartOrder_steps,
      zkbFePartOrder_status,zkbIntegralOrderType,zkbIntegralOrderSteps,zkbIntegralOrderStatus
    } = filter
    setQuery({
      ...query,
      currentStep: zkbIntegralOrderSteps && zkbIntegralOrderSteps[0],
      status: zkbIntegralOrderStatus && zkbIntegralOrderStatus[0],
      orderType: zkbIntegralOrderType && zkbIntegralOrderType[0]
    })
  }

  const eventChangeQuery = value => {
    setQuery({
      ...query,
      search: value
    })
  }
  const orderBy = e => {
    setQuery({
      ...query,
      sort: e ? 'asc' : 'desc'
    })
  }
  const getTitle = type => {
    {
      /* 步骤： zkbPoSteps
    状态：zkbPoStatus
    订单类型：zkbPartOrderType */
    }
    {
      /* 'zkbFePartOrderSteps',
        'zkbFePartOrderStatus',
        'zkbFePartOrderOrderType' */
    }
    if (!query) return
    const zkb = {
      type: 'zkbPartOrderType',
      step: 'zkbPoSteps',
      status: 'zkbPoStatus'
    }
    const ge = {
      type: 'zkbFePartOrder_orderType',
      step: 'zkbFePartOrder_steps',
      status: 'zkbFePartOrder_status'
    }
    const specified = {
      type: 'zkbIntegralOrderType',
      step: 'zkbIntegralOrderSteps',
      status: 'zkbIntegralOrderStatus'
    }
    if (query.type === 'zkb') return zkb[type]
    if (query.type === 'ge') return ge[type]
    if (query.type === 'specified') return specified[type]
  }
  const getData = () => {
    return {
      currentStep: query.type === 'specified' ? props.zkbIntegralOrderSteps : props.zkbFePartOrder_steps,
      status: query.type === 'specified' ? props.zkbIntegralOrderStatus : props.zkbFePartOrder_status,
      orderType: query.type === 'specified' ? props.zkbIntegralOrderType : props.zkbFePartOrder_orderType
    }
  }
  return (
    <div className="zkb-list">
      <div className="list-header">
        <SearchBar
          defaultValue={query.search}
          placeholder="搜索代理商/申请单编号/工单编号/systemID"
          showCancelButton
          cancelText="搜索"
          onSubmit={e => eventChangeQuery(e)}
          onClear={e => eventChangeQuery('')}
          onCancel={e => eventChangeQuery(e)}
        />
      </div>

      <APMFilter onChange={e => onChange(e)}>
        <APMFilterItem
          name={getTitle('step')}
          label="当前步骤"
          type={getTitle('step')}
          level={1}
          value={getInitAc(query, 'currentStep')}
        />
        <APMFilterItem
          name={getTitle('status')}
          label="当前状态"
          type={getTitle('status')}
          level={1}
          value={getInitAc(query, 'status')}
        />
        <APMFilterItem
          name={getTitle('type')}
          label="类型"
          type={getTitle('type')}
          level={1}
          value={getInitAc(query, 'orderType')}
        />
      </APMFilter>
      <div className="zkb-body-header">
        <p>
          订单列表(<span>{total}</span>)
        </p>
        {/* ZKB FE维修/GE医疗 FE维修 */}
        {query && query.type === 'zkb' && <p>ZKB FE维修</p>}
        {query && query.type === 'ge' && <p>GE HealthCare FE维修</p>}
        {query && query.type === 'specified' && <p>积分申请</p>}
        <div>
          创建时间
          <Switch size="small" onChange={orderBy} checked={query.sort === 'asc' ? true : false} />
        </div>
      </div>
      <div className="zkb-body">
        <PullToRefreshList onNext={next} total={total}>
          {data &&
            data.map((item, index) => {
              return (
                <CardList
                  jump={id => {
                    query.type === 'zkb' && history.push({ pathname: '/wx/zkb', query: { id: id } })
                    query.type === 'ge' && history.push({ pathname: '/wx/zkbfepartorder', query: { id: id } })
                    query.type === 'specified' && history.push({ pathname: '/wx/zkbnewapply', query: { ordernum: id } })
                  }}
                  key={index}
                  data={item}
                  select={getData()}
                />
              )
            })}
        </PullToRefreshList>
      </div>
    </div>
  )
}

function getInitAc(query, name) {
  return Array.isArray(query[name])
    ? query[name].map(item => item.toString())
    : query[name]
    ? [query[name].toString()]
    : []
}

const CardList = props => {
  const getChName = (data, type) => {
    let _d = props.select[type].filter(item => item.msgKey === data)
    return _d && _d.length !== 0 ? _d[0].valueZh : data
  }
  return (
    <div
      className="card-list"
      onClick={() => {
        props.jump && props.jump(props.data.id)
      }}
    >
      <div className="card-header">
        <div className="card-hos wordBreak">{props.data.hospitalName}</div>
        {/* <div className="card-order-type wordBreak typeOne">订单类型:{getChName(props.data.orderType, 'orderType')}</div> */}
      </div>
      <div className="card-body ">
        <div className="card-body-list">
          <div className="wordBreak typeTwo Cblack">{props.data.systemId}</div>
          <div className="wordBreak typeTwo Cblack">{`${
            props.data.integralOrderNo ? props.data.integralOrderNo : ''
          }`}</div>
        </div>
        <div className="card-body-list ">
          {/* {props.data.assetTypeDi && <div className="wordBreak typeTwo">{props.data.assetTypeDi}</div>}
          {props.data.artificialType && <div className="wordBreak typeTwo">{props.data.artificialType}</div>} */}
          {/* <div className="wordBreak typeTwo">
            订单编号:
            {`${props.data.integralOrderNo ? props.data.integralOrderNo : ''}`}
          </div> */}
          <div className="wordBreak typeTwo"> 当前步骤:{getChName(props.data.currentStep, 'currentStep')}</div>
          <div className="wordBreak typeTwo"> 当前状态:{getChName(props.data.status, 'status')}</div>
        </div>

        <div className="card-body-list">
          <div className="wordBreak typeTwo">订单类型:{getChName(props.data.orderType, 'orderType')}</div>
        </div>
      </div>
    </div>
  )
}
function mapStateToProps(state) {
  return {
    zkbPoSteps: state.preload.zkbPoSteps,
    zkbPoStatus: state.preload.zkbPoStatus,
    zkbPartOrderType: state.preload.zkbPartOrderType,
    zkbFePartOrder_steps: state.preload.zkbFePartOrder_steps,
    zkbFePartOrder_status: state.preload.zkbFePartOrder_status,
    zkbFePartOrder_orderType: state.preload.zkbFePartOrder_orderType,
    zkbIntegralOrderSteps: state.preload.zkbIntegralOrderSteps,
    zkbIntegralOrderStatus: state.preload.zkbIntegralOrderStatus,
    zkbIntegralOrderType: state.preload.zkbIntegralOrderType
  }
}
export default connect(mapStateToProps)(ZkbList)
