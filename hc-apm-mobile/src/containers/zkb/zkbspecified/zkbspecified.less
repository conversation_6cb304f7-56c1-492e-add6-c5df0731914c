.zkb-list {
  height : 100%;
  // overflow: auto;
  .zkb-body-header {
    display         : flex;
    justify-content : space-around;
    padding         : 5px;
    background-color: white;
    border-bottom   : 1px solid rgb(243, 243, 243);

    >p {
      margin: unset !important;

      >span {
        color: red
      }
    }
  }
}

.zkb-body {
  height: 100%;
  overflow: auto;
}

.card-list {
  padding         : 2px 10px;
  background-color: white;
  border-bottom   : 1px solid rgb(243, 243, 243);
  margin-bottom : 10px;
  .card-header {
    display        : flex;
    justify-content: space-between;

    .card-hos {
      // width: 66%;
      color : #00507D;
      font-size: 20px;
    }
    border-bottom: 1px solid #eee;
  }

  .card-body {
    .card-body-list {
      // width      : 33%;
      display: flex;
      align-items: center;
      margin     : auto;
      padding    : 5px 0;
      >div {
        width: 50%;
        // padding: 3px 0px;
      }
    }
  }
}

.wordBreak {
  word-break        : break-all;
  display           : -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow          : hidden;
}

.typeTwo {
  text-align: left;
}

.typeOne {
  text-align: right;
}
.Cblack {
  color: #000;
}
