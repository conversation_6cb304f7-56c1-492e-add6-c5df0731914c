import React, { Component, useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { defaultmsg, defaultapply, defaulttype, zkbContract, zkbgd } from './default.js'
import { browserHistory as history } from 'react-router'
import { Row, Col, Form, Input, Button, Select, DatePicker, Card, Modal, Alert } from 'antd'
import { rest, urls } from '@/constants'
import ImageUploader from '../../../components/ImageUploader'
import DefaultCard from '../Card'
import moment from 'moment'
import ApplyCard from '../zkbApply/ApplyCard'
import queryString from 'query-string'
import { download } from '../index.js'

import './index.less'
const { TextArea } = Input
const { confirm } = Modal
const dateFormat = 'YYYY/MM/DD HH:mm:ss'
const integralOrder = '/gateway/hcapmassetservice/api/apm/zkb/integralOrder/'
const ZkbNewApply = props => {
  const [data, setData] = useState(false)
  const [loading, setloading] = useState(false)
  const [editremark, seteditremark] = useState('')
  const [engineerQualificationsFlag, setengineerQualificationsFlag] = useState('')
  useEffect(() => {
    getList()
  }, [])
  const getList = () => {
    const urlParams = new URLSearchParams(location.search)
    const ordernum = urlParams.get('ordernum')
    // GE工程师现场维修、GE应用支持、GE工程师现场保养   id:   2c99858294687803019468a0ba770010
    rest.get(`${integralOrder}${ordernum}`).then(res => {
      setData(res)
    })
  }
  const inputChange = e => {
    console.log(e.target.value)
    seteditremark(e.target.value)
  }
  const submit = flag => {
    //     auditFlag  审批通过/不通过
    // remark 备注
    // haveTrainCourseFlag 是否参加培训课
    // dataFlag

    confirm({
      content: '是否确认',
      cancelText: '取消',
      okText: '确认',
      onOk() {
        setloading(true)
        const urlParams = new URLSearchParams(location.search)
        const ordernum = urlParams.get('ordernum')
        rest
          .put(integralOrder + ordernum, {
            auditFlag: flag,
            remark: editremark,
            engineerQualificationsFlag: engineerQualificationsFlag,
            dataFlag: data.zkbIntegralOrder.dataFlag
          })
          .then(res => {
            setloading(false)
            console.log(res, '')
            getList()
          })
      },
      onCancel() {}
    })
  }
  return (
    <div className="apply">
      <div className="title">积分申请审批</div>
      <div className="body">
        {data &&
          data.messageList &&
          data.messageList.length !== 0 &&
          data.messageList.map((i, index) => {
            return <Alert key={index} type="warning" showIcon description={i} />
          })}
        {data && data.zkbIntegralOrder && (
          <DefaultCard title={'基础信息'} label={defaultmsg} data={[data.zkbIntegralOrder]} />
        )}
        {data && data.zkbIntegralOrder.orderType !== 'ToolBox' && data.workInfo && (
          <DefaultCard title={'工单信息'} label={zkbgd} data={[data.workInfo]} />
        )}
        {data && data.zkbContract && data.zkbIntegralOrder.orderType !== 'ToolBox' && (
          <DefaultCard title={'合同信息'} label={zkbContract} data={[data.zkbContract]} />
        )}
        {data && data.zkbIntegralOrder && data.zkbIntegralOrder.orderType !== 'ToolBox' && (
          <DefaultCard title={'申请信息'} label={defaultapply} data={[data.zkbIntegralOrder]} />
        )}
        {data && data.zkbIntegralOrder && data.zkbIntegralOrder.orderType === 'ToolBox' && (
          <DefaultCard title={'申请信息'} label={defaulttype} data={[data.zkbIntegralOrder]} />
        )}
        {data && data.blobObjectList && data.blobObjectList.length !== 0 && (
          <Card title="附件信息" className="card" bordered={false}>
            <Row gutter={16}>
              {data.blobObjectList &&
                data.blobObjectList.map(item => {
                  return (
                    <Col lg={6} md={12} sm={24} key={item.objectName}>
                      <a href="#" onClick={() => download(item.objectStorageId)}>
                        {item.objectName}
                      </a>
                    </Col>
                  )
                })}
            </Row>
          </Card>
        )}
        {data && (data.showManage || data.showCommercial || data.showEngineer || data.auditFlag === '0') && (
          <Card title="审批信息" className="card" bordered={false}>
            {data && data.showManage && (
              <Row gutter={16} type="flex" justify="center">
                <Col xs={24}>
                  <Form.Item label="区域经理审批备注">
                    <TextArea disabled value={data.zkbIntegralOrder.manageRemark} row={4} />
                  </Form.Item>
                  <Form.Item label="区域经理审批时间">
                    <DatePicker
                      disabled
                      format={dateFormat}
                      value={moment(
                        data.zkbIntegralOrder.manageAuditDate ? data.zkbIntegralOrder.manageAuditDate : null,
                        dateFormat
                      )}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="区域经理审批人名称">
                    <Input disabled value={data.zkbIntegralOrder.manageAuditer} />
                  </Form.Item>
                </Col>
              </Row>
            )}
            {data && data.showCommercial && (
              <Form>
                <Form.Item label="Commercial审批备注">
                  <TextArea disabled value={data.zkbIntegralOrder.commercialRemark} row={4} />
                </Form.Item>
                <Form.Item label="Commercial审批时间">
                  <DatePicker
                    disabled
                    format={dateFormat}
                    value={moment(
                      data.zkbIntegralOrder.commercialAuditDate ? data.zkbIntegralOrder.commercialAuditDate : null,
                      dateFormat
                    )}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item label="Commercial审批人">
                  <Input disabled value={data.zkbIntegralOrder.commercialAuditer} />
                </Form.Item>
              </Form>
            )}
            {data && data.showEngineer && (
              <Form>
                <Form.Item label="工程师是否有资质">
                  <Select value={data.zkbIntegralOrder.engineerQualificationsFlag} disabled style={{ width: '100%' }}>
                    <Option value={'t'} key={'t'}>
                      是
                    </Option>
                    <Option value={'f'} key={'f'}>
                      否
                    </Option>
                  </Select>
                </Form.Item>
                <Form.Item label="工程师审批备注">
                  <TextArea disabled value={data.zkbIntegralOrder.engineerRemark} row={4} />
                </Form.Item>
                <Form.Item label="工程师审批时间">
                  <DatePicker
                    disabled
                    format={dateFormat}
                    value={moment(
                      data.zkbIntegralOrder.engineerAuditDate ? data.zkbIntegralOrder.engineerAuditDate : null,
                      dateFormat
                    )}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item label="工程师审批人">
                  <Input disabled value={data.zkbIntegralOrder.engineerAuditer} />
                </Form.Item>
              </Form>
            )}
            {data && data.auditFlag === '0' && (
              <Row gutter={16} type="flex" justify="center">
                <Col xs={24}>
                  {data && data.showHaveTrainCourseFlag && (
                    <Form.Item label="工程师是否有资质">
                      {/* 工程师是否有资质   engineerQualificationsFlag */}
                      <Select
                        value={engineerQualificationsFlag}
                        style={{ width: '100%' }}
                        onChange={value => {
                          setengineerQualificationsFlag(value)
                        }}
                      >
                        <Option value={'t'} key={'t'}>
                          是
                        </Option>
                        <Option value={'f'} key={'f'}>
                          否
                        </Option>
                      </Select>
                    </Form.Item>
                  )}
                  <Form.Item label="审批信息">
                    <TextArea
                      value={editremark}
                      maxLength={255}
                      rules={[
                        {
                          required: true,
                          message: 'Please input your msg!'
                        }
                      ]}
                      onChange={e => inputChange(e)}
                      row={4}
                    />
                  </Form.Item>
                </Col>
                {true && (
                  <Form.Item label="" style={{ textAlign: 'center' }}>
                    <Button
                      type={'danger'}
                      style={{ marginRight: '10px' }}
                      loading={loading}
                      onClick={() => submit(false)}
                    >
                      不通过
                    </Button>
                    <Button
                      type={'primary'}
                      loading={loading}
                      disabled={engineerQualificationsFlag && engineerQualificationsFlag === 'f'}
                      onClick={() => submit(true)}
                      style={{ marginLeft: '10px' }}
                    >
                      通过
                    </Button>
                  </Form.Item>
                )}
              </Row>
            )}
          </Card>
        )}
      </div>
    </div>
  )
}

export default ZkbNewApply
