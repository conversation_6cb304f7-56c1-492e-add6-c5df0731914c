import React from 'react'
import { connect } from 'react-redux'
import { browserHistory as history } from 'react-router'
import { Form, FormCell, CellHeader, Label, CellBody, Input, CellFooter, Icon, Dialog, Switch } from 'react-weui'
import { List, CheckBox, SelectOrg, SelectOrgNew, Select, Form as GeneralForm } from '../../components'
import { route_urls, rest, urls, util } from '../../constants'
import CancelPmOrders from './CancelPmOrders.js'
import { Button, message } from 'antd'
import UrlPattern from 'url-pattern'
import { SearchBar } from 'antd-mobile'


const LEVEL = {
  0: 'tenantUID',
  1: 'institutionUID',
  2: 'hospitalUID',
  3: 'siteUID'
}
class UnAcceptPmOrderList extends React.Component {

  constructor(props) {
    super(props)
    this.state = {
      loading: false,
      hasMore: false,
      org: null,
      page: 0,
      conditions: [
        { label: '我的工单', value: 0 },
        { label: '可抢工单', value: 1 }
      ],
      currentCons: props.location.query.con || 0,
      request: {
        title: '工单列表',
        count: 0,
        data: [],
        selectable: () => true,
        onSelect: selected => this.setState({ selectedItems: selected }),
        itemHeader: { key: 'pmNum', label: '保养编号' },
        itemContent: [
          { key: 'assetInfo.siteName', label: '所属院区' },
          { key: 'assetName', label: '资产名称' },
          { key: 'assetInfo.departNum', label: '设备编号' },
          { key: 'assetInfo.functionType', label: '设备型号' },
          { key: 'assetInfo.serialNum', label: '序列号' },
          { key: 'assetInfo.manufacture', label: '厂商' },
          { key: 'planTime', label: '计划时间' },
          { key: 'currentStepName', label: '当前步骤' },
          { key: 'ruleLevel', label: '保养等级', type: 'ruleLevel' },
          { key: 'ruleName', label: '保养规则' },
          { key: 'assetInfo.clinicalDeptName', label: '所属科室' },
          { key: 'assetInfo.emergencyEquipment', label: '急救设备' , type: 'emergencyEquipment'},
        ]
      },
      queryParam: {
        ruleLevel: null,
        clinicalId: null,
        planTime: null,
        assetName: '',
      },
      selectedItems: [],
      selectAll: false,
      showDialog: false,
      form: {
        planTime: ''
      }
    }
    this.eventClick = this.eventClick.bind(this)
    this.eventOnMore = this.eventOnMore.bind(this)
    this.pmToPickup = this.pmToPickup.bind(this)
    this.eventChangeQuery = this.eventChangeQuery.bind(this)
  }

  componentWillMount() {
    const { myself } = this.props
    const orgUID = myself.userAccount[LEVEL[myself.userAccount.orgLevel]]
    if (orgUID) {
      rest.get(urls.org + '/visible/' + orgUID).then(org => this.setState({ org }))
      this.pmToPickup(this.state.currentCons)
    }
  }

  searchSubmit(keyword) {
    let queryParam = this.state.queryParam
    queryParam.assetName = keyword
    this.setState({queryParam}, () => {
      this.pmToPickup(this.state.currentCons)
    })
  }

  pmToPickup(con) {
    this.setState({ loading: true, page: 0 })
    history.replace(route_urls.home_wx + '/unAcceptPmList?con=' + con)
    rest.list(urls.pmOrderToPickUp, { ...this.state.queryParam, type: con, page: 0 })
      .then(res => {
        this.state.request.data = res.data
        this.state.request.count = res.rowCount
        this.state.hasMore = res.data.length == 10
        this.state.loading = false
        this.setState(this.state)
      })
  }

  eventClick(pm) {
    const { queryParam } = this.state
    let { id } = pm
    history.push({
      pathname: new UrlPattern(route_urls.wx.pmorder).stringify({ id }),
      query: { from: 'repairList' },
      state: {
        ...this.props.location.state,
        pm,
        queryParam
      }
    })
    // history.push({ pathname: new UrlPattern(route_urls.wx.pmorder).stringify({ id }), query: { from: 'repairList' } })
  }

  eventOnMore() {
    this.state.page++
    this.setState(this.state, () => this.loadMoreData())
  }

  loadMoreData() {
    this.setState({ loading: true })
    rest.list(urls.pmOrderToPickUp, { ...this.state.queryParam, type: this.state.currentCons, page: this.state.page })
      .then(res => {
        this.state.request.data = this.state.request.data.concat(res.data)
        this.state.hasMore = res.data.length == 10
        this.state.loading = false
        this.setState(this.state)
      })
  }

  eventChangeQuery(e) {
    this.state.queryParam = { ...this.state.queryParam, ...e }
    this.setState(this.state, () => this.pmToPickup(this.state.currentCons))
  }

  deletePlanTime() {
    this.eventChangeQuery({ planTime: '' })
  }

  renderButtons() {
    return (
      <div style={{ width: '100vw', display: 'flex', justifyContent: 'space-around', position: 'fixed', bottom: 10 }}>
        <Button type="normal" icon="check-circle-o" onClick={() => this.submitAll()}>全部接单</Button>
        <Button type="primary" icon="exclamation-circle-o" onClick={() => this.submitPart()}>批量接单</Button>
        <CancelPmOrders items={this.state.selectedItems} />
      </div>
    )
  }

  submitAll() {
    let selectAll = this.state.request.data
    if (selectAll.length == 0) {
      message.info('没有需要接单的保养工单', 2)
      return
    }
    this.setState({ showDialog: true, selectAll: true })
  }
  submitPart() {
    if (this.state.selectedItems.length == 0) {
      message.info('没有需要接单的保养工单', 2)
      return
    }
    this.setState({ showDialog: true })
  }
  submit(val) {
    this.setState({ showDialog: false })
    const reload = () => util.reload()  //window.location.reload()
    const error = err => history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })

    if (this.state.selectAll) {
      // 批量接单
      const queryObj = { ...this.state.queryParam, type: this.state.currentCons }
      const params = Object.keys(queryObj).filter(key => queryObj[key] !== null).map(key => `${key}=${queryObj[key]}`).join('&')
      const content = {
        actionType: "accept",
        planTime: val.planTime
      }
      rest.put(`${urls.pmOrderBatchAccept}?${params}`, content)
        .then(reload)
        .catch(error)
    } else {
      const { selectedItems } = this.state
      let result = selectedItems.map(item => ({
        actionType: "accept",
        planTime: val.planTime || item.planTime,
        stepId: item.currentStepId,
        pmOrderId: item.id
      }))
      rest.put(urls.pmOrderBatch, result)
        .then(reload)
        .catch(error)
    }
  }

  generateForm() {
    let formTemplate = {
      buttons: [
        { type: 'primary', label: '接单', validation: true, onClick: val => this.submit(val) },
        {
          type: 'primary', label: '取消', onClick: val => {
            this.setState({ showDialog: false, selectAll: false })
          }
        }
      ],
      groups: [{
        items: [{
          key: 'planTime',
          label: '计划完成时间',
          component: 'Date'
        }
        ]
      }]
    }
    return formTemplate
  }

  disableOrg(item) {
    return ![3, 4].includes(item.orgType) // 只能选院区或科室
  }

  showClinicalHaveAssets(item) {
    return item.orgType === 4 ? item.count > 0 : true
  }

  onOrgSelect = e => {
    if (e.siteUID) {
      this.eventChangeQuery({ siteUID: e.siteUID, clinicalId: null })
    } else if (e.clinicalDeptUID) {
      this.eventChangeQuery({ siteUID: null, clinicalId: e.clinicalDeptUID })
    } else {
      this.eventChangeQuery({ siteUID: null, clinicalId: null })
    }
  }

  render() {
    return (
      <div style={{ paddingBottom: '40px' }}>
        <SearchBar
          placeholder='设备名称/设备编号/安装位置'
          showCancelButton
          cancelText="搜索"
          onSubmit={this.searchSubmit.bind(this)}
          onClear={this.searchSubmit.bind(this, '')}
          onCancel={this.searchSubmit.bind(this)}
        />
        <CheckBox data={this.state.conditions}
          defaultValue={this.state.currentCons}
          onCheck={currentCons => {
            this.setState({ currentCons })
            this.pmToPickup(currentCons)
          }} />
        <Form>
          <SelectOrgNew
            label="所属科室"
            style={{ height: '2.8rem', lineHeight: '2.8rem' }}
            org={this.state.org}
            // showOrg={this.showClinicalHaveAssets}
            showCount
            clearable
            disableOrg={this.disableOrg}
            onChange={this.onOrgSelect} />
          <Select item={{ key: 'ruleLevel', label: '保养等级', type: 'ruleLevel' }}
            onChange={e => this.eventChangeQuery({ ruleLevel: e })}
          />
          <FormCell>
            <CellHeader><Label>计划时间</Label></CellHeader>
            <CellBody>
              <Input
                type="date"
                value={this.state.queryParam.planTime}
                onChange={e => this.eventChangeQuery({ planTime: e.target.value })}
              />
            </CellBody>
            {!!this.state.queryParam.planTime &&
              <CellFooter>
                <Icon value="cancel" onClick={() => this.deletePlanTime()} />
              </CellFooter>
            }
          </FormCell>
          <FormCell>
            <CellHeader><Label>急救设备</Label></CellHeader>
            <CellBody style={{textAlign: 'right'}}>
              <Switch  onChange={(e) => this.eventChangeQuery({ emergencyEquipment: e.target.checked })}/>
            </CellBody>
          </FormCell>
        </Form>
        <List {...this.state.request} onClick={this.eventClick} loading={this.state.loading} hasMore={this.state.hasMore} onMore={this.eventOnMore} />
        {this.renderButtons()}
        {this.state.showDialog && <Dialog title="接单" show={this.state.showDialog} style={{ width: '98%', maxWidth: 'none' }}>
          <GeneralForm
            value={this.state.form}
            buttons={this.generateForm().buttons}
            groups={this.generateForm().groups}
          />
        </Dialog>}
      </div>
    )
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself
  }
}

export default connect(mapStateToProps)(UnAcceptPmOrderList)
