import React from 'react'
import { connect } from 'react-redux'
import UrlPattern from 'url-pattern'
import { browserHistory as history } from 'react-router'
import {
  CellBody,
  Cells,
  Cell,
  CellFooter,
  CellsTitle,
  Dialog as WCDialog,
  FormCell,
  CellHeader,
  Label,
  Icon,
  Toast,
  Input,
  Button,
  Switch
} from 'react-weui'
import Colored from '../../components/ColoredText'
import { urls, rest, route_urls, util } from '../../constants'
import { Attachment, Preview, Form as GeneralForm, Dialog, ImageUploader, genShortcut, SignatureImages } from '../../components'
import { WoCost } from '../workorder/wosteps/index'
import AssignApproverDialog from './AssignApproverDialog'
import { isNullOrEmpty, ellipsis } from 'noru-utils/lib'
import { sumBy } from 'lodash'
import { message, Icon as AntdIcon } from 'antd'
import { DEFAULT_FILE_TYPE } from '../../components/ImageUploader'
import './PmOrderDetail.scss'
import { isAppOffline } from '@/actions/jsApi'
import { setBoType, showBoTypeModal } from './PmOrderBoType'
import RepairContact from '@components/RepairContact'
import { downloadBlobFile } from '../../constants/util'

export const STEPS = {
  Create: 'Create',
  Assign: 'Assign',
  Accept: 'Accept',
  Maintain: 'Maintain',
  Acceptance: 'Acceptance',
  Close: 'Close',
  Closed: 'Closed'
}

const APPROVE_STATUS = {
  Unassign: '0',
  Pending: '1',
  Approved: '2',
  Rejected: '3',
  Partial: '4',
  MultiPending: '5'
}

const ACTION_TYPES = {
  Assign: 'assign',
  Accept: 'accept',
  Ack: 'ack',
  AckFailed: 'ackFailed',
  Close: 'close',
  Closed: 'Closed',
  Rate: 'rate',
  Approval: 'approval',
  Pass: 'pass',
  Reject: 'reject',
  Partial: 'partial',
  TempRepair: 'tempRepair',
  Repair: 'repair',
  ReAssign: 'reAssign',
  Simplify: 'simplify',
  Cancel: 'cancel',
  AcceptancePass: 'acceptancePass',
  AcceptanceReject: 'acceptanceReject'
}

const ACTION_DESC = {
  [ACTION_TYPES.Assign]: '派工',
  [ACTION_TYPES.Accept]: '接单',
  [ACTION_TYPES.TempRepair]: '保养信息暂存',
  [ACTION_TYPES.Repair]: '保养完成',
  [ACTION_TYPES.Approval]: '审核',
  [ACTION_TYPES.Pass]: '审核同意',
  [ACTION_TYPES.Reject]: '审核不同意',
  [ACTION_TYPES.Partial]: '审核部分同意',
  [ACTION_TYPES.Close]: '关单',
  [ACTION_TYPES.Closed]: '已关单',
  [ACTION_TYPES.ReAssign]: '转单',
  [ACTION_TYPES.Simplify]: '保养完成',
  [ACTION_TYPES.Cancel]: '取消',
  [ACTION_TYPES.AcceptancePass]: '验收通过',
  [ACTION_TYPES.AcceptanceReject]: '验收不通过'
}

class PmOrderDetail extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showMore: false,
      showToast: false,
      toastMessage: '',
      brief: [
        { key: 'assetInfo.departNum', i18n: 'departNum' },
        { key: 'assetInfo.name', i18n: 'assetName' },
        { key: 'assetInfo.functionType', i18n: 'functionType' },
        { key: 'assetInfo.status', i18n: 'status', type: 'assetStatus' },
        { key: 'assetInfo.orgPath', i18n: 'orgPath', label: '所属单位' },
        { key: 'assetInfo.clinicalDeptName', i18n: 'clinicalDeptName' }
      ],
      more: [
        { key: 'assetInfo.serialNum', i18n: 'serialNum' },
        { key: 'assetInfo.financingNum', i18n: 'financingNum' },
        { key: 'assetInfo.qrCode', i18n: 'QRCode' },
        { key: 'assetInfo.assetGroup', i18n: 'assetGroup', type: 'assetGroup' },
        { key: 'pmOrder.creatorName', i18n: 'creatorName' },
        { key: 'pmOrder.createTime', i18n: 'createTime' },
        { key: 'pmOrder.ruleLevel', label: '保养等级', type: 'ruleLevel' }
      ],
      pmOrder: {},
      assetInfo: {},
      msg: [],
      isProcessor: false,
      isOwner: false,
      index: -1,
      showForm: false,
      addPart: false,
      parts: [],
      addCoworker: false,
      coworkers: [],
      addApprover: false,
      planTime: null,
      endTime: null,
      manHours: null,
      attachmentsTwo: [],
      attachmentsTwoPreview: [],
      prePmAttachments: [],
      attachments: [],
      attachmentsAfterClosed: [],
      images: [],
      prePmImages: [],
      showAs: false,
      reassignFormTemplate: undefined,
      currentUserId: null,
      partsNeedSign: undefined,
      reAssignDescCompare: '工作需要',
      partTemplate: undefined,
      signatureFiles: [],
      pmAckNeedSign: false,
      pmWcNeedSign: false,
      actionType: null,
      hasConfigMultiLevelApproval: false,
      isMultiVerifyBtnDisabled: false,
      isGE_MVS: false
    }

    this.showReAssign = this.showReAssign.bind(this)
    this.reAssignToTempUser = this.reAssignToTempUser.bind(this)
    this.clonePart = this.clonePart.bind(this)
    this.handlePopState = this.handlePopState.bind(this);
  }

  componentDidMount() {
    this._init()
    this.unlisten = this.props.router.listenBefore((nextLocation) => {
      if (nextLocation.action === 'POP') {
        // 我们知道用户是通过后退按钮来到这个location的
        this.props.router.replace({ pathname: nextLocation.pathname, state: this.props.location.state })
      }
    });
  }

  componentWillUnmount() {
    if (this.unlisten) {
      this.unlisten();
    }
  }

  handlePopState() {
    const { from } = this.props.location.query
    let pathname = route_urls.home_wx
    if (from === 'repairList') {
      pathname = `${route_urls.home_wx}/unAcceptPmList`
    }
    if (from === 'pmList' && this.props.location.state && this.props.location.state.cons) {
      pathname = `${route_urls.home_wx}/consolePmList`
    }
    window.history.replace(pathname, this.props.location.state)
  };

  _init = async function () {
    let currentUserId = this.props.myself.userAccount.uid
    let pmOrder = await rest.get(new UrlPattern(urls.pmOrderDetail).stringify({ id: this.props.params.id }))
    let assetInfo = pmOrder.assetInfo
    const wfConfig = await rest.get(urls.workflowsConfig + `/${assetInfo.id}`)
    const approvalsConfig = await rest.get(urls.approvalsConfig + '/pmOrder' + `/${pmOrder.id}`)

    let isOwner = pmOrder.ownerId === currentUserId || this.props.myself.multiTenant
    let approvedParts = []
    let approvedParts1 = []
    if (pmOrder.currentStepId == STEPS.Maintain && pmOrder.approvalStatus == APPROVE_STATUS.Pending) {
      approvedParts = [].concat(
        pmOrder.details
          .filter(part => !part.approved)
          .map(part => {
            part.approved = false
            return part
          })
      )
      approvedParts1 = [].concat(pmOrder.details.filter(part => part.approved))
    }

    let signatures = []
    if (pmOrder.signature) {
      let file1 = {
        objectName: '审核人签名',
        original: '',
        thumbnail: '',
        objectStorageId: pmOrder.signature
      }
      signatures.push(file1)
    }
    if (pmOrder.ackSignature) {
      let file2 = {
        objectName: '验收人签名',
        original: '',
        thumbnail: '',
        objectStorageId: pmOrder.ackSignature
      }
      signatures.push(file2)
    }
    if (pmOrder.wcSignature) {
      let file3 = {
        objectName: '保养完成签名',
        original: '',
        thumbnail: '',
        objectStorageId: pmOrder.wcSignature
      }
      signatures.push(file3)
    }

    this.setState({
      pmOrder,
      assetInfo,
      isOwner,
      comments: pmOrder.comments,
      isProcessor: pmOrder.currentPersonId === currentUserId || this.props.myself.multiTenant,
      currentUserId: this.props.myself.userAccount.id,
      actionType: pmOrder.currentStepId,
      parts: [].concat(pmOrder.details),
      coworkers: [].concat(pmOrder.coworkers),
      planTime: pmOrder.planTime,
      showForm: isOwner && pmOrder.approvalStatus !== APPROVE_STATUS.Pending,
      endTime: pmOrder.endTime,
      manHours: pmOrder.manHours,
      images: !!pmOrder.attachments && pmOrder.attachments,
      attachments: !!pmOrder.attachments && pmOrder.attachments,
      prePmImages: !!pmOrder.prePmAttachments && pmOrder.prePmAttachments,
      prePmAttachments: !!pmOrder.prePmAttachments && pmOrder.prePmAttachments,
      attachmentsTwo: !!pmOrder.attachmentsTwo && pmOrder.attachmentsTwo,
      attachmentsTwoPreview: !!pmOrder.attachmentsTwo && JSON.parse(JSON.stringify(pmOrder.attachmentsTwo)),
      reassignFormTemplate: this.generateReAssignForm(),
      approvedParts,
      approvedParts1,
      partsNeedSign: wfConfig && wfConfig.partsNeedSign,
      signatureFiles: signatures,
      pmAckNeedSign: wfConfig && wfConfig.pmAckNeedSign,
      pmWcNeedSign: wfConfig && wfConfig.pmWcNeedSign,
      hasConfigMultiLevelApproval: approvalsConfig.enable,
      //新流程多级审核遇到1,5状态后灰掉“提交多级审核,暂存，保养完成”按钮
      isMultiVerifyBtnDisabled: pmOrder.approvalStatus === '1' || pmOrder.approvalStatus === '5',
      isGE_MVS: assetInfo && assetInfo.isMvsTant
    })
    let msg = await rest.get(urls.requestMsgs + `/${pmOrder.id}/serviceRequestMsgs`)
    this.setState({ msg })
  }

  _accept(val) {
    let { planTime } = this.state
    this._submit(ACTION_TYPES.Accept, { planTime })
  }

  _dispatch({ worker }) {
    let { planTime } = this.state
    this._submit(ACTION_TYPES.Assign, { assignee: worker, planTime })
  }

  _maintainSave({ complete, pass, manHours, endTime, prePmAttachments, attachments, attachmentsTwo, comments }) {
    let action = complete ? ACTION_TYPES.Repair : ACTION_TYPES.TempRepair
    let { coworkers, parts, planTime } = this.state
    // parts = complete ? parts.filter(part=>part.approved!=undefined&&part.approved!=null):parts
    if (complete)
      this.__closeSubmit(
        action,
        {
          comments,
          coworkers,
          details: parts,
          manHours,
          endTime,
          attachmentsTwo,
          prePmAttachments,
          attachments,
          planTime,
          qualityChecking: pass ? 1 : 2
        },
        pass
      )
    else
      this._submit(action, {
        comments,
        coworkers,
        details: parts,
        manHours,
        endTime,
        attachmentsTwo,
        prePmAttachments,
        attachments,
        planTime,
        qualityChecking: pass ? 1 : 2
      })
  }

  _maintainComplete({ complete, pass, manHours, endTime, attachmentsTwo, prePmAttachments, attachments, assignee, comments }) {
    let { coworkers, parts, planTime, wcSignature, pmWcNeedSign } = this.state

    if (pmWcNeedSign && !wcSignature) {
      Dialog.toast('请先签名。', 2000, 'warn')
      return
    }

    this.__closeSubmit(
      ACTION_TYPES.Repair,
      {
        comments,
        coworkers,
        details: parts,
        manHours,
        endTime,
        attachmentsTwo,
        prePmAttachments,
        attachments,
        planTime,
        qualityChecking: pass ? 1 : 2,
        assignee: assignee,
        wcSignature: wcSignature
      },
      pass
    )
  }

  _simplifyPm({ pass, manHours, endTime, attachmentsTwo, prePmAttachments, attachments, assignee, comments }) {
    let { coworkers, parts, planTime, wcSignature, pmWcNeedSign } = this.state

    if (pmWcNeedSign && !wcSignature) {
      Dialog.toast('请先签名。', 2000, 'warn')
      return
    }

    // parts = parts.filter(part=>part.approved!=undefined&&part.approved!=null)
    this.__closeSubmit(
      ACTION_TYPES.Simplify,
      {
        comments,
        coworkers,
        details: parts,
        manHours,
        endTime,
        attachmentsTwo,
        prePmAttachments,
        attachments,
        planTime,
        qualityChecking: pass ? 1 : 2,
        assignee: assignee,
        wcSignature: wcSignature
      },
      pass
    )
  }

  _assignApprover({ approver, desc }) {
    let { parts } = this.state
    this.setState({ addApprover: false })
    this._submit(ACTION_TYPES.Approval, {
      assignee: approver,
      desc,
      details: parts.map(part => {
        part.approved = part.approved ? part.approved : null
        return part
      })
    })
  }

  _approve({ desc, signature }) {
    let { approvedParts, approvedParts1 } = this.state
    let details = approvedParts1.concat(approvedParts)
    let passedParts = details.filter(part => part.approved)
    let approvedStatus =
      passedParts.length === details.length
        ? ACTION_TYPES.Pass
        : passedParts.length > 0
        ? 'partial'
        : ACTION_TYPES.Reject
    if (approvedStatus != 'pass' && !desc) {
      Dialog.toast('需要填写不同意原因。', 2000, 'warn')
      return
    }
    if (signature) signature = typeof signature == 'string' ? signature : signature.signatureUid
    this._submit(approvedStatus, { details, desc, signature })
  }

  _reject() {
    this._submit(ACTION_TYPES.Reject)
  }

  _reAssign({ assignee, desc }) {
    desc = desc || '工作需要'
    util.fixModalBug()
    this.setState({ showAs: false })
    this._submit(ACTION_TYPES.ReAssign, { assignee, desc })
  }

  _close({ pass, attachments, prePmAttachments, attachmentsTwo }) {
    this.__closeSubmit(ACTION_TYPES.Close, { qualityChecking: pass ? 1 : 2, attachments, prePmAttachments, attachmentsTwo }, pass)
  }

  __closeSubmit(action, body = {}, redirect = true) {
    this._submit(action, body, redirect).then(() => {
      if (!redirect) {
        Dialog.confirm('要创建报修工单吗?', '设备验收不合格').onDismiss(confirmed => {
          if (confirmed) {
            let asset = this.state.assetInfo
            util.assetExternalRepair(asset).then(isExtRepair => {
              if (!isExtRepair) {
                // 内修
                const { ruleLevel } = this.state.pmOrder
                history.replace({
                  pathname: route_urls.wx.createWo,
                  query: { id: this.state.pmOrder.assetId, srRepairType: 1, repairSource: ruleLevel == 4 ? 'QualityControl' : 'Maintenance' }
                })
              }
            })
          } else {
            history.replace({
              pathname: route_urls.home_wx + '/consolePmList',
              query: { con: 0 },
              state: { url: urls.pmOrderPickedUp, cons: { status: 0, batch: 0 }, from: '?' }
            })
            // this._defaultRedirect(ACTION_TYPES.Close)
          }
        })
      }
    })
  }

  _submit(action, body = {}, redirect = true) {
    let { id, currentStepId } = this.state.pmOrder
    body.actionType = action
    body.stepId = currentStepId
    return rest
      .put(new UrlPattern(urls.pmOrderAction).stringify({ id }), body, true, {
        extraHeaders: { taskSource: 'pmActionTask' }
      })
      .then(res => {
        if (action === ACTION_TYPES[STEPS.Accept]) {
          history.replace({ pathname: `/wx/pmorder/${id}`, query: { from: 'pmList' } })
          message.success('接单成功')
          util.reload()
        } else if (action === ACTION_TYPES[STEPS.Closed]) {
          util.reload()
        } else {
          res && redirect && this._defaultRedirect(action)
        }
      })
      .catch(err => {
        history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
      })
  }

  _defaultRedirect(action = '') {
    const { from } = this.props.location.query
    let pathname = route_urls.home_wx
    if (from === 'repairList') {
      pathname = `${route_urls.home_wx}/unAcceptPmList`
    }
    if (from === 'pmList' && this.props.location.state && this.props.location.state.cons) {
      pathname = `${route_urls.home_wx}/consolePmList`
    }
    history.replace({
      pathname: route_urls.msg,
      query: { code: 201 },
      state: { desc: ACTION_DESC[action] || action, next: { pathname, state: this.props.location.state || null } }
    })
  }

  _dispatchFormTemplate(assetId) {
    return {
      buttons: [
        {
          type: 'primary',
          label: '派工',
          validation: true,
          confirm: '确定派工？',
          onClick: val => this._dispatch(val)
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'worker',
              label: '保养人员',
              component: 'SelectUser',
              rule: { required: true },
              transform: user => user.uid,
              query: {
                assetId: 'assetId',
                excludes: 'excludes'
              }
            }
          ]
        }
      ]
    }
  }

  _maintainFormTemplate() {
    let value = {
      manHours: this.state.manHours || 0.1,
      endTime: this.state.endTime,
      attachmentsTwo: this.state.attachmentsTwo,
      prePmAttachments: this.state.prePmAttachments,
      attachments: this.state.attachments,
      comments: this.state.comments,
      pass: true
    }
    let { pmOrder } = this.state

    return {
      value,
      groups: [
        {
          items: [
            {
              key: 'manHours',
              label: '工时(小时)',
              component: 'Number',
              rule: { required: true, max: 999, min: 0 }
            },
            {
              key: 'endTime',
              label: '结束保养时间',
              component: 'Date',
              rule: { required: true }
            },
            {
              key: 'assignee',
              label: '验收人',
              component: 'SelectUser',
              rule: { required: pmOrder.autoAckMaintenance === false },
              props: {
                url: new UrlPattern(`${urls.pmOrder}/${pmOrder.id}/acker`).stringify({}),
                first: false,
                show: false
              },
              transform: user => user.uid,
              query: {},
              hide: v => pmOrder.autoAckMaintenance === true || pmOrder.autoAckMaintenance === null
            },
            {
              key: 'pass',
              label: '验收合格',
              component: 'Switch'
            },
            {
              key: 'comments',
              label: '工单备注'
            }
          ].filter(item => !isAppOffline() || !['验收人', '验收合格'].includes(item.label)) // 离线模式不显示验收人、验收合格
        },
        {
          items: [
            {
              key: 'attachmentsTwo',
              component: 'Attachment',
              props: { beforeUpload: showBoTypeModal, afterUpload: setBoType },
              // rule: { required: true },
              label: '保养附件上传',
            }
          ]
        },
        {
          items: [
            {
              key: 'prePmAttachments',
              component: 'ImageUploader',
              props: {
                title: '保养前照片上传',
                maxCount: 5,
                autoSave: true
              }
            }
          ]
        },
        {
          items: [
            {
              key: 'attachments',
              component: 'ImageUploader',
              props: {
                title: '保养后照片上传',
                maxCount: 5,
                autoSave: true
              }
            }
          ]
        }
      ]
    }
  }

  _generateApprovalForm() {
    let approvalForm = {
      value: { desc: '' },
      groups: [
        {
          title: '备注',
          items: [{ key: 'desc', label: '备注', component: 'TextArea', rule: { min: 0 } }]
        }
      ]
    }
    return approvalForm
  }

  _closeFormTemplate() {
    let value = { pass: true }
    return {
      value,
      buttons: [
        {
          type: 'primary',
          label: '关单',
          validation: true,
          confirm: '确定关单？',
          onClick: val => this._close({ ...value, ...val })
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'pass',
              label: '验收合格',
              component: 'Switch'
            }
          ]
        },
        {
          items: [
            {
              key: 'attachmentsTwo',
              component: 'Attachment',
              props: { beforeUpload: showBoTypeModal, afterUpload: setBoType },
              // rule: { required: true },
              label: '保养附件上传',
            }
          ]
        },
        {
          items: [
            {
              key: 'prePmAttachments',
              component: 'ImageUploader',
              props: {
                title: '保养前照片上传',
                maxCount: 5,
                autoSave: true
              }
            }
          ]
        },
        {
          items: [
            {
              key: 'attachments',
              component: 'ImageUploader',
              props: {
                title: '保养后照片上传',
                maxCount: 5,
                autoSave: true
              }
            }
          ]
        }
      ]
    }
  }

  generateReAssignForm() {
    let that = this
    let reassignFormTemplate = {
      buttons: [
        { type: 'primary', label: '转单', validation: true, onClick: val => this._reAssign({ ...val }) },
        {
          type: 'primary',
          label: '取消',
          onClick: val => {
            util.fixModalBug()
            this.setState({ showAs: false })
          }
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'assignee',
              label: '保养人员',
              component: 'SelectUser',
              rule: { required: true },
              transform: user => user.uid,
              query: {
                assetId: 'assetId',
                excludes: 'excludes'
              }
            },
            {
              key: 'desc',
              label: '转单原因',
              component: 'TextArea',
              rule: { required: true },
              props: {
                placeholder: '转单原因……',
                maxLength: 200,
                rows: '3',
                onClick: (form, e, cb) => {
                  if (that.state.reAssignDescCompare == form.desc) {
                    form.desc = ''
                    e.target.value = ''
                    cb(form)
                  }
                }
              }
            }
          ]
        }
      ]
    }
    return reassignFormTemplate
  }

  _updatePlanTime = evt => {
    this.setState({ planTime: evt.target.value })
  }

  toStepList() {
    let id = this.props.params.id
    history.push(new UrlPattern(route_urls.home_wx + '/pmOrder/:id/steps').stringify({ id }))
  }

  toReport() {
    alert('not implemented')
  }

  toChecklist() {
    let { assetInfo, pmOrder, isProcessor } = this.state
    let target = `/form/pmorder/:pmId/asset/:assetId`
    let query = { level: pmOrder.ruleLevel, assetId: assetInfo.id }
    let params = { assetId: assetInfo.uid, pmId: pmOrder.id }
    history.push({
      pathname: new UrlPattern(route_urls.home_wx + target).stringify(params),
      query:
        pmOrder.currentStepId == STEPS.Assign ||
        pmOrder.currentStepId == STEPS.Accept ||
        (pmOrder.currentStepId == STEPS.Maintain && isProcessor && pmOrder.approvalStatus != 1)
          ? query
          : { readonly: true }
    })
  }

  toMessageList() {
    let { pmOrder } = this.state
    history.push({
      pathname: route_urls.wx.srMsg + `/${pmOrder.id}`,
      state: { readOnly: pmOrder.currentStepId !== STEPS.Maintain, woType: 'pmOrder' }
      // state: {readOnly: true}
    })
  }

  toCreateWo() {
    const { ruleLevel } = this.state.pmOrder
    history.replace({
      pathname: route_urls.wx.createWo,
      query: { id: this.state.pmOrder.assetId, srRepairType: 1, repairSource: ruleLevel == 4 ? 'QualityControl' : 'Maintenance' }
    })
  }

  toast(msg) {
    this.setState({ toastMessage: msg, showToast: true }, () =>
      setTimeout(() => {
        this.setState({ toastMessage: '', showToast: false })
      }, 2000)
    )
  }

  addPart = part => {
    if (!part) return this.setState({ addPart: false })
    this.setState(state => {
      state.addPart = false
      if (state.index < 0) {
        state.parts.push(part)
      } else {
        state.parts = state.parts.slice(0, state.index).concat(part, state.parts.slice(state.index + 1))
      }
      state.index = -1
      return state
    })
    // fixModalBug()
  }

  addCoworker = coworker => {
    if (!coworker) return this.setState({ addCoworker: false })
    this.setState(state => {
      state.addCoworker = false
      if (state.index == -1) {
        state.coworkers.push(coworker)
      } else {
        state.coworkers = state.coworkers.slice(0, state.index).concat(coworker, state.coworkers.slice(state.index + 1))
      }
      state.index = -1
      return state
    })
  }

  clonePart(idx) {
    let partTemplate = { ...this.state.parts[idx] }
    partTemplate.id = null
    partTemplate.approved = null
    partTemplate.parts = ''
    partTemplate.apartsQuantity = ''
    partTemplate.partsPrice = ''
    partTemplate.closedPrice = ''
    // partTemplate.warrantyDate = moment(partTemplate.warrantyDate).format('YYYY-MM-DD')
    this.setState({ addPart: true, index: -2, partTemplate: partTemplate })
  }

  deletePart = (idx, item) => {
    Dialog.confirm('确定删除备件？').onDismiss(confirmed => {
      if (confirmed) {
        rest
          .del(
            urls.updatePrice + '/' + item.id,
            { pmId: this.props.params.id },
            { extraHeaders: { taskSource: 'deletePmOrderCostTask' } }
          )
          .then(res => {
            if (res) {
              message.success('删除成功', 2)
              this.setState(state => {
                state.parts.splice(idx, 1)
                return state
              })
            } else {
              message.error('删除失败', 2)
            }
          })
      }
    })
  }

  deleteCoworker = idx => {
    this.setState(state => {
      state.coworkers.splice(idx, 1)
      return state
    })
  }

  resetPartApproved = (idx, approved) => {
    this.setState(state => {
      state.approvedParts[idx].approved = approved
      return state
    })
  }
  resetPartArrived = (idx, arrived) => {
    this.setState(state => {
      state.parts[idx].arrived = arrived
      return state
    })
  }

  renderPartForm() {
    let { parts, pmOrder, isMultiVerifyBtnDisabled } = this.state
    return (
      <div key="part-form">
        <CellsTitle>备件明细</CellsTitle>
        <Cells>
          <Cell
            access
            onClick={() => {
              this.setState({ addPart: true, index: -1 })
            }}
          >
            <CellHeader>
              <Label>增加备件</Label>
            </CellHeader>
            <CellBody>总价格：{getTotalCost(parts)} (元)</CellBody>
            <CellFooter />
          </Cell>

          {parts.map((part, i) => {
            let props = {
              ...part,
              idx: i,
              deleteCallback: () => this.deletePart(i, part),
              resetPartArrived: this.resetPartArrived,
              state: this.props.state,
              clonePart: this.clonePart
            }
            return PartLineItem.call(this, props)
          })}

          {!isAppOffline() &&
            this.state.hasConfigMultiLevelApproval &&
            pmOrder.approvalStatus !== APPROVE_STATUS.Pending && (
              <Cell>
                <Button
                  onClick={() => {
                    this.submitToMultiLevelApproval()
                  }}
                  disabled={
                    this.state.parts.filter(item => !item.approved).length <= 0 || isMultiVerifyBtnDisabled
                      ? true
                      : false
                  }
                >
                  提交多级审核
                </Button>
              </Cell>
            )}

          {!isAppOffline() &&
            !this.state.hasConfigMultiLevelApproval &&
            pmOrder.approvalStatus !== APPROVE_STATUS.Pending && (
              <Cell>
                <Button
                  onClick={() => {
                    this.setState({ addApprover: true })
                  }}
                  disabled={this.state.parts.filter(item => !item.approved).length > 0 ? false : true}
                >
                  指定审核人
                </Button>
              </Cell>
            )}
        </Cells>
      </div>
    )
  }

  submitToMultiLevelApproval() {
    //配置了多级审核直接走多级审核,不走提交上级审核
    let { pmOrder } = this.state
    rest.post(urls.approvalsSubmit + `/pmOrder/${pmOrder.id}`).then(res => {
      if (res && res.id) {
        //message.success('保养多级审核提交成功', 2)
        history.replace({
          pathname: route_urls.msg,
          query: { code: 201 },
          state: { desc: '保养多级审核提交成功', next: route_urls.home_wx }
        })
      }
    })
  }

  renderCoworkerForm() {
    let { coworkers } = this.state
    return (
      <div key="coworker-form">
        <CellsTitle>工时与其他</CellsTitle>
        <Cells>
          <Cell access onClick={() => this.setState({ addCoworker: true, index: -1 })}>
            <CellHeader>
              <Label>增加协作人</Label>
            </CellHeader>
            <CellBody />
            <CellFooter />
          </Cell>

          {coworkers.map((worker, i) => {
            let props = { ...worker, idx: i, deleteCallback: () => this.deleteCoworker(i) }
            return CoworkerLineItem.call(this, props)
          })}
        </Cells>
      </div>
    )
  }

  showReAssign() {
    util.fixModalBug()
    this.setState({ showAs: true })
  }

  renderAcceptAction() {
    let buttons = [{ type: 'primary', label: '接单', confirm: '确定接单？', onClick: val => this._accept(val) }]
    return <GeneralForm buttons={buttons} />
  }

  reAssignToTempUser() {
    genShortcut(route_urls.home_wx + '/pmorder/' + this.props.params.id + '/external')
  }

  renderByStep(step) {
    let { isPmDispatcher, myself, location } = this.props
    let fromSource = location.query.from
    let roleList = new Set(myself.userRoleList.map(r => r.name))
    isPmDispatcher = isPmDispatcher || roleList.has('AssetHead') || roleList.has('HospitalHead')
    let { pmOrder, isProcessor, isOwner, showForm, partsNeedSign, isMultiVerifyBtnDisabled } = this.state

    let view = null

    switch (step) {
      case STEPS.Assign:
        if (isPmDispatcher && fromSource != 'repairList' && [2, 4].indexOf(pmOrder.dispatchMode) != -1) {
          let { buttons, groups } = this._dispatchFormTemplate(pmOrder.assetId)
          view = <GeneralForm value={{ assetId: pmOrder.assetId }} buttons={buttons} groups={groups} />
        } else if (
          (roleList.has('AssetStaff') || roleList.has('ExternalStaff')) &&
          [2, 3].indexOf(pmOrder.dispatchMode) != -1
        ) {
          view = this.renderAcceptAction()
        }
        break
      case STEPS.Accept:
        // if (isOwner) {
        if (roleList.has('AssetStaff') || isOwner) {
          view = this.renderAcceptAction()
        }
        break
      case STEPS.Maintain:
        if (showForm && isProcessor) {
          let { groups, value } = this._maintainFormTemplate()
          let buttons = [
            {
              type: 'default',
              label: '暂存',
              disabled: isMultiVerifyBtnDisabled,
              onClick: val => this._maintainSave({ complete: false, ...value, ...val })
            },
            !isAppOffline()
              ? this.state.pmWcNeedSign
                ? {
                    type: 'primary',
                    label: '保养完成',
                    validation: true,
                    disabled: isMultiVerifyBtnDisabled,
                    signature: 'wcSignature',
                    message,
                    confirm: '请签名后完成保养',
                    onClick: val => this._maintainComplete({ ...value, ...val })
                  }
                : {
                    type: 'primary',
                    label: '保养完成',
                    validation: true,
                    disabled: isMultiVerifyBtnDisabled,
                    onClick: val => this._maintainComplete({ ...value, ...val })
                  }
              : null
          ]
          view = [
            this.renderPartForm(),
            this.renderCoworkerForm(),
            <GeneralForm
              key="maintain-action"
              value={value}
              buttons={buttons}
              groups={groups}
              onChange={val => {
                console.log('maintain-action onchange', val )
                Object.assign(this.state, val)
                this.setState(this.state)
              }}
            />
          ]

          if (this.state.addApprover) {
            view.push(
              <AssignApproverDialog
                key="approver-dialog"
                assetUid={pmOrder.assetUid}
                onPositive={({ approver, desc }) => {
                  document.getElementsByTagName('body')[0].classList.remove('modal-open')
                  this._assignApprover({ approver, desc })
                }}
                onNegative={() => {
                  document.getElementsByTagName('body')[0].classList.remove('modal-open')
                  this.setState({ addApprover: false })
                }}
              />
            )
          }
        } else if (
          (isProcessor || roleList.has('AssetHead') || roleList.has('DeptHead')) &&
          pmOrder.approvalStatus === APPROVE_STATUS.Pending
        ) {
          let buttons = [
            partsNeedSign
              ? {
                  type: 'primary',
                  label: '提交',
                  signature: 'signature',
                  validation: true,
                  confirm: '确定提交通过？',
                  onClick: val => this._approve({ ...val })
                }
              : {
                  type: 'primary',
                  label: '提交',
                  validation: true,
                  confirm: '确定提交？',
                  onClick: val => this._approve({ ...val })
                }
          ]
          let { value, groups } = this._generateApprovalForm()
          view = <GeneralForm buttons={buttons} groups={groups} value={value} />
        }
        break

      case STEPS.Acceptance:
        // 待验收
        view = this.renderAcceptance()
        break

      case STEPS.Close:
        if (isOwner && pmOrder.approvalStatus !== APPROVE_STATUS.Pending) {
          let { buttons, groups, value } = this._closeFormTemplate()
          view = <GeneralForm value={value} buttons={buttons} groups={groups} />
        }
        break
    }
    return view
  }

  renderCommon = pmSimplify => {
    let {
      pmOrder: {
        currentStepId,
        currentStepName,
        fromDeptName,
        ownerName,
        details,
        coworkers,
        approvalStatus,
        currentPersonName,
        endTime,
        manHours,
        comments,
        pmTypeName,
        cancelPerson,
        cancelReason
      },
      assetInfo: { clinicalDeptName },
      showForm,
      msg,
      planTime,
      isProcessor
    } = this.state
    let roleList = new Set(this.props.myself.userRoleList.map(r => r.name))

    let allowEditPlanTime =
      currentStepId === STEPS.Accept || currentStepId === STEPS.Assign || currentStepId === STEPS.Maintain
    return (
      <Cells>
        {currentStepId !== STEPS.Approval && clinicalDeptName !== fromDeptName && (
          <DeptFrom title="保养时所在科室" value={fromDeptName} />
        )}
        {currentStepId !== STEPS.Approval && <Steps label={currentStepName} onClick={this.toStepList.bind(this)} />}
        <Cell>
          <CellBody>计划完成时间</CellBody>
          <CellFooter>
            <Input type="date" value={planTime} onChange={this._updatePlanTime} disabled={!allowEditPlanTime} />
          </CellFooter>
        </Cell>
        <Cell>
          <CellBody>保养类型</CellBody>
          <CellFooter>{pmTypeName}</CellFooter>
        </Cell>
        {ownerName && (
          <Cell>
            <CellBody>负责人</CellBody>
            <CellFooter>{ownerName}</CellFooter>
          </Cell>
        )}
        {approvalStatus === APPROVE_STATUS.Pending && currentStepId === STEPS.Maintain && (
          <Cell>
            <CellHeader>审核人</CellHeader>
            <CellBody />
            <CellFooter>{currentPersonName}</CellFooter>
          </Cell>
        )}

        {(currentStepId === STEPS.Maintain ||
          currentStepId === STEPS.Close ||
          currentStepId === STEPS.Closed ||
          pmSimplify) && [
          /** temperary hide this cell until backend ready */
          // <Cell key="open-report" access onClick={this.toReport}>
          //   <CellBody>报告</CellBody>
          //   <CellFooter/>
          // </Cell>,
          <Cell key="open-manifest" access onClick={this.toChecklist.bind(this)}>
            <CellBody>保养清单</CellBody>
            <CellFooter />
          </Cell>
        ]}
        {comments && (
          <Cell>
            <CellBody>工单备注</CellBody>
            <CellFooter>{comments}</CellFooter>
          </Cell>
        )}
        {!isNullOrEmpty(msg) && (
          <Cell key="open-messages" access={!isAppOffline()} onClick={!isAppOffline() && this.toMessageList.bind(this)}>
            <CellBody>最近消息</CellBody>
            <CellFooter>{isNullOrEmpty(msg) ? '' : ellipsis(msg[msg.length - 1].message, 15)}</CellFooter>
          </Cell>
        )}

        {(!showForm || currentStepId === STEPS.Close || currentStepId === STEPS.Closed) && (
          <div>
            {approvalStatus == APPROVE_STATUS.Pending &&
            (isProcessor || roleList.has('AssetHead') || roleList.has('DeptHead'))
              ? !isNullOrEmpty(this.state.approvedParts) && (
                  <Parts
                    parts={this.state.approvedParts}
                    currentStepId={currentStepId}
                    clonePart={this.clonePart}
                    approvalStatus={approvalStatus}
                    resetPartApproved={this.resetPartApproved}
                    state={this.props.state}
                  />
                )
              : !isNullOrEmpty(details) && (
                  <Parts
                    parts={details}
                    state={this.props.state}
                    approvalStatus={approvalStatus}
                    currentStepId={currentStepId}
                    clonePart={this.clonePart}
                  />
                )}
            {!isNullOrEmpty(coworkers) && coworkers.map(CoworkerLineItem)}
          </div>
        )}

        {endTime && currentStepId != STEPS.Maintain && (
          <Cell>
            <CellBody>完成时间</CellBody>
            <CellFooter>{endTime}</CellFooter>
          </Cell>
        )}
        {manHours && (
          <Cell>
            <CellBody>工时(小时)</CellBody>
            <CellFooter>{manHours}</CellFooter>
          </Cell>
        )}
        {cancelPerson && (
          <Cell>
            <CellBody>取消人</CellBody>
            <CellFooter>{cancelPerson}</CellFooter>
          </Cell>
        )}
        {!isAppOffline() && cancelReason && (
          <Cell>
            <CellBody>取消原因</CellBody>
            <CellFooter>{cancelReason}</CellFooter>
          </Cell>
        )}

        {approvalStatus !== APPROVE_STATUS.Unassign && approvalStatus !== APPROVE_STATUS.Pending && (
          <ApprovalStatus status={approvalStatus} />
        )}
        {this.state.attachmentsTwoPreview && this.state.attachmentsTwoPreview.length > 0 && (
          <Attachment title="保养附件" items={this.state.attachmentsTwoPreview} />
        )}
        {this.state.prePmImages && this.state.prePmImages.length > 0 && (
          <ImageUploader title="保养前照片" files={this.state.prePmImages} previewMode />
        )}
        {this.state.images && this.state.images.length > 0 && (
          <ImageUploader title="保养后照片" files={this.state.images} previewMode />
        )}
        {this.state.showAs && (
          <WCDialog title="转单" show={this.state.showAs} style={{ width: '98%', maxWidth: 'none' }}>
            <GeneralForm
              value={{
                assetId: this.state.pmOrder.assetId,
                excludes: [this.props.myself.userAccount.id],
                desc: '工作需要'
              }}
              buttons={this.state.reassignFormTemplate.buttons}
              groups={this.state.reassignFormTemplate.groups}
            />
          </WCDialog>
        )}
      </Cells>
    )
  }

  renderSimplify() {
    let { pmOrder, isMultiVerifyBtnDisabled } = this.state

    let view = null

    let { groups, value } = this._maintainFormTemplate()
    let closeTemplate = this._closeFormTemplate()
    value = { ...value, ...closeTemplate.value }
    // groups.push({
    //   items: [
    //     {
    //       key: 'pass',
    //       label: '验收合格',
    //       component: 'Switch'
    //     }
    //   ]
    // })
    let buttons = [
      {
        type: 'default',
        label: '暂存',
        disabled: isMultiVerifyBtnDisabled,
        onClick: val => this._maintainSave({ complete: false, ...value, ...val })
      },
      !isAppOffline()
        ? this.state.pmWcNeedSign
          ? {
              type: 'primary',
              label: '保养完成',
              validation: true,
              signature: 'wcSignature',
              disabled: isMultiVerifyBtnDisabled,
              message,
              confirm: '请签名后完成保养',
              onClick: val => this._simplifyPm({ ...value, ...val })
            }
          : {
              type: 'primary',
              label: '保养完成',
              validation: true,
              disabled: isMultiVerifyBtnDisabled,
              onClick: val => this._simplifyPm({ ...value, ...val })
            }
        : null
    ]
    view = [
      this.renderPartForm(),
      this.renderCoworkerForm(),
      <GeneralForm
        key="maintain-action"
        value={value}
        buttons={buttons}
        groups={groups}
        onChange={val => {
          Object.assign(this.state, val)
          this.setState(this.state)
        }}
      />
    ]

    if (this.state.addApprover) {
      view.push(
        <AssignApproverDialog
          key="approver-dialog"
          assetUid={pmOrder.assetUid}
          onPositive={({ approver, desc }) => this._assignApprover({ approver, desc })}
          onNegative={() => this.setState({ addApprover: false })}
        />
      )
    }
    return view
  }

  renderAcceptance(step) {
    // 待验收
    let groups = [
      {
        title: '不通过原因',
        items: [
          {
            key: 'remarks',
            label: '不通过原因',
            component: 'TextArea',
            rule: { required: false },
            props: { placeholder: '不通过原因…', maxLength: 200, rows: '3' }
          }
        ]
      }
    ]
    let value = { remarks: '' }
    let buttons = [
      this.state.pmAckNeedSign
        ? {
            type: 'default',
            label: '验收通过',
            validation: true,
            signature: 'ackSignature',
            confirm: '请签名后完成验收',
            onClick: val => this.acceptancePass({ ...value, ...val })
          }
        : {
            type: 'default',
            label: '验收通过',
            validation: true,
            onClick: val => this.acceptancePass({ ...value, ...val })
          },
      {
        type: 'primary',
        label: '验收不通过',
        validation: true,
        onClick: val => this.acceptanceReject({ ...value, ...val })
      }
    ]
    let view = [
      <GeneralForm
        key="maintain-action"
        value={value}
        buttons={buttons}
        groups={groups}
        onChange={val => {
          Object.assign(this.state, val)
          this.setState(this.state)
        }}
      />
    ]

    return view
  }

  renderCancel() {
    const { pmOrder } = this.state
    let roleList = new Set(this.props.myself.userRoleList.map(r => r.name))
    if (!roleList.has('AssetHead') && this.state.pmOrder.currentStepId == 'Maintain') return
    if (!(roleList.has('AssetHead') || roleList.has('AssetStaff'))) return
    let view = null
    let groups = [
      {
        title: '取消原因',
        items: [
          {
            key: 'desc',
            label: '取消原因',
            component: 'TextArea',
            rule: { required: pmOrder.pmType === 4 },
            props: { placeholder: '取消原因…', maxLength: 200, rows: '3' }
          }
        ]
      }
    ]
    let value = { desc: '' }
    let buttons = isAppOffline()
      ? []
      : [
          {
            type: 'primary',
            label: '取消',
            validation: true,
            confirm: '确定取消工单?',
            onClick: val => this.cancelPmOrder({ ...value, ...val })
          }
        ]
    view = [
      <GeneralForm
        key={pmOrder.pmType}
        value={value}
        buttons={buttons}
        groups={groups}
        onChange={val => {
          Object.assign(this.state, val)
          this.setState(this.state)
        }}
      />
    ]
    return view
  }

  async cancelPmOrder(val) {
    const { id, pmType, ruleLevel } = this.state.pmOrder
    const { assetInfo } = this.state
    val.qualityChecking = 2
    let pmRule = undefined
    if (assetInfo.uid && ruleLevel !== undefined) {
      pmRule = await rest.get(urls.pmRule + `/level?assetUid=${assetInfo.uid}&level=${ruleLevel}&status=1`)
    }
    if (
      pmType == 1 &&
      assetInfo.status != 4 &&
      assetInfo.status != 5 &&
      assetInfo.isValid &&
      !assetInfo.isDeleted &&
      pmRule
    ) {
      Dialog.confirm('是否继续生成周期性保养工单?', '', { cancel: '否', ok: '是' }).onDismiss(confirmed => {
        if (confirmed) {
          val.qualityChecking = 1
        }
        rest
          .del(new UrlPattern(urls.pmOrderCancel).stringify({ id }), val, {
            extraHeaders: { taskSource: 'cancelPmOrderTask' }
          })
          .then(() => {
            this._defaultRedirect(ACTION_TYPES.Cancel)
          })
          .catch(err => {
            history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
          })
      })
    } else {
      rest
        .del(new UrlPattern(urls.pmOrderCancel).stringify({ id }), val, {
          extraHeaders: { taskSource: 'cancelPmOrderTask' }
        })
        .then(() => {
          this._defaultRedirect(ACTION_TYPES.Cancel)
        })
        .catch(err => {
          history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
        })
    }
  }

  //验证通过
  async acceptancePass({ remarks, ackSignature }) {
    if (this.state.pmAckNeedSign && !ackSignature) {
      Dialog.toast('请先签名。', 2000, 'warn')
      return
    }

    this._submit(ACTION_TYPES.AcceptancePass, { ackSignature })
  }

  //验证不通过
  async acceptanceReject({ remarks }) {
    if (!remarks) {
      Dialog.toast('需要填写不通过原因。', 2000, 'warn')
      return
    }
    this._submit(ACTION_TYPES.AcceptanceReject, { remarks })
  }

  uploadAttachmentAfterCloseList() {
    let body = {
      attachments: this.state.attachments,
      prePmAttachments: this.state.prePmAttachments,
      attachmentsTwo: [...this.state.attachmentsTwo, ...this.state.attachmentsAfterClosed],
    }
    this._submit(STEPS.Closed, body)
  }

  deleteFile(file) {
    let newFiles = this.state.attachmentsAfterClosed
    newFiles = newFiles.filter(item => item.objectStorageId !== file.objectStorageId)
    this.setState({ attachmentsAfterClosed: newFiles })
  }

  render() {
    let { pmOrder, assetInfo, brief, more, showMore, actionType, showToast, toastMessage, index, parts, coworkers } =
      this.state
    let { isPmDispatcher, myself, location } = this.props
    let fromSource = location.query.from
    let roleList = new Set(myself.userRoleList.map(r => r.name))
    isPmDispatcher = isPmDispatcher || roleList.has('AssetHead') || roleList.has('HospitalHead')
    let isAssetStaff = roleList.has('AssetStaff')
    let pmSimplify =
      pmOrder.simplify && pmOrder.currentStepId !== STEPS.Closed && pmOrder.approvalStatus !== APPROVE_STATUS.Pending
    if (pmOrder.dispatchMode === 4 && pmOrder.currentStepId === STEPS.Assign) {
      pmSimplify = false
    } else if (
      pmOrder.dispatchMode === 2 &&
      pmOrder.currentStepId === STEPS.Assign &&
      isPmDispatcher &&
      fromSource != 'repairList'
    ) {
      pmSimplify = false
    } else if (
      [2, 3, 5].includes(pmOrder.dispatchMode) &&
      (pmOrder.currentStepId === STEPS.Assign || pmOrder.currentStepId === STEPS.Accept) &&
      !isAssetStaff
    ) {
      pmSimplify = false
    } else if (pmOrder.currentStepId === STEPS.Maintain && pmOrder.currentPersonId != myself.userAccount.uid) {
      pmSimplify = false
    }
    if (pmSimplify && APPROVE_STATUS.Pending === pmOrder.approvalStatus) {
      actionType = STEPS.Maintain
    }

    let partTemplate = undefined
    if (parts.length > 0) {
      partTemplate = { ...parts[parts.length - 1] }
      partTemplate.id = null
      partTemplate.parts = ''
      partTemplate.apartsQuantity = ''
      partTemplate.partsPrice = ''
    }

    if (this.state.addPart)
      return (
        <WoCost
          currentPersonId={this.state.currentUserId}
          woType="pmOrder"
          cost={index === -2 ? this.state.partTemplate : parts[this.state.index]}
          assetUid={assetInfo.uid}
          woId={pmOrder.id}
          siteUID={assetInfo.siteUID}
          onResult={this.addPart}
        />
      )
    if (this.state.addCoworker)
      return (
        <WoCost
          woType="pmOrder"
          assetId={pmOrder.assetId}
          coworker={coworkers[index]}
          currentPersonId={this.state.currentUserId}
          onResult={this.addCoworker}
        />
      )

    return (
      <div className={this.state.isGE_MVS ? 'disable_sub_btn' : ''}>
        <Toast icon="success-no-circle" show={showToast}>
          {toastMessage}
        </Toast>
        <Preview
          title={`保养编号: ${pmOrder.pmNum || ''}`}
          data={{ pmOrder, assetInfo }}
          brief={brief}
          more={more}
          showMore={showMore}
          onMore={() => this.setState({ showMore: !showMore })}
          hasMore
        />
        <RepairContact title="保养人信息" data={pmOrder.externalTempUser} />
        {this.renderCommon(pmSimplify)}
        {pmOrder.finished && <SignatureImages title="签名图片" files={this.state.signatureFiles} previewMode />}
        {!pmSimplify && this.renderByStep(actionType)}
        {pmSimplify && actionType != STEPS.Acceptance && this.renderSimplify()}
        {pmSimplify && actionType == STEPS.Acceptance && this.renderAcceptance()}
        {!pmOrder.finished && !isAppOffline() && this.renderCancel()}
        {!isAppOffline() &&
          pmOrder.currentStepId === STEPS.Maintain &&
          pmOrder.approvalStatus !== APPROVE_STATUS.Pending &&
          pmOrder.currentPersonId == myself.userAccount.uid && (
          <Cells>
            <Cell access onClick={this.showReAssign}>
              <CellHeader>
                <Label>转单</Label>
              </CellHeader>
              <CellBody />
              <CellFooter />
            </Cell>
          </Cells>
        )}
        {!isAppOffline() &&
        pmOrder.currentStepId === STEPS.Maintain &&
        pmOrder.approvalStatus !== APPROVE_STATUS.Pending && (
          <Cells>
            <Cell access onClick={this.reAssignToTempUser}>
              <CellHeader>
                <Label>短码</Label>
              </CellHeader>
              <CellBody />
              <CellFooter />
            </Cell>
          </Cells>
        )}
        {pmOrder.currentStepId === STEPS.Closed && (
          <Cells>
            {isAssetStaff && <React.Fragment>
              <Attachment
                title="附件上传"
                items={this.state.attachmentsAfterClosed}
                onChange={attachmentsAfterClosed => this.setState({ attachmentsAfterClosed })}
                accept={DEFAULT_FILE_TYPE}
                beforeUpload={showBoTypeModal}
                afterUpload={setBoType}
              />
              <Cell>
                <Button
                  disabled={this.state.attachmentsAfterClosed.length === 0}
                  onClick={() => this.uploadAttachmentAfterCloseList()}
                >
                  附件保存
                </Button>
              </Cell>
            </React.Fragment>}
            <Cell>
              <Button
                onClick={() => {
                  const url = `${urls.reportDownload}/pm?id=${pmOrder.id}`;
                  downloadBlobFile(url);
                }}
                type="primary"
              >
                下载保养报告
              </Button>
            </Cell>
          </Cells>
        )}

      </div>
    )
  }
}

function getTotalCost(parts) {
  return sumBy(parts, p => p.closedPrice)
}

function Steps({ title, label, onClick }) {
  return (
    <Cell access onClick={onClick}>
      <CellBody>{title ? title : '当前步骤'}</CellBody>
      <CellFooter>{label}</CellFooter>
    </Cell>
  )
}

function DeptFrom({ title, value }) {
  return (
    <Cell>
      <CellBody>{title}</CellBody>
      <CellFooter>{value}</CellFooter>
    </Cell>
  )
}

function Parts({ parts, approvalStatus, resetPartApproved, state, currentStepId, clonePart }) {
  return (
    <div>
      <CellsTitle>费用明细</CellsTitle>
      <Cells>
        {resetPartApproved && (
          <Cell>
            <CellHeader>
              <Label>总价格：</Label>
            </CellHeader>
            <CellBody>
              <Label>{getTotalCost(parts)}元</Label>
            </CellBody>
            <CellFooter>
              <Label>是否同意</Label>
            </CellFooter>
          </Cell>
        )}
        {parts.map((part, idx) =>
          PartLineItem({ ...part, idx, approvalStatus, resetPartApproved, state, stepId: currentStepId, clonePart })
        )}
      </Cells>
    </div>
  )
}

function PartLineItem({
  id,
  idx,
  attachments,
  parts,
  apartsQuantity,
  partsPrice,
  closedPrice,
  deleteCallback,
  partsType,
  approved,
  arrived,
  approvalStatus,
  resetPartApproved,
  resetPartArrived,
  state,
  stepId,
  clonePart
}) {
  let showIcon = !deleteCallback && !resetPartApproved && approvalStatus != 1
  return (
    <div>
      <FormCell key={idx || String(Math.random())}>
        {deleteCallback && (
          <CellHeader style={{ padding: '0px 20px 0px 0px' }}>
            <AntdIcon type="copy" theme="twoTone" onClick={() => clonePart(idx)} />
          </CellHeader>
        )}
        <CellBody
          onClick={() => {
            if (!this) return
            this.setState({ addPart: true, index: idx })
          }}
        >
          <div>
            {'备件名称：' +
              (parts || '无') +
              ',  备件数量：' +
              (apartsQuantity || 0) +
              (showIcon ? ', 实际价格(元)：' : ', 备件单价(元)：') +
              (showIcon ? partsPrice : partsPrice) +
              '，备件类别：' +
              (partsType ? util.preload(state).zh('partsType', partsType) : '')}
          </div>
        </CellBody>
        {deleteCallback && (
          <CellFooter>
            <Icon value="cancel" onClick={() => deleteCallback(idx)} />
          </CellFooter>
        )}
        {/* { approved && resetPartArrived &&
        <CellFooter>
          <Label style={{color:'red'}}>已到货</Label>
          <Switch checked={arrived} onChange={e => {
            resetPartArrived(idx, e.target.checked)
          }
          }/>
        </CellFooter>
      } */}
        {approvalStatus == 1 && resetPartApproved && (
          <CellFooter>
            <Switch
              checked={approved}
              onChange={e => {
                resetPartApproved(idx, e.target.checked)
              }}
            />
          </CellFooter>
        )}
        {showIcon && (
          <CellFooter>
            <Icon
              value="search"
              onClick={() => {
                history.push({
                  pathname: route_urls.home_wx + `/pmOrder/${id}/price`,
                  state: { currentStepId: stepId }
                })
              }}
            />
          </CellFooter>
        )}
      </FormCell>
      {Array.isArray(attachments) && attachments.length > 0 && (
        <FormCell key={'att_' + idx || String(Math.random())}>
          <CellBody>
            <ImageUploader title="附件" files={attachments} previewMode />
          </CellBody>
        </FormCell>
      )}
    </div>
  )
}

function CoworkerLineItem({ cowokerUserName, manHours, deleteCallback, idx }) {
  return (
    <Cell key={idx || String(Math.random())}>
      <CellBody
        onClick={() => {
          if (!this) return
          this.setState({ addCoworker: true, index: idx })
        }}
      >
        <p>{'协作人：' + (cowokerUserName || '无') + ', 工时(小时)：' + (manHours || 0)}</p>
      </CellBody>
      {deleteCallback && (
        <CellFooter>
          <Icon value="cancel" onClick={() => deleteCallback(idx)} />
        </CellFooter>
      )}
    </Cell>
  )
}

function ApprovalStatus({ status }) {
  return (
    <Cell key={String(Math.random())}>
      <CellHeader>审核状态</CellHeader>
      <CellBody />
      <CellFooter>
        {status === APPROVE_STATUS.Approved ? (
          <Colored color="green">同意</Colored>
        ) : status === APPROVE_STATUS.Rejected ? (
          <Colored color="red">不同意</Colored>
        ) : (
          <Colored color="red">部分同意</Colored>
        )}
        {/* <Colored color="green">已审核</Colored> */}
      </CellFooter>
    </Cell>
  )
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    isPmDispatcher: state.preload.isPmDispatcher,
    state
  }
}

export default connect(mapStateToProps)(PmOrderDetail)
