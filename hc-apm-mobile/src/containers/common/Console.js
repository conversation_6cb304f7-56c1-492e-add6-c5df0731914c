import React, { lazy } from 'react'
import { <PERSON><PERSON>, Spin } from 'antd'
import { connect } from 'react-redux'
import { isEqual } from 'lodash'
import { Page, Card, UserInfo, PullToRefresh, Svg } from '../../components'
import { cards, route_urls, fsoUtil, util, urls, rest } from '../../constants'
import ReOrderButton, { savedCards } from './ReOrderButton'
import './css/console.scss'
import 'animate.css'
import pulse from '../../images/pulse.inline.svg'
import { cloneDeep } from 'lodash'
import { isAPMBrowser, isAppOffline } from '@/actions/jsApi'

const { welcomeImage } = lazy(() => import(/* webpackChunkName: "welcomeImage" */ '@/containers/ib/ibConsole/constant'))
const bannerImages = lazy(() => import(/* webpackChunkName: "bannerImages" */ '@/images/application/masks/banner'))
const LayerMasks = lazy(() => import(/* webpackChunkName: "LayerMasks" */ '@/containers/ib/common/LayerMasks'))
// const { IbEntry } = lazy(() => import(/* webpackChunkName: "IbEntry" */'./AcctToIb'))
import { IbEntry } from './AcctToIb'
// import { browserHistory as history } from 'react-router'

const IbConsole = lazy(() => import(/* webpackChunkName: "IbConsole" */ '../ib/ibConsole/IBConsole'))
const tabPaneStyle = { display: 'flex', flexWrap: 'wrap', justifyContent: 'center', alignItems: 'flex-start' }
const TabPane = ({ ...props, children }) => <Tabs.TabPane {...props}>{children}</Tabs.TabPane>

class Console extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showCards: true,
      showFECards: false,
      showTraining: false,
      showGamePlan: false,
      queryWeChatStatistics: [],
      showNewCards:
        props &&
        props.location &&
        props.location.query &&
        props.location.query.showNewCards !== undefined
          ? props.location.query.showNewCards === 'true' || props.location.query.showNewCards === true
          : true
    }
  }

  componentWillMount() {
    this.redirect()
  }

  componentDidMount() {
    if (util.isIbSite()) {
      rest.get(`${urls.showMyTraining}`).then(showTraining => this.setState({ showTraining }))
      this.setState({ showGamePlan: true })
    }

    const { myself, isDispatcher, isPmDispatcher, zkbItems } = this.props
    const myTenant = myself.userAccount.tenantInfo
    if (myTenant && myTenant.type === 3) {
      this.setState({ showFECards: true })
      this.getFeCards()
    }
    this.setState({ showCards: true })
    this.setState({ newCards: this.getCardsByFeaturesFunctions() })
  }

  // componentDidUpdate(prevProps, prevState) {
  //   if (!isEqual(this.state.myCards, prevState.myCards)) {
  //     // console.log(1111111, this.state.newCards)
  //     // console.log('myCards state changed:', this.state.myCards)
  //   }
  // }

  redirect = () => {
    /** 需求来自远东 通过服务器透传state参数进行页面跳转
     * ?state=%2Fwx%2Fworkorder%2F2c92808a903526b4019080ed462e3a72%3Ffrom%3DrepairList
    */
    const { state, ...others } = this.props.location.query
    if (state) {
      try {
        let path = decodeURIComponent(state)
        if (path && path.startsWith('/wx/')) {
          // 创建 URL 对象
          const parsedUrl = new URL(path, window.location.origin);

          // 提取 pathname 和 query
          const pathname = parsedUrl.pathname;
          const query = Object.fromEntries(parsedUrl.searchParams.entries());
          Object.assign(query, others)
          // 处理空值
          for (const key in query) {
            if (query[key] === '') {
              delete query[key];
            }
          }

          // 最终结果
          const result = {
            pathname,
            query,
          };

          this.props.router.replace(result)
        }
      } catch (error) {
        console.warn(error)
      }
    }
  }

  async getFeCards() {
    const { myself, isDispatcher, isPmDispatcher, zkbItems } = this.props
    const myTenant = myself.userAccount.tenantInfo
    if (myTenant && myTenant.type === 3) {
      let feCards = []

      myTenant.testStatus !== -1 && feCards.push(cards.apply)
      let queryWeChatStatistics = await rest.get(urls.QWeChatStatistics)
      if (queryWeChatStatistics && queryWeChatStatistics.zkbPartOrderFlag === 't') {
        feCards.push(cards.zkb_apply)
      }
      if (queryWeChatStatistics && queryWeChatStatistics.zkbFePartOrderFlag === 't') {
        feCards.push(cards.fe_apply)
      }
      if (queryWeChatStatistics && queryWeChatStatistics.zkbIntegralOrderFlag === 't') {
        feCards.push(cards.application_specified)
      }

      // feCards.push(cards.zkb_docs(zkbItems))
      this.setState({ queryWeChatStatistics: feCards })
    }
  }

// 合并部分卡片
mergeSomeCards = (cards) => {
  const { newCards, showFECards } = this.state
  const cardNames = [
    showFECards && '设备查询',
    '我的报修',
    '我的维修工单',
    '报修管理',
    '保养管理',
    '我的保养工单',
    '保养备件审核',
    // '设备调剂',
    // '无人值守'
  ].filter(Boolean)

  // 以title为key建立旧卡片和新卡片的映射
  const oldMap = Array.isArray(cards) ? Object.fromEntries(cards.map(card => [card.title, card])) : {}
  const newMap = Array.isArray(newCards) ? Object.fromEntries(newCards.map(card => [card.title, card])) : {}

  // 只显示cardNames中在旧卡片里存在的
  const merged = cardNames.reduce((arr, name) => {
    if (oldMap[name]) {
      // 新旧都有，优先用新的
      if (newMap[name]) {
        arr.push(cloneDeep(newMap[name]))
      } else {
        // 只有旧的有
        arr.push(cloneDeep(oldMap[name]))
      }
    }
    // 旧的没有则不显示
    return arr
  }, [])

  // 把newCards里未出现在cardNames里的也加进来
  if (Array.isArray(newCards)) {
    newCards.forEach(card => {
      if (!cardNames.includes(card.title)) {
        merged.push(cloneDeep(card))
      }
    })
  }

  // 如果有设备查询卡片，则将其移到最前面
  const deviceQueryIndex = merged.findIndex(card => card.title === '设备查询')
  if (deviceQueryIndex > -1) {
    const [deviceQueryCard] = merged.splice(deviceQueryIndex, 1)
    merged.unshift(deviceQueryCard)
  }
  
  // （临时）如果有自定义角色且没有用户角色列表，则移除“我的报修”卡片
  const { myself: { customRoles, userRoleList } } = this.props
  if (customRoles.length > 0 && userRoleList.length === 0) {
    const filtered = merged.filter(card => card.title !== '我的报修')
    return this.removeDuplicate(this.getCardsOrder(filtered))
  }

  return this.removeDuplicate(this.getCardsOrder(merged))
}

/**
  输入: cards对象
        │
        ↓
  过滤notAcctCards + 对象类型
        │
        ├── 有features? → 检查权限 → 合并或添加卡片
        │
        └── 无features? → 过滤items权限 → 合并或添加卡片
        │
        ↓
  输出: myCards [按title合并后的卡片列表]
*/
getCardsByFeaturesFunctions = () => {
    const { config, myself } = this.props
    const roleList = new Set(myself.userRoleList.map(r => r.name))
    const workflowConfig = config.get('workflowConfig')
    const configItem = config.get('configItem')
    const myCards = []
    // 不需要展示的卡片key
    const notAcctCards = [
      'edu_apm',
      'edu_user',
      'kpi',
      'direport',
      'connectivity',
      'qrCode',
      'gamePlan',
      'zkb_apply',
      'fe_apply',
      'application_specified',
      'apply',
      'asset_ib'
    ]
    // 过滤掉不需要的卡片，只处理对象类型的cards
    const acctCardsFilter = cardKey => !notAcctCards.includes(cardKey) && typeof cards[cardKey] === 'object'
    Object.keys(cards).filter(acctCardsFilter).forEach(cardKey => {
      // 查找当前卡片是否已在myCards中（根据title判断）
      let thisCard = myCards.find(card => card.title === cards[cardKey].title)
      
      // 如果有myself属性，则检查是否有权限
      if (cards[cardKey].myself && !cards[cardKey].myself.split(',').some(item => myself[item] ? true : false)) {
        return 
      }
      // 如果有configItem属性且configItem[属性]为false，则跳过
      if (cards[cardKey].configItem && configItem && !configItem[cards[cardKey].configItem]) {
        return
      }
      // 如果有workflowConfig属性且workflowConfig[属性]为false，则跳过
      if (cards[cardKey].workflowConfig && workflowConfig && !workflowConfig[cards[cardKey].workflowConfig]) {
        return
      }
      // 只保留当前用户有权限的items
      const thisItems = cards[cardKey].items.filter(item => {
        // item.functions 可能有多个功能，用逗号分隔，只要有一个功能有权限就保留
        return item.functions && item.functions.split(',').some(func => util.hasFunction(func))
      })
      if (thisItems.length > 0) {
        if (thisCard) {
          // 如果已存在，则合并items.如果添加过的不需要再添加
          const existLinkNames = thisCard.items.map(item => item.label)
          const newLinks = thisItems.filter(item => !existLinkNames.includes(item.label))
          thisCard.items = thisCard.items.concat(newLinks)
        } else {
          // 否则深拷贝卡片对象，并设置items
          thisCard = cloneDeep(cards[cardKey])
          thisCard.items = thisItems
          myCards.push(thisCard)
        }
      }
    })
    // myCards.push(cards.no_asset_sr)
    return myCards
  }
  retrieveCards() {
    const { myself, isDispatcher, isPmDispatcher, zkbItems, config } = this.props
    const workflowConfig = config.get('workflowConfig')
    const configItem = config.get('configItem')
    const myTenant = myself.userAccount.tenantInfo
    const roleList = new Set(myself.userRoleList.map(r => r.name))
    // remove ultrasound limit YHAAO-4502
    // const isUltrasoundTenant = myTenant && myTenant.tenantConfig && JSON.parse(myTenant.tenantConfig).edition === 'ultrasound'
    let myCards = []
    let favorite
    const training = {
      label: '我的培训',
      component: require('../course/mine').default
      // props: { location: { query: { status: 1, submittedBy: 1 } } }
    }

    let my_sr = workflowConfig && workflowConfig.enableSendRepair ? cards.configurable_my_sr : cards.my_sr
    let my_wo = workflowConfig && workflowConfig.enableSendRepair ? cards.configurable_my_wo : cards.my_wo
    let team_sr = workflowConfig && workflowConfig.enableSendRepair ? cards.configurable_team_sr : cards.team_sr

    if (util.isZTB()) {
      myCards = [cards.asset, cards.mgr_pm_head, cards.mgr_pm_staff]
    } else if (myTenant && util.isIbTenant(myTenant.id)) {
      // ultrasound租户, YHAAO-3986
      myCards = [cards.asset_ib]
    } else if (myself.multiTenant) {
      // mvs 跨租户
      myCards = [cards.mvs_asset, cards.mvs_wo, cards.mvs_pm]
    } else if (myself.userRoleList.length === 1 && roleList.has('Guest')) {
      // Guest 用户
      myCards = [my_sr, cards.mgr_pm_clinic]
    } else if (roleList.has('ExternalStaff')) {
      // 外部维修
      myCards = [my_wo, cards.mgr_pm_staff]
    } else {
      // 设备查询 - 以下角色: 设备科, 临床等
      myCards = [cards.asset]
      // 综合绩效分析
      if (util.hasFunction('HISPERFORM_EFFICIENCY_EQUIPMENT') || util.hasFunction('HISPERFORM_EFFICIENCY_DEPARTMENT')) {
        const links = []
        util.hasFunction('HISPERFORM_EFFICIENCY_EQUIPMENT') && links.push('设备综合绩效分析')
        util.hasFunction('HISPERFORM_EFFICIENCY_DEPARTMENT') && links.push('综合绩效科室汇总')
        myCards.push(cards.get('performance').pick(links))
      }
      // 我的报修 或 科室报修
      if (!roleList.has('AssetHead') && !isDispatcher) {
        myCards.push(team_sr)
      } else {
        if (myself.userAccount.coreTeamApproveFalg) {
          let newCard = cloneDeep(my_sr)
          newCard.items.push({
            component: 'Link',
            label: '核心组审核进度查看',
            target: { pathname: route_urls.home_wx + '/coreGroupReviewProgress', query: {} },
            summaryItem: ''
          })
          myCards.push(newCard)
        } else {
          myCards.push(my_sr)
        }
      }
      // 保养备件审核
      if (roleList.has('ClinicalStaff') || roleList.has('DeptHead') || roleList.has('HospitalHead')) {
        myCards.push(cards.mgr_pm_clinic)
      }
      // 护士工作站 - 日常维护
      if (roleList.has('NursingStaff')) {
        myCards.push(util.isNursePower() ? cards.get('nurse').remove('使用登记记录') : cards.nurse)

        if (!roleList.has('AssetStaff') && !roleList.has('AssetHead')) {
          myCards.push(cards.staff_inv)
        }
      }

      // 报修管理 - 派工人
      if (isDispatcher) {
        myCards.push(cards.dis_wo)
      }
      // 报修管理 - 科长
      if (roleList.has('AssetHead') && !isDispatcher) {
        myCards.push(cards.mgr_wo)
      }
      // 保养管理
      if (isPmDispatcher || roleList.has('AssetHead')) {
        myCards.push(cards.mgr_pm_head)
      }
      // 报修管理 - 医工
      if (roleList.has('AssetStaff')) {
        myCards.push(my_wo)
        myCards.push(cards.mgr_pm_staff)
      }
      // 设备调剂
      if (util.hasFeature('ADJUST_ASSET') && myself.allocationCenter) {
        if (roleList.has('SharedAssetMgmt') || roleList.has('AssetHead') || roleList.has('AssetStaff')) {
          if (myself.siteSelectable) {
            myCards.push(cards.asset_dispatch_center_Unattended_Manager)
            myCards.push(cards.asset_dispatch_center_Normal_Manager)
          } else {
            if (configItem && configItem.whetherToEnableUnattended) {
              myCards.push(cards.asset_dispatch_center_Unattended_Manager)
            } else {
              myCards.push(cards.asset_dispatch_center_Normal_Manager)
            }
          }
        } else if (
          [...roleList].filter(r => !'MultiHospital,ITAdmin,HospitalHead,NursingStaff'.includes(r)).length > 0
        ) {
          if (configItem && configItem.whetherToEnableUnattended) {
            myCards.push(cards.asset_dispatch_center_Unattended_Clinical)
          } else {
            myCards.push(cards.asset_dispatch_center_Normal_Clinical)
          }
        }
      }
      // 没有无人值守卡片，又是专为院长设计的ADJUST_UNATTENDED_MOBILE_STORAGE_COUNT
      if (!myCards.some(card => card.title === '无人值守') && util.hasFunction('ADJUST_UNATTENDED_MOBILE_STORAGE_COUNT')) {
        myCards.push(cards.get('asset_dispatch_center_Unattended_Manager').pick(['设备库存统计']))
      }

      //备件库存
      if (util.hasFeature('PARTS_APPLY') && roleList.has('SparePartsAdmin') && myself.userAccount.tenantInfo.tenantConfig.indexOf('parts') > -1) {
        myCards.push(cards.new_inventory)
      }

      //多级审批
      if (roleList.has('MultiLevelAuditor')) {
        myCards.push(cards.approvals)
      } else {
        myCards.push(cards.get('approvals').pick(['我发起的']))
      }

      // 设备报废
      if (configItem && configItem.assetScrap) {
        if (roleList.has('AssetStaff')) { // 医工显示3个
          myCards.push(cards.get('asset_scrap').pick(['我的申请-已审核', '待审核', '已审核']))
        } else if ([...roleList].every(item => ['NursingStaff', 'DeptHead', 'ClinicalStaff'].includes(item))) { // 只有临床橘色, 只显示2个我的申请
          myCards.push(cards.get('asset_scrap').pick(['我的申请-待审核', '我的申请-已审核']))
        } else {  // 其他
          myCards.push(cards.get('asset_scrap').pick(['我的申请-已审核']))
        }
      }

      // 设备转科
      if (util.isEnableAssetTransfer()) {
        if (roleList.has('DeptHead')) { // 临床主任
          myCards.push(cards.asset_transfer)
        } else if (roleList.has('ClinicalStaff') || roleList.has('NursingStaff')) {
          myCards.push(cards.get('asset_transfer').pick(['我的申请-待审核', '我的申请-已审核']))
        }
      }

      // 科务
      if (roleList.has('AssetHead')) {
        myCards.push(cards.dept_transaction_head)
      } else if (roleList.has('AssetStaff')) {
        myCards.push(cards.dept_transaction_staff)
      }
      // 盘点
      if (roleList.has('AssetHead')) {
        myCards.push(cards.mgr_inv)
      } else if (roleList.has('AssetStaff') && !roleList.has('AssetHead')) {
        myCards.push(cards.staff_inv)
      }
      // 临床科室: 快捷入口(科室报修)
      if (roleList.has('ClinicalStaff') || roleList.has('DeptHead')) {
        favorite = {
          label: '科室报修',
          component: require('../workorder/ConsoleSRList').default,
          props: { location: { query: { status: 1, submittedBy: 1 } } }
        }
      }

      //不良事件上报
      if (util.hasFeature('ASSET_ADVERSE_EVENT') && myself.userAccount.tenantInfo.isEnablesAdverseEvents) {
        if (
          roleList.has('ITAdmin ') || //后台运营管理
          roleList.has('NursingStaff') || //护理人员
          roleList.has('DeptHead') || //临床科室主任
          roleList.has('HospitalHead') || //院长
          roleList.has('ClinicalStaff') || //临床科室人员
          roleList.has('AssetStaff') || //工程师
          roleList.has('SharedAssetMgmt') || //设备调剂管理人员
          roleList.has('AssetHead')
        ) {
          //医学工程处主任
          myCards.push(cards.adverseEvents_reporter)
        }

        if (roleList.has('MultiLevelAuditor')) {
          myCards.push(cards.adverseEvents_reporter)
          myCards.push(cards.adverseEvents_approver)
        }
      }

      //Acct科室调用
      if (workflowConfig && workflowConfig.enableSiteToSiteAllocation) {
        if (roleList.has('DeptHead')) {
          myCards.push(cards.departmentSeconded_director)
        } else if (roleList.has('ClinicalStaff') || roleList.has('NursingStaff')) {
          myCards.push(cards.departmentSeconded_nursingAndClinical)
        }
      }

      // 新版科室间设备调剂
      if (util.hasFeature('SECONDMENT_BETWEEN_DEPARTMENTS')) {
        const links = []
        util.hasFunction('SECONDMENT_MOBILE_MULTI_DEPARTMENT_EQUIPMENT') && links.push('科室间设备')
        util.hasFunction('SECONDMENT_MOBILE_SINGLE_DEPARTMENT_EQUIPMENT') && links.push('本科室设备')
        util.hasFunction('SECONDMENT_BORROW_LEND_RECORDS') && links.push('借入管理', '借出管理')
        if (links.length > 0) {
          myCards.push(cards.get('asset_secondment').pick(links))
        }
      }
    }
    // 智康保
    if (fsoUtil.isFSOType(myself.userAccount.geUserType)) {
      myCards = [cards.fso_asset]
    }
    // 用户反馈
    if (fsoUtil.isFSType(myself.userAccount.geUserType)) {
      myCards.push(cards.kpi)
    }
    // sales game plan
    if (this.state.showGamePlan && fsoUtil.isSType(myself.userAccount.geUserType)) {
      myCards.push(cards.gamePlan)
    }

    // di微报
    if (fsoUtil.isFEType(myself.userAccount.geUserType)) {
      myCards.push(cards.direport)
    }

    // add asset if the tenant has ib device
    if (myTenant && myTenant.haveIbDevice && !myself.multiTenant && !fsoUtil.isFSOType(myself.userAccount.geUserType)) {
      myCards.push(util.isIbTenant(myTenant.id) ? cards.asset_ib : cards.asset)
    }

    if (roleList.size === 1 && roleList.has('OpWorker')) {
      //工勤人员
      myCards = []
    }

    return {
      myCards: this.mergeCustomRoleWxCards(myself.customRoles, myCards),
      favorite,
      training
    }
  }

  mergeCustomRoleWxCards(customRoles, myCards) {
    if (Array.isArray(customRoles) && customRoles.length > 0) {
      const wxCards = new Set([])
      const cardLablels = new Set([])

      customRoles.forEach(role => {
        Array.isArray(role.wxCards) && role.wxCards.forEach(cardKey => wxCards.add(cardKey))
        role.authoritiesCH.split(',').forEach(label => cardLablels.add(label))
      })

      wxCards.forEach(cardKey => {
        const card = Object.assign({}, cards[cardKey])
        if (card && !myCards.find(mycard => isEqual(mycard, card)) && Array.isArray(card.items)) {
          card.items = card.items.filter(item => Array.from(cardLablels).some(label => label === item.label))
          card.items.length > 0 && myCards.push(card)
        }
      })
    }
    return this.removeDuplicate(this.getCardsOrder(myCards))
  }

  removeDuplicate(duoCards) {
    let _cards = Array.from(new Set(duoCards))
    let _gfc = isAPMBrowser()
    if (_gfc && typeof _gfc.setOfflineModules === 'function') {
      _cards = _gfc.setOfflineModules(_cards)
    }
    return _cards
  }

  async refresh() {
    this.setState({ showCards: false }, () => this.setState({ showCards: true }))
    return Promise.resolve()
  }

  renderCards(myCards) {
    // 用 card.title 作为 key，确保唯一和稳定
    return Array.isArray(myCards) && myCards.map((card, index) => (
      <Card key={card.title || index} {...card} />
    ))
  }

  getCardsOrder(myCards) {
    const cardNames = savedCards(this.props.myself).get()
    const sortCards = []
    if (Array.isArray(cardNames)) {
      cardNames.forEach(name => {
        const index = myCards.findIndex(card => card.title === name)
        if (index > -1) {
          sortCards.push(myCards.splice(index, 1)[0])
        }
      })
      return [...sortCards, ...myCards]
    } else {
      return myCards
    }
  }

  onTabClick(tab) {
    const {
      router,
      location: { pathname }
    } = this.props
    router.replace({ pathname, query: { tab } })
  }

  render() {
    if (util.isIbSite()) {
      return <IbConsole />
    }
    const { showTraining, showCards, queryWeChatStatistics, showFECards, newCards, showNewCards } = this.state
    const { favorite, myCards, training } = this.retrieveCards()
    const _newCards = showNewCards ? this.mergeSomeCards(myCards) : myCards
    const {
      location: { query },
      t,
      myself
    } = this.props
    const { userAccount } = myself
    const hasFavorite = favorite && favorite.label && favorite.component
    const activeKey = query && query.tab ? query.tab : hasFavorite && !isAppOffline() ? 'favorite' : 'cards'
    const goScanQrCode = () => this.props.router.push({ pathname: route_urls.wx.scanQrCode })

    return (
      <Page className="panel" footer={util.COPYRIGHT}>
        {util.isIbSite() && <LayerMasks new="new_version" mapImage={welcomeImage}></LayerMasks>}
        <PullToRefresh
          onRefresh={(resolve, reject) => {
            this.refresh().then(resolve).catch(reject)
          }}
          loadingHeight={70}
          loaderLoadingIcon={<Svg classnames={'pulsesvg loadingsvg'} svg={pulse} />}
          loaderDefaultIcon={progress => {
            return <Svg classnames={'pulsesvg'} svg={pulse} />
          }}
        >
          <UserInfo onClick={() => this.props.router.push(route_urls.wx.user)} goScanQrCode={goScanQrCode} />
          <Tabs className={`console-tab`} activeKey={activeKey} onTabClick={this.onTabClick.bind(this)}>
            {hasFavorite && !isAppOffline() && (
              <TabPane tab={favorite.label} key="favorite">
                <favorite.component {...this.props} {...favorite.props} />
              </TabPane>
            )}
            <TabPane tab="我的应用" key="cards">
              <div style={tabPaneStyle}>
                <div style={{ width: '100vw', padding: '0px 5px 5px 0px', textAlign: 'right' }}>
                  <IbEntry />
                </div>
                {this.state.showCards && this.renderCards(_newCards)}
                {_newCards && _newCards.length > 0 && <ReOrderButton cards={_newCards} />}
                {util.isIbSite() && (
                  <img
                    src={bannerImages['changeBanner']}
                    onClick={this.switchV2}
                    style={{ width: '100%', marginTop: '20px' }}
                    alt=""
                  />
                )}
              </div>
            </TabPane>
            {/* {newCards && (
              <TabPane tab="Func Cards" key="newCards">
                {this.renderCards(this.mergeSomeCards(myCards))}
              </TabPane>
            )} */}
            {showFECards && (
              <TabPane tab="智康保" key="fecards">
                {/* <Grids items={modules} onClick={target => this.props.router.push(target)}/> */}
                {queryWeChatStatistics && queryWeChatStatistics.length !== 0 ? (
                  this.renderCards(queryWeChatStatistics)
                ) : (
                  <div style={{ width: '100%', textAlign: 'center' }}>
                    <Spin />
                  </div>
                )}
              </TabPane>
            )}
            {showTraining && (
              <TabPane tab={training.label} key="training">
                <training.component {...this.props} />
              </TabPane>
            )}
          </Tabs>
        </PullToRefresh>
      </Page>
    )
  }
}

function mapStateToProps(state) {
  return {
    userConnInfo: state.preload.userConnInfo,
    myself: state.preload.myself,
    myConfig: state.preload.myConfig,
    zkbItems: state.preload.zkbMainDirectory,
    isDispatcher: state.preload.isDispatcher,
    isPmDispatcher: state.preload.isPmDispatcher,
    config: state.workflowConfig
  }
}

export default connect(mapStateToProps)(Console)
