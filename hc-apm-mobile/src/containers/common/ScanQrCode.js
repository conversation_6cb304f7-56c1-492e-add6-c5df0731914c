import React from 'react'
import { connect } from 'react-redux'
import { Icon, message } from 'antd'
import { Modal, Toast } from 'antd-mobile'
import { Button, ButtonArea } from 'react-weui'
import { Page, QrCodeScan } from '../../components'
import { browserHistory as history } from 'react-router'
import { route_urls, rest, urls, util } from '../../constants'
import { getShowName } from '@/containers/ib/ibConsole/constant'
import { getAssetConnInfo } from '@/containers/device/DeviceActions'
import { AcctToIb, IbToAcct } from '@/containers/common/AcctToIb'
import { isAPMBrowser } from '@/actions/jsApi'
import './css/scanQrCode.scss'

class ScanQrCode extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      action: 'events',
      query: ''
    }
  }

  componentWillMount() {
    let { qrCode, assetId } = this.props.params
    if (!qrCode) {
      qrCode = this.props.location.query.qrCode
    }
    this.setQrCode(qrCode, assetId)
  }

  componentDidMount() {
    let { qrCode } = this.props.params
    //取消角色判断页面跳转
    // if (!qrCode && util.isIbSite()) {
    //   if (IbToAcct()) {
    //     console.log('Acct不是临时用户, 重定向至Acct扫一扫页面')
    //   } else if (getShowName() === 'CUSTOMER') {
    //     util.goOneClickRepair()
    //   }
    // }
  }

  setQrCode(qrCode, assetId) {
    const gotoErrorPage = error =>
      error &&
      error.code &&
      history.push({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
    if (qrCode) {
      this.parseQrCode(qrCode, assetId).then().catch(gotoErrorPage)
    }
  }

  setAction(action) {
    const { query } = this.state
    if (action === 'query') {
      history.push(route_urls.wx.device + '?assetName=' + query)
    } else {
      this.setState({ action }, () => this.setQrCode(query))
    }
  }

  async checkLibAndAsset(qrCodeObj) {
    let { listQrCodeLib } = qrCodeObj
    if (Array.isArray(listQrCodeLib) && listQrCodeLib.length > 0) {
      const len = listQrCodeLib.length
      if (this.state.action === 'maintain') {
        listQrCodeLib = listQrCodeLib.filter(lib => lib.assetInfo && lib.assetInfo.isPm && lib.assetInfo.accessable)
        if (listQrCodeLib.length === 0 && listQrCodeLib.length < len) {
          throw { code: 401, message: '当前用户无权保养此设备' }
        }
      }
      if (this.state.action === 'nurse' || this.state.action === 'nursePowerCtrl') {
        listQrCodeLib = listQrCodeLib.filter(lib => lib.assetInfo && lib.assetInfo.accessable)
        if (listQrCodeLib.length === 0 && listQrCodeLib.length < len) {
          throw { code: 401, message: '当前用户无权维护此设备' }
        }
      }
      if (listQrCodeLib.length > 1) {
        return new Promise((resolve, reject) => {
          const libs = listQrCodeLib.map(lib => ({
            text: lib.orgSiteName,
            onPress: () => {
              resolve(this.checkQrCodeLib(lib))
            }
          }))
          libs.push({ text: '取消', style: { color: 'gray' } })
          Modal.alert('请选择院区', listQrCodeLib[0].qrCode, libs)
        })
      } else {
        return this.checkQrCodeLib(listQrCodeLib[0])
      }
    } else {
      throw { code: 400, message: '无法找到该设备, 请联系医院设备科/医工处' }
    }
  }

  checkQrCodeLib(qrCodeLib) {
    switch (qrCodeLib.status) {
      case 1: // 已发行: barcode was created but no data has been uploaded, go to update asset page
        if (this.isAssetAccesser()) {
          history.push({ pathname: route_urls.wx.updateQrCode, state: { qrCodeLib } })
        } else {
          Toast.fail('此设备尚未建档，请联系设备科进行处理！')
        }
        break
      case 2: // 已上传: some data has been uploaded to the asset, go to uploaded page, then user choose to view or to keep uploading data */
        history.push({ pathname: route_urls.wx.uploadedQrCode, state: { qrCodeLib } })
        break
      case 3: // 已建档
        return this.checkAssetStatus(qrCodeLib.assetInfo)
      case 4: // 已作废
        throw { code: 400, message: '已作废的二维码' }
      case 5: // 已使用
        throw { code: 400, message: '该资产不存在' }
      default:
        return qrCodeLib.assetInfo
    }
  }

  async checkAssetStatus(asset) {
    if (!asset) {
      throw { code: 400, message: '无法精确匹配资产' }
    }
    if (asset.isValid === false) {
      throw { code: 400, message: '该设备已报废' }
    }
    if (asset.status === 4) {
      throw { code: 400, message: '该设备待报废' }
    }
    if (asset.status === 5) {
      throw { code: 400, message: '该设备已停用' }
    }
    return asset
  }

  async manuallyReport(qrCode, action) {
    // exist query asset by input: 设备编号/资产编号/二维码
    const { userAccount } = this.props.myself
    const asset = await rest.get(urls.assetScanQrCode, { qrCode })
    if (asset) {
      if (action === 'incident') {
        if (this.isTempOrGuestOrFE()) {
          const wfConfig = await rest.get(urls.workflowsConfig + `/${asset.id}`)
          if (wfConfig && wfConfig.isTempUserReport) {
            throw { code: 401, message: '不支持临时账号报修' }
          }
        }
        await this.goInternalRequest(asset, userAccount.id)
      } else {
        if (asset.isPm === false) {
          throw { code: 400, message: '此设备已设置无需保养' }
        } else {
          const allowed = await this.checkUserRole(asset)
          if (allowed) {
            const { data: assets } = await rest.list(urls.assets, { uid: asset.uid })
            if (!assets || assets.length < 1) {
              throw { code: 401, message: '当前用户无权保养此设备' }
            } else {
              history.push(`${route_urls.home_wx}/createPmOrder/${asset.id}`)
            }
          }
        }
      }
    } else {
      throw { code: 400, message: '无法精确查找到指定设备' }
    }
  }

  async checkUserRole(asset) {
    const canViewAsset = async () => {
      const { data: assets } = await rest.list(urls.assets, { uid: asset.uid })
      if (Array.isArray(assets) && assets.length > 0) return true
      else return false
    }
    const allowNurseGoAssetPage = async () => {
      if (util.isIbSite()) {
        return false
      } else {
        const workflowConfig = this.props.config.get('workflowConfig')
        if (!workflowConfig) {
          return false
        }
        const { isNursingStaffCodeLedger } = workflowConfig
        return isNurse().result && isNursingStaffCodeLedger ? await canViewAsset() : false
      }
    }
    // check FE or 非FE
    const { multiTenant, userAccount } = this.props.myself
    if (multiTenant) {
      const access = await rest.get(urls.mvsAccessAvailable, { assetUid: asset.uid })
      if (!access) {
        throw { code: 401, message: '当前用户无权查看此设备' }
      }
      return true
    } else if (this.isTempOrGuestOrFE()) {
      const wfConfig = await rest.get(urls.workflowsConfig + `/${asset.id}`)
      if (wfConfig && wfConfig.isTempUserReport) {
        throw { code: 401, message: '不支持临时账号报修' }
      }
      await this.goInternalRequest(asset, userAccount.id)
    } else if (await allowNurseGoAssetPage()) {
      return true
    } else if ((!this.isAssetAccesser() || !asset.accessable) && this.state.action !== 'maintain') {
      await this.goInternalRequest(asset, userAccount.id)
    } else if (!await canViewAsset()) {
      await this.goInternalRequest(asset, userAccount.id)
    } else {
      return true
    }
  }

  async ibRegisterOrIconnect(asset) {
    if (this.isTempOrGuest() && asset && asset.id) {
      util.registerOrRepair(asset, () => util.goOneClickRepair(asset))
    } else {
      util.goOneClickRepair(asset)
    }
  }

  async goInternalRequest(asset, userId) {
    let callback = async (asset, userId) => {
      const isExtRepair = await util.assetExternalRepair(asset)
      if (!isExtRepair) {
        await this.checkAssetStatus(asset)
        const retUser = await rest.get(urls.telReport, { assetId: asset.id })
        if (retUser && retUser.id) {
          history.push(route_urls.wx.telReport + '?userName=' + retUser.name + '&tel=' + retUser.telephone)
          return
        }
        const res = await rest.list(urls.request, { status: 1, page: 0, pageSize: 1, assetId: asset.id })
        if (
          res &&
          res.data &&
          res.data.length > 0 &&
          (this.isClinicalStaff() || isNurse().result || this.isTempOrGuest())
        ) {
          history.push({ pathname: route_urls.wx.srScanList, state: { asset, userId } })
        } else {
          history.push(route_urls.wx.createWo + '?id=' + asset.id)
        }
      }
    }

    if (this.isTempOrGuest() && asset && util.isIbTenant(asset.siteId)) {
      util.registerOrRepair(asset, callback.bind(this, asset, userId))
      // if (util.isIbTenant(asset.siteId)) {
      // } else {
      //   Toast.info('本设备不支持自助注册，请联系管理员', undefined, callback.bind(this, asset, userId))
      // }
    } else {
      callback(asset, userId)
    }
  }

  async parseQrCode(qrCode, assetId) {
    const { action } = this.state
    let asset
    if (util.isIbSite()) {
      if (assetId) {
        asset = await rest.get(`${urls.assets}/${assetId}`)
      } else {
        asset = { systemId: '-asset-not-found-' }
      }
    } else {
      const { qrCodeObj } = await util.parseQrCode(qrCode)
      asset = await this.checkLibAndAsset(qrCodeObj)
    }
    switch (action) {
      case 'nurse':
      case 'nursePowerCtrl':
      case 'events':
        if (action === 'events') {
          if (util.isIbSite()) {
            await this.ibRegisterOrIconnect(asset)
          } else {
            if (this.isTempOrGuest() && asset.systemId) {
              await getAssetConnInfo(asset.systemId)
            }
            const allowed = await this.checkUserRole(asset)
            if (allowed) {
              if (this.isFe()) {
                this.goDevicePage(asset)
              } else {
                history.push(`${route_urls.home_wx}/device/${asset.id}/events`)
              }
            }
          }
        }
        if (action === 'nurse') {
          history.push({ pathname: route_urls.wx.nurse, query: { assetUid: asset.uid, group: 'todayTimeout' } })
        }
        if (action === 'nursePowerCtrl') {
          if (asset && !asset.isUseRegistration) {
            message.error('当前设备不需要使用登记')
            return
          }
          history.push({ pathname: route_urls.wx.nursePowerCtrl + '/' + asset.uid })
        }
        break
      case 'incident':
      case 'maintain':
        await this.manuallyReport(qrCode, action)
        break
      default:
    }
  }

  goDevicePage(asset) {
    const { config } = this.props
    const configItem = config.get('configItem')
    const pageMap = {
      record: 'events',
      asset: 'props',
    }
    if (configItem && configItem.setEngineerScansToPage) {
      if (configItem.setEngineerScansToPage === 'repair') {
        this.goInternalRequest(asset, this.props.myself.userAccount.id)
      } else {
        history.push(`${route_urls.home_wx}/device/${asset.id}/${pageMap[configItem.setEngineerScansToPage]}`)
      }
    }
  }

  isTempOrGuestOrFE() {
    const { userAccount, userRoleList, multiTenant } = this.props.myself
    return userAccount.id === 0 || userRoleList.find(role => role.name === 'Guest') || multiTenant
  }

  isTempOrGuest() {
    const { userAccount, userRoleList } = this.props.myself
    return userAccount.id === 0 || userRoleList.find(role => role.name === 'Guest')
  }

  isFe() {
    const { userAccount, userRoleList } = this.props.myself
    const roleList = new Set(userRoleList.map(r => r.name))
    return userAccount.id !== 0 && (roleList.has('AssetStaff'))
  }

  isAssetAccesser() {
    const { userAccount, userRoleList, multiTenant } = this.props.myself
    const roleList = new Set(userRoleList.map(r => r.name))
    return userAccount.id !== 0 && (roleList.has('AssetStaff') || roleList.has('AssetHead') || multiTenant)
  }

  isOcrOperator() {
    const { userAccount, userRoleList, multiTenant } = this.props.myself
    const roleList = new Set(userRoleList.map(r => r.name))
    return (
      !util.isUniSite() &&
      userAccount.id !== 0 &&
      (roleList.has('AssetStaff') || roleList.has('AssetHead') || multiTenant) &&
      roleList.has('ITAdmin')
    )
  }

  isClinicalStaff() {
    const { userAccount, userRoleList } = this.props.myself
    const roleList = new Set(userRoleList.map(r => r.name))
    return userAccount.id !== 0 && (roleList.has('ClinicalStaff') || roleList.has('DeptHead'))
  }

  isExternalStaffOnly() {
    const { userRoleList } = this.props.myself
    return userRoleList.length === 1 && userRoleList[0].name === 'ExternalStaff'
  }

  nurseButtons() {
    const nurseStyle = this.isAssetAccesser() ? { backgroundColor: '#E3628A', color: '#fff' } : null
    const { result, showDaily, showPower } = isNurse()
    const powerCtrlLabel = util.isNursePower() ? '开关机登记' : '使用登记'
    if (!this.state.query && result) {
      return (
        <ButtonArea direction="horizontal">
          {showDaily && (
            <Button
              type="primary"
              style={nurseStyle}
              onClick={() =>
                QrCodeScan().then(qrCode => this.setState({ action: 'nurse' }, () => this.setQrCode(qrCode)))
              }
            >
              日常检查
            </Button>
          )}
          {showPower && (
            <Button
              type="primary"
              onClick={() =>
                QrCodeScan().then(qrCode => this.setState({ action: 'nursePowerCtrl' }, () => this.setQrCode(qrCode)))
              }
            >
              {powerCtrlLabel}
            </Button>
          )}
        </ButtonArea>
      )
    }
  }

  has_productNo = url => {
    let a = new URL(url)
    const productNo_number = a.href.match(/productNo=(\d+)/) // https:/xxxx/?productNo=082421200221&repairType=3
    if (productNo_number) {
      return a.href
    } else {
      return false
    }
  }

  render() {
    if (util.isIbSite()) {
      return (
        <Page
          header
          className="panel"
          title={
            <span>
              <Icon type="qrcode" /> 扫一扫
            </span>
          }
          subTitle="支持条形码和二维码, 也可以输入"
          footer={util.COPYRIGHT}
        >
          <ButtonArea>
            <ButtonArea direction="horizontal">
              <Button type="warn" onClick={() => util.goOneClickRepair({}, 'push')}>
                一键报修
              </Button>
              {getShowName() !== 'CUSTOMER' && (
                <Button type="primary" onClick={() => history.push('/wx/ib/qrcode')}>
                  绑定二维码
                </Button>
              )}
              <Button type="primary" onClick={() => history.push('/wx/spare/')}>
                备件扫码验真
              </Button>
            </ButtonArea>
          </ButtonArea>
        </Page>
      )
    }
    if (!this.state.qrCode) {
      return (
        <Page
          header
          className="panel"
          title={
            <span>
              <Icon type="qrcode" /> 扫一扫
            </span>
          }
          subTitle="支持条形码和二维码, 也可以输入"
          footer={util.COPYRIGHT}
        >
          <AcctToIb />
          {this.isExternalStaffOnly() ? (
            <h2 style={{ textAlign: 'center', color: 'gray' }}>外修医工无扫一扫功能</h2>
          ) : (
            <ButtonArea>
              {!this.state.query && (
                <ButtonArea direction="horizontal">
                  <Button
                    type={this.isAssetAccesser() ? 'primary' : 'warn'}
                    onClick={() =>
                      QrCodeScan().then(qrCode => {
                        if (~qrCode.indexOf('http')) {
                          if (isAPMBrowser()) {
                            // app 直接setQrCode查询资产信息，穿越会有问题。需要后续解决
                            this.setState({ action: 'events' }, () => this.setQrCode(qrCode))
                          } else {
                            const urlObj = new URL(qrCode)
                            const origin = urlObj.origin
                            if (util.ibSites.includes(origin) && ~urlObj.pathname.indexOf('/parts')) {
                              location.href = qrCode
                            } else if (util.isValidIbQrCode(qrCode)) {
                              location.href = util.isValidIbQrCode(qrCode)
                            } else if (this.has_productNo(qrCode)) {
                              location.href = this.has_productNo(qrCode)
                            } else {
                              this.setState({ action: 'events' }, () => this.setQrCode(qrCode))
                            }
                          }
                        } else {
                          this.setState({ action: 'events' }, () => this.setQrCode(qrCode))
                        }
                      })
                    }
                  >
                    立即扫码
                  </Button>
                  {this.isOcrOperator() && (
                    <Button type="default" onClick={() => (location.href = `${route_urls.home_wx}/device/ocr`)}>
                      识别铭牌
                    </Button>
                  )}

                  {this.props.myself.userAccount.tenantInfo.isEnablesAdverseEvents && util.hasFeature('ASSET_ADVERSE_EVENT') && (
                    <Button
                      onClick={() =>
                        history.push({
                          pathname: '/wx/adverseEvents/detail',
                          query: { eventsState: 'addReport', role: 'reporter' }
                        })
                      }
                    >
                      医疗器械不良事件上报
                    </Button>
                  )}
                </ButtonArea>
              )}
              {!util.isIbSite() && this.nurseButtons()}
              <ButtonArea>
                <input
                  className="weui-btn weui-btn_default"
                  placeholder="手工输入"
                  style={{ borderWidth: 'thin' }}
                  value={this.state.query}
                  onChange={e => this.setState({ query: e.target.value })}
                />
              </ButtonArea>
              {this.state.query && (
                <ButtonArea direction="horizontal">
                  {!this.isTempOrGuestOrFE() && (
                    <Button type="default" onClick={() => this.setAction('query')}>
                      查档
                    </Button>
                  )}
                  <Button type="warn" onClick={() => this.setAction('incident')}>
                    报修
                  </Button>
                  {this.isAssetAccesser() && (
                    <Button type="primary" onClick={() => this.setAction('maintain')}>
                      保养
                    </Button>
                  )}
                </ButtonArea>
              )}
            </ButtonArea>
          )}
        </Page>
      )
    } else return <div>数据检索中...</div>
  }
}

export const isNurse = () => {
  const { userAccount, userRoleList, customRoles } = util.myself()
  let authoritiesCH = []
  const wxCards = customRoles
    .concat(userRoleList)
    .reduce((last, curr) => {
      if (curr.authoritiesCH) {
        authoritiesCH = authoritiesCH.concat(curr.authoritiesCH.split(','))
      }
      last = last.concat(curr.wxCards)
      return last
    }, [])
    .filter((item, i, arr) => arr.indexOf(item) === i)
  authoritiesCH = authoritiesCH.filter((item, i, arr) => arr.indexOf(item) === i)

  if (wxCards.indexOf('nurse') > -1) {
    const showDaily = authoritiesCH.some(s => ['当日检查', '逾期检查', '有问题', '30天内通过'].indexOf(s) > -1)
    const showPower = authoritiesCH.some(s => ['未关机列表'].indexOf(s) > -1)
    return {
      result: showDaily || showPower,
      showDaily,
      showPower
    }
  } else {
    const roleList = new Set(userRoleList.map(r => r.name))
    return {
      result: userAccount.id !== 0 && roleList.has('NursingStaff'),
      showDaily: true,
      showPower: true
    }
  }
}

function mapStateToProps(state) {
  return {
    config: state.workflowConfig,
    myself: state.preload.myself
  }
}

export default connect(mapStateToProps)(ScanQrCode)
