import React from 'react'
import { urls, util } from '@/constants'
import { GEDeviceType } from '../AssetHeader'
import { Icon } from 'antd'
import ibImages from '@/images/application/application'

import '../ibChild.less'

const Report = props => {
  const filter = data => {
    let arr = []
    data.map(item => {
      GEDeviceType.push({
        value: '使用手册',
        label: '使用手册',
        type: 'others',
        key: [999],
        component: <Icon type="file-word" theme="twoTone" />
      })
      let report = GEDeviceType.filter(child => child.key.includes(item.deviceType))[0]
      const { id, deviceType, storageId } = item
      if (id) report.id = id
      if (deviceType) report.deviceType = deviceType
      if (storageId) report.storageId = storageId
      if (arr.every(childList => childList.value !== report.value)) {
        arr.push(report)
      }
    })
    return arr
  }
  const download = async id => {
    const downloadUrl = await util.download(id)
    if (downloadUrl) {
      location.href = downloadUrl
    }
  }
  return (
    <div className="report">
      {props.data &&
        filter(props.data).map((item, index) => {
          // const report = GEDeviceType.filter(child => child.key.includes(item.deviceType))[0]
          return (
            // <div className="inline" key={index}>
            <div key={index} style={{ width: '25%', textAlign: 'center' }} onClick={() => download(item.storageId)}>
              <div style={{ position: 'relative', width: '46px', margin: 'auto' }}>
                <img src={ibImages['reportDefault']} alt="" style={{ height: '55px', width: '46px' }} />
                <div
                  style={{
                    position: 'absolute',
                    left: '14px',
                    bottom: '10px',
                    zIndex: '9'
                  }}
                >
                  {item.component && item.component}
                </div>
              </div>

              <div className="report-title">{`APM 远程守护`}</div>
              <div className="report-title">{`${item.type ? item.value : ''}${
                !item.type ? item.value + '操作指南' : ''
              } `}</div>
            </div>
            // </div>
          )
        })}
    </div>
  )
}

export default Report
