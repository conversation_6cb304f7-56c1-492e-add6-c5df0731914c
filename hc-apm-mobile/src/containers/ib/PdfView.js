import React, { useEffect, useState } from 'react'
import { connect } from 'dva'
import { browserHistory as history } from 'react-router'
import DefaultPDF from './DefaultPDF'
import { Button, Modal, Icon, Spin } from 'antd'
import { urls, rest, util } from '@/constants'
import './pdfviewer.less'
import { setShareInfo } from '@/actions/jsApi'
import pdfImages from '@/images/application/pdf/pdf'
import Share from '@/components/Share'
import queryString from 'query-string'

import moment from 'moment'
// 统计
const count = '/gateway/hcapmcms/api/apm/cms/content/count/'

// `收藏
const collection = '/gateway/hcapmcms/api/apm/cms/collection/'
let scrollTop = 0
let initHeight = 0
function PDFViewer(props) {
  const [counts, setCount] = useState('')
  const [footer, setFooter] = useState('')
  const [showAll, setshowAll] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [pdfHeight, setpdfHeight] = useState('100%')
  const [updateModal, setupdateModal] = useState(false) //完善信息弹框
  const [topHide, setTopHide] = useState(false)
  const [favor, setFavor] = useState(false)
  const queryObj = props.location.query || null
  const [loading, setLoading] = useState(false)
  const scroll = () => {
    let viewHeader = document.querySelector('.header')
    let viewContent = document.querySelector('.body')
    let viewFooter = document.querySelector('.footer')
    scrollTop = document.documentElement.scrollTop || viewContent.scrollTop
    if (initHeight + 10 <= scrollTop && scrollTop > 10 && !topHide) {
      setTopHide(true)
      viewContent.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);height: 100%'
      viewHeader.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);margin-top: -60px;'
      viewHeader.addEventListener('transitionend', function setHeight() {
        viewHeader.removeEventListener('transitionend', setHeight, false)
      })
      viewFooter.style.cssText = 'transition: all 0.7s cubic-bezier(0.9, .1, 0.85, 1);transform: translateY(100px);'
      viewFooter.addEventListener('transitionend', function setHeight() {
        viewFooter.removeEventListener('transitionend', setHeight, false)
      })
    } else if (initHeight >= scrollTop + 5 && topHide) {
      setTopHide(false)
      viewHeader.style.cssText = 'transition: all 0.3s;margin-top: 0;'
      viewFooter.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);transform: inherit;'
      viewContent.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);height: calc(100% - 175px)'
    }
    initHeight = scrollTop
  }
  useEffect(() => {
    const acticleId = queryObj.id
    if (queryObj.id) {
      pdfDetail(acticleId)
      pdfCount(acticleId)
    }
  }, [queryObj.id])
  useEffect(() => {
    const { userAccount: { id } } = props
    if (queryObj && queryObj.userid && footer && queryObj.userid != id) {
      let params = {
        type: `智学堂-分享-${footer.title}`,
        target: `用户 [${queryObj.userid}] `
      }
      util.log(params)
    }
  }, [footer.id])

  // useEffect(() => {
  //   if (footer) setpdfHeight('100% - 190px')
  // }, [footer])
  const share = data => {
    setShowShare(!showShare)
  }
  const defaultShare = data => {
    let useLink =
      `${location.origin}/wx/ib/pdfview?id=${data.id}&userid=` +
      (queryObj && queryObj.userid ? queryObj.userid : props.userAccount.id)
    const config = {
      title: data.title,
      desc: data.summary || 'PDF分享',
      link: useLink,
      imgUrl: `${location.origin}${urls.obj}${data.cover}`
    }
    return config
  }
  useEffect(() => {
    let header = '',
      footer = ''
    if (document.getElementById('header')) {
      header = document.getElementById('header').clientHeight
    }
    if (document.getElementById('footer')) {
      footer = document.getElementById('footer').clientHeight
    }
    let total = Number(header) + Number(footer)
    setpdfHeight(`100% - ${total}px`)
  }, [footer])

  useEffect(() => {
    let header = '',
      footer = ''
    if (document.getElementById('header')) {
      header = document.getElementById('header').clientHeight
    }
    if (document.getElementById('footer')) {
      footer = document.getElementById('footer').clientHeight
    }
    let total = Number(header) + Number(footer)
    topHide ? setpdfHeight(`100%`) : setpdfHeight(`100% - ${total}px`)
  }, [showAll])
  useEffect(() => {
    return () => {
      if (top.location !== location) {
        window.parent.postMessage(
          {
            type: 'setShareInfo',
            payload: null
          },
          '*'
        )
      }
    }
  }, [])

  const getNewUrl = params => {
    let useLink =
      `/wx/ib/pdfview?id=${params.id}&userid=` + (queryObj && queryObj.userid ? queryObj.userid : props.userAccount.id)
    return useLink
  }
  const pdfDetail = id => {
    setLoading(true)
    rest.get(`${urls.articleInfo}/${id}`).then(res => {
      setLoading(false)
      if (res && !res.limiting) {
        setFooter(res)
        setFavor(res.collection)
        util.log({ type: `查看智学堂文章`, target: `智学堂+${res.title}` })
        if (top.location !== location) {
          window.parent.postMessage(
            {
              type: 'setShareInfo',
              payload: defaultShare(res)
            },
            '*'
          )
        } else {
          setShareInfo(defaultShare(res))
        }
        window.history.replaceState(null, window.title, getNewUrl(res))
        // history.replace(getNewUrl(res))
      } else if (res && res.limiting) {
        setupdateModal(true)
      }
    })
  }
  const pdfCount = id => {
    rest.get(`${count}${id}`).then(res => {
      setCount(res)
    })
  }
  const pdfCollection = id => {
    rest.get(`${collection}${id}`).then(res => {
      res && setFavor(res.status == 0 ? true : false)
    })
  }
  const fullScreen = () => {
    let viewHeader = document.querySelector('.header')
    let viewContent = document.querySelector('.body')
    let viewFooter = document.querySelector('.footer')
    if (topHide) {
      setTopHide(false)
      viewContent.style.cssText = 'height: calc(100% - 170px)'
      viewHeader.style.cssText = 'transition: all 0.3s;margin-top: 0;'
      viewFooter.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);transform: translateY(0);'
    } else {
      setTopHide(true)
      viewContent.style.cssText = 'height: 100%'
      viewHeader.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);margin-top: -60px;'
      viewHeader.addEventListener('transitionend', function setHeight() {
        viewHeader.removeEventListener('transitionend', setHeight, false)
      })
      viewFooter.style.cssText = 'transition: all 0.5s cubic-bezier(0.9, .1, 0.85, 1);transform: translateY(100px);'
      viewFooter.addEventListener('transitionend', function setHeight() {
        viewFooter.removeEventListener('transitionend', setHeight, false)
      })
      setshowAll(false)
    }
  }
  const viewCont = (data, fun, viewState) => {
    switch (data.articleType) {
      case 1:
        if (data.contentType === 0) {
          let articleData = JSON.parse(data.content)
          return (
            <div>
              {articleData.map(item => {
                return <img key={item.objectId} style={{ width: '100%' }} src={urls.obj + item.objectId} />
              })}
            </div>
          )
        } else if (data.contentType === 2) {
          return (
            <div
              onClick={event => {
                linktoPage(event)
              }}
              dangerouslySetInnerHTML={{ __html: data.content }}
            ></div>
          )
        } else {
          return null
        }
      case 2:
        return (
          <div>
            <VideoSrc source={data.articleUrl} cover={data.cover} sourceType={data.sourceType} />
            {data.videoText && (
              <div
                style={{ wordBreak: 'break-all' }}
                onClick={event => {
                  linktoPage(event)
                }}
                dangerouslySetInnerHTML={{ __html: data.videoText }}
              ></div>
            )}
          </div>
        )
      case 3:
        return (
          <div style={{ height: '100%' }}>
            <span className="screen-btn" onClick={fun}>
              {viewState ? '退出全屏' : '进入全屏'}
              <Icon type={`${viewState ? 'shrink' : 'arrows-alt'}`} />
            </span>
            <DefaultPDF sourceType={data.sourceType} id={data.articleUrl}></DefaultPDF>
          </div>
        )
      default:
        break
    }
  }

  const linktoPage = event => {
    let target = event.target || event.srcElement
    while (target.tagName && target.tagName.toLowerCase() != 'a') {
      if (target.tagName.toLowerCase() == 'a') {
        break
      }
      target = target.parentNode
    }

    if (event.preventDefault) {
      event.preventDefault()
    } else {
      window.event.returnValue = true
    }
    const url = decodeURIComponent(target.getAttribute('href'))

    // 检查URL是否包含协议，如果没有则添加默认协议
    let fullUrl = url
    if (!/^[a-zA-Z]+:\/\//.test(fullUrl)) {
      fullUrl = 'https://' + fullUrl
    }

    try {
      const urlObj = new URL(fullUrl)
      console.log(urlObj, 'urlObj')

      // 更简洁的获取查询参数的方式
      let locationQuery = queryString.parse(urlObj.search)
      console.log(urlObj.pathname, 'pathName')
      console.log(locationQuery, 'locationQuery')
      util.log({ type: '智学堂-富文本-链接跳转', target: `${url}` })
      if (urlObj.pathname.includes('/wx')) {
        if (urlObj.pathname.includes('/wx/ib/device')) {
          util.log({ type: '微工作台', target: `IB应用 - 资产列表` })
        }
        if (urlObj.pathname.includes('/wx/connectivity/study/studyIndex')) {
          util.log({ type: '微工作台', target: `IB应用 - 智学堂` })
          if (document.getElementsByClassName('iframe-box')[0]) {
            history.go(-1)
          }
        }
        history.push({
          pathname: urlObj.pathname,
          query: { ...locationQuery }
        })
      } else {
        window.location.href = fullUrl
      }
    } catch (error) {
      console.error('URL解析错误:', error)
      // 处理解析失败的情况
    }
  }
  return (
    <div className="pdf-viewer" style={{ width: '100%', height: '100vh' }}>
      <PdfHeader></PdfHeader>
      <Spin spinning={loading} tip="获取文章数据..." delay={300}>
        <div className="pdf-body">
          <div className="pdf-header"></div>
          <div className="pdf-body">
            {footer && (
              <div className="header" id="header">
                <div className="header-title">{footer.title}</div>
                <div className="header-body">
                  {footer.creator && <p>作者: {footer.creator}</p>}
                  <p>{moment(footer.pubTime).format('YYYY-MM-DD')}</p>
                  <p>
                    {counts && counts.viewCount} 阅读&nbsp;&nbsp; <img src={pdfImages.attach} alt="" />
                  </p>
                </div>
              </div>
            )}
            {footer && (
              <div className="body" onScroll={scroll} style={{ height: `calc(${pdfHeight})` }}>
                {viewCont(footer, fullScreen, topHide)}
              </div>
            )}
            {footer && (
              <div className="footer" id="footer">
                <div className="target">
                  <div className="title">标签: </div>
                  <div className="target-title">
                    <div>
                      {JSON.parse(footer.tags).map(item => {
                        return <p key={item.id}>{item.name}</p>
                      })}
                    </div>
                  </div>
                </div>
                {showAll && (
                  <div className="bref">
                    <div className="title">简介: </div>
                    <div className="bref-body">{footer.summary}</div>
                  </div>
                )}
                {
                  <Button
                    type="link"
                    size={'small'}
                    style={{ color: '#037AA6' }}
                    onClick={e => {
                      e.stopPropagation()
                      setshowAll(!showAll)
                    }}
                  >
                    {!showAll ? '显示简介' : '隐藏简介'}
                  </Button>
                }

                <div className="share">
                  <div
                    style={{ display: 'flex' }}
                    onClick={() => {
                      share(footer)
                    }}
                  >
                    分享&nbsp; <img src={pdfImages.share} alt="" />
                    {showShare && <Share />}
                  </div>
                  <div
                    onClick={() => {
                      pdfCollection(footer.id)
                    }}
                    style={{ display: 'flex' }}
                  >
                    收藏&nbsp; <img src={favor ? pdfImages.Star : pdfImages.Starblank} alt="" />
                  </div>
                  {/* <div style={{display:'flex'}}>
                下载文档&nbsp; <img src={pdfImages.download} alt="" />
              </div> */}
                </div>
              </div>
            )}
          </div>
          <div className="pdf-footer"></div>
        </div>
      </Spin>
      <Modal
        visible={updateModal}
        closable={false}
        maskClosable={false}
        getContainer={() => document.getElementsByClassName('pdf-viewer')[0]}
        footer={[
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              history.push({ pathname: '/wx/user/register/update' })
            }}
          >
            确认
          </Button>
        ]}
        bodyStyle={{ textAlign: 'center' }}
      >
        已读完五篇文章,完善信息阅读更多文章！
      </Modal>
    </div>
  )
}

const PdfHeader = props => {
  return (
    <div className="pdf-title">
      <Title></Title>
      <Description></Description>
    </div>
  )
}
const VideoSrc = props => {
  let source = props.sourceType == 1 ? `${urls.obj}${props.source}` : props.source
  return (
    <div>
      <video
        id="myVideo"
        controls
        preload="auto"
        controlsList="nodownload"
        poster={`${urls.obj}${props.cover}`}
        data-setup="{}"
        style={{ width: '100%', height: 'auto' }}
      >
        <source id="source" src={source} type="video/mp4"></source>
      </video>
    </div>
  )
}

const pdfTitleStyle = {
  textAlign: 'center',
  fontSize: '20px'
}
const pdfDeStyle = {
  textAlign: 'right',
  fontSize: '20px',
  paddingRight: '20px'
}
const Title = props => {
  return <div style={pdfTitleStyle}>{props.children}</div>
}
const Description = props => {
  return <div style={pdfDeStyle}>{props.children}</div>
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    userAccount: state.preload.myself.userAccount
  }
}
export default connect(mapStateToProps)(PDFViewer)
