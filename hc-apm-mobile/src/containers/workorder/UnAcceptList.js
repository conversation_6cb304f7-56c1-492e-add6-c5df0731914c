import React from 'react'
import { browserHistory as history } from 'react-router'
import { List, CheckBox } from '../../components'
import { route_urls,rest, urls } from '../../constants'
import { badgeStyle } from './ConsoleSRList'
export default class UnAcceptList extends React.Component {

  constructor(props) {
    super(props)
    this.state = {
      loading: false,
      hasMore: false,
      page: 0,
      conditions: [
        { label: '我的工单', value: 0 },
        { label: '可抢工单', value: 1 }
      ],
      currentCons: this.props.location.query.con||0,
      request: {
        title: '工单列表',
        count: 0,
        data: [],
        itemHeader: { key: 'serviceRequest.srNum', label: '报修编号' },
        itemContent: [
          { key: 'assetInfo.siteName', label: '所属院区' },
          { key: 'assetInfo.financingNum', label: '资产编号' },
          { key: 'serviceRequest.assetName', label: '资产名称' },
          { key: 'assetInfo.functionType', label: '型号' },
          { key: 'assetInfo.clinicalDeptName', label: '所属科室' },
          { key: 'serviceRequest.requestorName', label: '报修人' },
          { key: 'serviceRequest.requestTime', label: '报修时间' },
          { key: 'currentPersonName', label: '当前处理人'},
          { key: 'intExtType', label: '工单类型', type: 'intExtType' },
          { key: 'comment', label: '故障描述'},
          { key: 'serviceRequest.repairReportSendPlace', i18n: 'repairReportSendPlace', type: 'repairReportSendPlace' },
          // { key: 'currentStepId', label: '当前步骤', type: 'ProgressBar'}
          { key: 'currentStepId', label: '当前步骤', type:'woSteps' }
        ],
        badge: { key: 'serviceRequest.casePriority', label: '紧急程度', type: 'casePriority' },
        badgeStyle
      }
    }
    this.eventClick = this.eventClick.bind(this)
    this.eventOnMore = this.eventOnMore.bind(this)
    this.woToPickup = this.woToPickup.bind(this)
  }

  componentWillMount() {
    this.woToPickup(this.state.currentCons)
  }

  woToPickup(con) {
    this.setState({ loading: true, page: 0})
    history.replace(route_urls.home_wx + '/unAcceptList?con=' + con)
    rest.list(urls.woToPickup, {type : con, page: 0})
      .then(res => {
        this.state.request.data = res.data
        this.state.request.count = res.rowCount
        this.state.hasMore = res.data.length == 10
        this.state.loading = false
        this.setState(this.state)
      })
  }

  eventClick(wo) {
    history.push({pathname:route_urls.wx.workorder + '/' + wo.id, query: {from: 'repairList', con: this.state.currentCons }})
  }

  eventOnMore() {
    this.state.page++
    this.setState(this.state, () => this.loadMoreData())
  }

  loadMoreData() {
    this.setState({loading: true})
    rest.list( urls.woToPickup, {type : this.state.currentCons, page: this.state.page} )
      .then(res => {
        this.state.request.data = this.state.request.data.concat(res.data)
        this.state.hasMore = res.data.length == 10
        this.state.loading = false
        this.setState(this.state)
      })
  }

  render() {
    return (
      <div>
        <CheckBox data={this.state.conditions}
          defaultValue={this.state.currentCons}
          onCheck={currentCons => {
            this.setState({ currentCons })
            this.woToPickup(currentCons)
          }}/>
        <List {...this.state.request} onClick={this.eventClick} loading={this.state.loading} hasMore={this.state.hasMore} onMore={this.eventOnMore}/>
      </div>
    )
  }
}
