import React, { useState } from 'react';
import { Form, List } from '@components';
import { message, Modal, Input } from 'antd';
import { useSelector } from 'react-redux';
import { rest, urls } from '@constants';
import { LinkDrawer } from '@containers/report/quarterlyReport/components/drawer';

// 提取公共取消按钮配置
const getCancelButton = (onSubmit) => ({
  label: '取消工单',
  type: 'warn',
  onClick: () => {
    let cancelReason = '';
    Modal.confirm({
      title: '确定要取消工单吗？',
      content: (
        <div>
          <span>取消理由：</span>
          <Input.TextArea
            style={{ width: '100%' }}
            rows={5}
            onChange={e => cancelReason = e.target.value}
            autoFocus
          />
        </div>
      ),
      okText: '确认取消工单',
      okButtonProps: { type: 'danger' },
      cancelText: '不取消',
      onOk: () => {
        if (!cancelReason) {
          message.error('请输入取消理由');
          return Promise.reject();
        }
        onSubmit('cancel', { cancelReason });
      }
    });
  }
});

// 优化表单配置函数（避免在内部使用hooks）
const getFormConfig = (status, data, onSubmit, setWoItems, orgUid) => {
  const buttons = status === '5' ? [] : [getCancelButton(onSubmit)];
  let onChange = null;
  let groups = [];

  switch (status) {
    case '2': // 待派工
      groups = [{
        items: [
          {
            label: '接单人',
            key: 'assigneeId',
            component: 'SelectUser',
            rule: { required: true },
            props: { url: `${urls.serviceRequestsNoAsset}/assigneeUsers?orgUid=${orgUid}` }
          },
          { label: '备注', key: 'desc', component: 'TextArea', full: false }
        ]
      }];
      buttons.push({
        label: '派工',
        type: 'primary',
        confirm: '确定要派工吗？',
        validation: true,
        onClick: formData => onSubmit('assign', formData)
      });
      break;

    case '3': // 待接单
      groups = [{
        items: [
          { label: '预估完成时间', key: 'estimatedCloseTime', component: 'DateTime', rule: { required: true } },
          {
            label: '维修类型',
            key: 'repairType',
            type: 'repairType',
            component: 'Select',
            rule: { required: true }
          },
          { label: '备注', key: 'desc', component: 'TextArea', full: false }
        ]
      }];
      buttons.push({
        label: '接单',
        type: 'primary',
        confirm: '确定要接单吗？',
        validation: true,
        onClick: formData => {
          formData.estimatedCloseTime && 
            (formData.estimatedCloseTime = formData.estimatedCloseTime.replace('T', ' '));
          onSubmit('accept', formData);
        }
      });
      break;

    case '4': // 待关单
      groups = [{
        items: [
          {
            label: '关联设备',
            key: 'asset',
            component: 'MasterAsset',
            rule: { required: true },
            props: { editable: true, searchPlaceholder: '设备查询' }
          },
          { label: '备注', key: 'desc', component: 'TextArea', full: false }
        ]
      }];
      buttons.push(
        {
          label: '关单',
          type: 'primary',
          confirm: '确定要关单吗？',
          onClick: formData => onSubmit('close', formData)
        },
        {
          label: '转台账',
          type: 'primary',
          confirm: '确定要到这个设备工单吗？',
          validation: true,
          onClick: formData => onSubmit('turnWorkOrder', { assetUid: formData.asset.uid })
        }
      );
      onChange = async formData => {
        if (formData.asset) {
          const res = await rest.list(urls.request, { 
            status: 1, 
            page: 0, 
            pageSize: 100, 
            assetId: formData.asset.id 
          });
          if (res && res.rowCount > 0) {
            message.info('该设备有已存在的维修工单，请查看');
            setWoItems(res.data);
          } else {
            message.info('该设备没有关联的工单, 可以转台账');
          }
        }
      };
      break;
  }

  return {
    key: 'action',
    title: '工单操作',
    onChange,
    data,
    groups,
    buttons
  };
};

const NoAssetWoAction = ({ data, status }) => {
  // 组件顶层使用hooks，符合React规范
  const { userAccount: { orgUid } } = useSelector(state => state.preload.myself);
  
  // 状态合并简化
  const [modalState, setModalState] = useState({
    visible: false,
    link: null,
    woItems: null
  });

  // 简化状态更新函数
  const setWoItems = (items) => setModalState(prev => ({
    ...prev,
    visible: true,
    woItems: items
  }));

  // 提交处理函数简化
  const onSubmit = async (action, postData) => {
    const { id, currentStepId } = data || {};
    try {
      const res = await rest.post(
        `${urls.serviceRequestsNoAsset}/${id}/action?type=${action}`,
        { ...postData, currentStepId }
      );
      if (res === 'success') {
        message.success('操作成功');
        window.history.back();
      }
    } catch (e) {
      message.error('操作失败，请重试');
      console.error(e);
    }
  };

  // 工单点击处理
  const handleWoClick = wo => setModalState(prev => ({
    ...prev,
    link: `${location.origin}/wx/workorder/${wo.id}`
  }));

  // 表单配置
  const formConfig = getFormConfig(status, data, onSubmit, setWoItems, orgUid);

  return (
    <React.Fragment>
      <Form value={data} {...formConfig} />
      
      <Modal
        title={null}
        visible={modalState.visible}
        onCancel={() => setModalState(prev => ({ ...prev, visible: false }))}
        footer={null}
        style={{ width: '90%' }}
        bodyStyle={{
          maxHeight: '80vh',
          minHeight: '50vh',
          overflowY: 'auto',
          padding: 0,
          margin: 0
        }}
        centered
        closable
      >
        <List
          title='正在进行中的报修（点击查看详情）'
          data={modalState.woItems}
          itemHeader={{ key: 'srNum' }}
          badge={{ key: 'casePriority', label: '紧急程度', type: 'casePriority' }}
          itemContent={[
            { key: 'assetName', label: '设备名称' },
            { key: 'requestorName', label: '报修人' },
            { key: 'requestTime', label: '报修时间' },
            { key: 'requestReason', label: '报修原因' },
            { key: 'currentStepId', label: '当前步骤', type: 'woSteps' },
          ]}
          onClick={handleWoClick}
        />
      </Modal>
      
      <LinkDrawer 
        visible={!!modalState.link} 
        setVisible={(visible) => setModalState(prev => ({ ...prev, link: visible ? prev.link : null }))} 
        linkurl={modalState.link} 
        height={'95%'} 
      />
    </React.Fragment>
  );
};

export default NoAssetWoAction;
