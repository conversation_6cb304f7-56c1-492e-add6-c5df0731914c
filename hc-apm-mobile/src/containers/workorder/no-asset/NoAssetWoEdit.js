import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { message } from 'antd'
import { Form } from '@components'
import { rest, urls } from '@constants'

const NoAssetWoEdit = props => {
  const [data, setData] = useState(props.data || {})
  const [show, setShow] = useState(false)
  const [org, setOrg] = useState(null)
  const myself = useSelector(state => state.preload.myself)

  useEffect(() => {
    if (!myself) return

    const { userAccount } = myself

    const fetchOrgAndInitData = async () => {
      try {
        const orgData = await rest.get(`${urls.org}/visible/${userAccount.siteUID}`)
        setOrg(orgData)

        // 初始化表单数据
        const initialData = {
          ...data,
          requestorName: userAccount.name,
          requestorPhone: userAccount.telephone
        }
        if (userAccount.orgUid !== userAccount.siteUID) {
          initialData.deptUid = userAccount.orgUid
        }
        setData(initialData)
        setShow(true)
      } catch (e) {
        // 可根据需要添加错误处理
      }
    }

    fetchOrgAndInitData()
  }, [myself])

  const validateImgAudioText = form => {
    if (!form.voiceAttachment && !form.requestReason && !form.attachments) {
      return {
        msg: '图片、视频，语音和文字至少一项有内容'
      }
    }
  }

  const onSubmit = form => {
    const submitData = {
      ...data,
      ...form
    }
    // 处理 confirmedDownTime 去掉 T
    if (submitData.confirmedDownTime && typeof submitData.confirmedDownTime === 'string') {
      submitData.confirmedDownTime = submitData.confirmedDownTime.replace('T', ' ')
    }

    // 合并 voiceAttachment 到 attachments，并删除 voiceAttachment
    if (submitData.voiceAttachment) {
      if (!submitData.attachments) {
        submitData.attachments = []
      }
      // 假设 voiceAttachment 是单个对象或数组
      if (Array.isArray(submitData.voiceAttachment)) {
        submitData.attachments = submitData.attachments.concat(submitData.voiceAttachment)
      } else {
        submitData.attachments.push(submitData.voiceAttachment)
      }
      delete submitData.voiceAttachment
    }
    console.log('提交数据', submitData)
    rest.post(urls.serviceRequestsNoAsset, submitData).then(res => {
      if (res && res.id) {
        console.log('提交成功', res)
        message.success('报修成功')
        window.history.back()
      }
    })
  }

  const formGroups = [
    {
      title: '设备信息',
      items: [
        { key: 'assetName', label: '设备名称', rule: { required: true } },
        {
          key: 'casePriority',
          type: 'casePriority',
          label: '紧急程度',
          component: 'Select',
          first: false,
          rule: { required: true }
        },
        {
          key: 'assetStatus',
          label: '资产状态',
          options: [
            {
              key: 2,
              value: '停机'
            },
            {
              key: 3,
              value: '限制功能使用'
            }
          ],
          component: 'Select',
          rule: { required: true }
        },
        {
          key: 'confirmedDownTime',
          label: '停机时间',
          component: 'DateTime',
          hide: form => {
            return form.assetStatus != 2
          },
          placeholder: '请选择停机时间...',
          rule: { required: true }
        }
      ]
    },
    {
      title: '报修信息',
      items: [
        {
          key: 'deptUid',
          label: '所属科室',
          component: 'SelectOrgNew',
          props: { org, disableOrg: item => item.orgType !== 4 },
          rule: { required: true },
          transform: org => org.clinicalDeptUID
        },
        { key: 'requestorName', label: '报修人', rule: { required: true } },
        { key: 'requestorPhone', label: '报修人电话', rule: { required: true } }
      ]
    },
    {
      keyIdx: 'attachment',
      title: '故障信息（图片、视频，语音和文字至少一项有内容）',
      items: [
        {
          key: 'attachments',
          component: 'ImageUploader',
          label: '故障、视频图片',
          props: {
            title: '图片、视频上传',
            allowVideo: true,
            maxCount: 5,
            autoSave: true
          },
          rule: { custom: validateImgAudioText }
        }
      ]
    },
    {
      keyIdx: 'voiceAttachment',
      title: '故障语音描述',
      items: [
        {
          key: 'voiceAttachment',
          label: '故障语音描述',
          component: 'Audio',
          rule: { custom: validateImgAudioText }
        }
      ]
    },
    {
      keyIdx: 'requestReason',
      title: '故障文字描述',
      items: [
        {
          key: 'requestReason',
          label: '故障文字描述',
          component: 'TextArea',
          rule: { custom: validateImgAudioText }
        }
      ]
    }
  ]
  const buttons = [
    { type: 'default', label: '取消', onClick: () => window.history.back() },
    { type: 'primary', label: '提交', validation: true, onClick: onSubmit }
  ]

  return show && <Form title="无台账报修" value={data} groups={formGroups} buttons={buttons} />
}

export default NoAssetWoEdit
