import React, { useState, useEffect } from 'react'
import { message } from 'antd'
import { rest, urls } from '@constants'
import NoAssetWoEdit from './NoAssetWoEdit'
import NoAssetWoPreview from './NoAssetWoPreview'
import NoAssetWoAction from './NoAssetWoAction'

const NoAssetWo = props => {
  const [data, setData] = useState({})
  const [mode, setMode] = useState('')

  useEffect(() => {
    const wo = props.location && props.location.state && props.location.state.wo
    if (wo) {
      setData(wo)
      setMode('action')
      return
    }
    const id = props.location && props.location.query && props.location.query.id
    if (!id) {
      setMode('edit')
    } else {
      rest
        .get(`${urls.serviceRequestsNoAsset}/${id}`)
        .then(response => {
          setData(response)
          setMode('action')
        })
        .catch(() => {
          message.error('获取工单数据失败')
        })
    }
  }, [props.location && props.location.query])

  return (
    <React.Fragment>
      {mode === 'edit' && <NoAssetWoEdit data={data} />}
      {mode === 'action' && <NoAssetWoPreview data={data} />}
      {mode === 'action' && <NoAssetWoAction data={data} status={props.location.query.status} />}
    </React.Fragment>
  )
}

export default NoAssetWo
