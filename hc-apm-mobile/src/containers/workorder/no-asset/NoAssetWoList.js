import React, { useState } from 'react';
import { browserHistory as history } from 'react-router';
import { APMList as List } from '@components';
import { urls, route_urls } from '@constants';
import { Input } from 'antd';

const NoAssetWoList = ({ location }) => {
  const [keyword, setKeyword] = useState('');

  const handleSearch = value => setKeyword(value);

  const query = {
    status: location.query && location.query.status ? location.query.status : 0,
    keyword,
  };

  const handleItemClick = wo => {
    history.push({
      pathname: `/wx/no-asset-wo`,
      query: { status: query.status, id: wo.id },
      state: { wo }
    });
  };

  const listProps = {
    title: '无台账报修列表',
    url: urls.serviceRequestsNoAsset,
    query,
    itemHeader: { key: 'srNum', label: '报修编号' },
    itemContent: [
      { key: 'assetName', label: '设备名称' },
      { key: 'deptName', label: '报修科室' },
      { key: 'requestorName', label: '报修人' },
      { key: 'requestorPhone', label: '报修人电话' },
      { key: 'requestTime', label: '报修时间' },
      { key: 'requestReason', label: '故障描述' },
    ],
    badge: { key: 'casePriority', label: '紧急程度', type: 'casePriority' },
    onClick: handleItemClick,
  };

  return (
    <React.Fragment>
      <div style={{ padding: 8 }}>
        <Input.Search
          placeholder="请输入搜索内容"
          allowClear
          enterButton="搜索"
          onSearch={handleSearch}
        />
      </div>
      <List {...listProps} />
    </React.Fragment>
  );
};

export default NoAssetWoList;