import React from 'react'
import { Preview, ImageUploader, Audio } from '@components'

const NoAssetWoPreview = props => {
  const { data } = props
  let voiceAttachment = null
  if (Array.isArray(data.attachments)) {
    const audioAttachments = data.attachments.filter(att => att.objectType && att.objectType.startsWith('audio/'))
    voiceAttachment = audioAttachments.length > 0 ? audioAttachments[0] : null
    // 只保留 image/* 和 video/* 类型的附件
    data.attachments = data.attachments.filter(att =>
      att.objectType &&
      (att.objectType.startsWith('image/') || att.objectType.startsWith('video/'))
    )
    data.voiceAttachment = voiceAttachment
  } else {
    data.voiceAttachment = null
  }
  const config = {
    title: '工单详情',
    data,
    brief: [
      { key: 'srNum', label: '报修编号' },
      { key: 'assetName', label: '设备名称' },
      { key: 'casePriority', label: '紧急程度', type: 'casePriority' },
      { key: 'assetStatus', label: '资产状态', type: 'assetStatus' },
      data.assetStatus === 2 && { key: 'confirmedDownTime', label: '停机时间', hide: item => item.assetStatus !== 2 },
      { key: 'siteName', label: '报修单位' },
      { key: 'deptName', label: '报修科室' },
      { key: 'requestorName', label: '报修人' },
      { key: 'requestorPhone', label: '报修人电话' },
      { key: 'requestTime', label: '报修时间' },
      { key: 'requestReason', label: '故障描述' }
    ].filter(Boolean)
  }

  return (
    <React.Fragment>
      <Preview {...config} />
      {data.attachments && data.attachments.length > 0 && (
        <ImageUploader
          files={data.attachments}
          previewMode
          title="故障、视频图片"
          allowVideo={true}
        />
      )}
      {data.voiceAttachment && (
        <Audio
          attachment={data.voiceAttachment}
          title="故障语音描述"
        />
      )}
    </React.Fragment>
  )
}

export default NoAssetWoPreview
