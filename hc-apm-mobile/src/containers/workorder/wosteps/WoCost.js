import React from 'react'
import { connect } from 'dva'
import { urls, rest } from '../../../constants'
import moment from 'moment'
import { message } from 'antd'
import { Form as GeneralForm, Dialog as GeneralDialog } from '../../../components'
@connect(state => ({
  supplier: state.device.get('supplier'),
  extraSuppliers: state.device.get('extraSuppliers'),
  workflowConfig: state.workflowConfig.get('workflowConfig')
}))
class WoCost extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      woType: props.woType,
      showCostForm: false,
      initTime: new Date(),
      buttons:
        props.readOnly || props.isMultiVerifyBtnDisabled
          ? [{ type: 'default', label: '返回', onClick: val => this.cancel() }]
          : [
            { type: 'default', label: '取消', onClick: val => this.cancel() },
            {
              type: 'primary',
              label: '保存',
              confirm: props.workflowConfig && props.workflowConfig.woSparePartsSign ? null : '确定保存？',
              validation: true,
              onClick: this.submit
            }
          ],
      suppliers: props.supplier,
      cost: props.cost
        ? props.cost
        : {
          parts: '',
          apartsQuantity: '',
          partsPrice: '',
          estimatedArriveTime: '',
          applyTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          arrivedTime: '',
          serialNum: '',
          warrantyDate: '',
          arrived: '',
          partsType: '',
          invoiceNum: '',
          contractNum: '',
          invoiceTime: '',
          comment: '',
          supplier: '',
          priority: '',
          arrivedGood: false,
          arrivedGoodReturn: false,
          deliverTime: '',
          purchaseTime: '',
          closedPrice: '',
          woId: props.woId
        },
      coworker: props.coworker
        ? props.coworker
        : {},
      partsIdKeyObj: {},
      assetId: props.assetId,
      multiTenant: props.multiTenant,
      closedPriceFlag: props.cost ? props.cost.partsPrice : ''
    }
    if (this.props.cost && this.props.cost.warrantyDate) {
      let cost = this.state.cost
      cost.warrantyDate = moment(cost.warrantyDate).format('YYYY-MM-DD')
      this.setState({ cost })
    }
    this.eventBind = this.eventBind.bind(this)
  }

  async componentDidMount() {
    const coworkGroups = [
      {
        items: [
          {
            key: 'startTime',
            label: '开始时间',
            component: 'DateTime',
            rule: { required: true, lt: 'endTime' },
            hide: val => this.props.coworkerEntryType != 1
          },
          {
            key: 'endTime',
            label: '结束时间',
            component: 'DateTime',
            rule: { required: true, gt: 'startTime' },
            hide: val => this.props.coworkerEntryType != 1
          },
          {
            key: 'cowokerUserId',
            label: '协作者',
            component: 'SelectUser',
            rule: { required: this.props.assetId ? true : false },
            props: {
              defaultValue: this.state.coworker.cowokerUserId,
              excludes: [this.props.currentLoginPersonId],
              assetId: this.state.assetId,
            },
            onChange: (...param) => {
              const [,,, user] = param
              this.setState({
                coworker: {
                  ...this.state.coworker,
                  cowokerUserId: user.id,
                  cowokerUserName: user.name,
                }
              })
            },
            rule: { required: true }
          },
          {
            key: 'manHours',
            label: '工时(小时)',
            component: 'Number',
            rule: { required: true, min: 0, max: 999 },
            hide: val => this.props.coworkerEntryType != 0
          },
          {
            key: 'remark',
            label: '备注',
          }
        ]
      }
    ]
    this.setState({ coworkGroups })
    rest.get(urls.customConfig, { range: 'pageWorkOrder', siteUid: this.props.siteUID }).then(res => {
      if (res && res.pageWorkOrder && res.pageWorkOrder.accessoryDefaultType && this.props.opTypeIndex == -1) {
        let cost = this.state.cost
        cost.partsType = parseInt(res.pageWorkOrder.accessoryDefaultType)
        this.setState({ cost })
      }
      this.setState({ showCostForm: true })
    })
    let that = this
    // let supplierPlus = this.props.supplier.concat(this.props.extraSuppliers.map(s => ({ supplier_t: s.supplier_t, maintainer_t: s.maintainer_t, name: s.name })))
    await this.props.dispatch({ type: 'device/get/extraSuppliers' })
    let supplierPlus = this.props.extraSuppliers.map(s => ({
      supplier_t: s.supplierT,
      maintainer_t: s.maintainerT,
      name: s.name
    }))
    supplierPlus = Array.from(new Set(supplierPlus))
    const { approved } = this.state.cost
    const disabled = this.props.readOnly
    const disabledApproved = approved === true || approved === false

    let partsConfig = await rest.get(`${urls.backup_psrts_config_location}?assetUid=${this.props.assetUid}&currentPersonId=${this.props.currentPersonId}`)

    let storagePartsItemList = await rest.get(`${urls.getPartsItemList}?partsType=1&assetUid=${this.props.assetUid}&currentPersonId=${this.props.currentPersonId}`) //1-库房领用
    let purchasePartsItemList = await rest.get(`${urls.getPartsItemList}?partsType=2&assetUid=${this.props.assetUid}&currentPersonId=${this.props.currentPersonId}`) //2-医工采购
    let partsIdKeyObj = {}
    storagePartsItemList &&
      storagePartsItemList.map(item => {
        partsIdKeyObj[item.id] = item.name
      })
    purchasePartsItemList &&
      purchasePartsItemList.map(item => {
        partsIdKeyObj[item.id] = item.name
      })
    this.state.partsIdKeyObj = partsIdKeyObj

    this.state.groups = [
      {
        items: [
          {
            key: 'partsType',
            label: '备件类别',
            component: 'Select',
            type: 'partsType',
            props: { disabled },
            rule: { required: true },
            onChange: e => {
              let cost = this.state.cost
              cost.parts = ''
              cost.apartsQuantity = ''
              cost.partsPrice = ''
              cost.closedPrice = ''
              this.setState({ cost })
            }
          },
          {
            key: 'parts',
            label: '备件名称',
            props: { maxLength: 60, defaultValue: '' },
            rule: { required: true },
            hide: item =>
              partsConfig.enableIntegrationWithWorkOrder
                ? this.state.cost.partsType == '1' || this.state.cost.partsType == '2'
                : false
          },
          {
            key: 'partsItemId',
            label: '备件名称',
            component: 'Select',
            tree: true,
            options:
              storagePartsItemList &&
              storagePartsItemList.map(r => ({
                value: r.id,
                title: r.name
              })),
            props: { maxLength: 60, disabled: false },
            rule: { required: true },
            hide: item => !partsConfig.enableIntegrationWithWorkOrder || this.state.cost.partsType != '1',
            onChange: async e => {
              let partsItemInfo = await rest.get(`${urls.getPartsItemInfo}?partsItemId=${e}&assetUid=${this.props.assetUid}&currentPersonId=${this.props.currentPersonId}`)
              let cost = this.state.cost
              cost.partsPrice = partsItemInfo.estimatedUnitPrice
              cost.inStockNum = partsItemInfo.inStockNum
              this.setState(this.state)
            }
          },
          {
            key: 'partsItemId',
            label: '备件名称',
            component: 'Select',
            tree: true,
            options:
              purchasePartsItemList &&
              purchasePartsItemList.map(r => ({
                value: r.id,
                title: r.name
              })),
            props: { maxLength: 60, disabled: false },
            rule: { required: true },
            hide: item => !partsConfig.enableIntegrationWithWorkOrder || this.state.cost.partsType != '2'
          },
          {
            key: 'apartsQuantity',
            label: '备件数量',
            component: 'Number',
            rule: { required: true, min: 0, max: 999999, custom: this.validateQuantity.bind(this) },
            props: { disabled }
          },
          {
            key: 'partsPrice',
            label: '预估单价(元)',
            component: 'Number',
            rule: { required: true, min: 0, max: 99999999 },
            props: { disabled: disabled || disabledApproved }
          },
          {
            key: 'inStockNum',
            label: '所剩库存',
            component: 'Number',
            props: { disabled: true },
            hide: item => item.partsType != '1'
          },
          { key: 'invoiceNum', label: '发票号', props: { disabled } },
          { key: 'invoiceTime', label: '开票时间', component: 'Date', props: { disabled } },
          // { key: 'supplier', label: '供应商', options: supplierPlus.filter(s => s.supplier_t).map(s => s.name) },
          // { key: 'maintainer', label: '维修商', options: supplierPlus.filter(s => s.maintainer_t).map(s => s.name) },
          { key: 'supplier', label: '发票提供方', options: supplierPlus.map(s => s.name), props: { disabled } },
          { key: 'contractNum', label: '合同号', props: { disabled } },
          {
            key: 'closedPrice',
            label: '合同价格(元)',
            component: 'Number',
            rule: { min: 0, max: 99999999 },
            props: {
              disabled,
              onClick: e => {
                if (that.state.cost.closedPrice == that.state.cost.partsPrice) {
                  that.state.cost.closedPrice = ''
                  e.target.value = ''
                }
              }
            }
          },
          {
            key: 'priority',
            label: '紧急程度',
            component: 'Select',
            type: 'mvsPriority',
            hide: () => !that.state.multiTenant,
            props: { disabled }
          },
          { key: 'serialNum', label: '序列号', props: { disabled } },
          { key: 'warrantyDate', label: '质保期', component: 'Date', props: { disabled } },
          // { key: 'estimatedArriveTime', label: '预计到达时间', component: 'DateTime', rule: {custom: this.validateTime.bind(this)}},
          {
            key: 'applyTime',
            label: '申请备件时间',
            component: 'DateTime',
            props: { disabled: disabled || disabledApproved }
          },
          { key: 'estimatedArriveTime', label: '预计到达时间', component: 'DateTime', props: { disabled } },
          {
            key: 'purchaseTime',
            label: '采购时间',
            component: 'DateTime',
            props: { disabled: true },
            hide: () => !that.state.multiTenant
          },
          {
            key: 'deliverTime',
            label: '发货时间',
            component: 'DateTime',
            props: { disabled: true },
            hide: () => !that.state.multiTenant
          },
          { key: 'arrived', label: '已到货', component: 'Switch', props: { disabled } },
          {
            key: 'arrivedGood',
            label: '是否完好',
            component: 'SwitchLabel',
            hide: () => !that.state.multiTenant,
            props: { disabled }
          },
          {
            key: 'arrivedGoodReturn',
            label: '是否好还',
            component: 'SwitchLabel',
            hide: () => !that.state.multiTenant,
            props: { disabled }
          },
          { key: 'closed', label: '已结单', component: 'Switch', props: { disabled } },
          // { key: 'arrivedTime', label: '到货确认时间', component: 'DateTime'}
          {
            key: 'attachments',
            component: 'ImageUploader',
            props: {
              title: '附件',
              previewMode: disabled,
              maxCount: 9,
              allowFile: true,
              accept: ".jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf, .txt, .ps",
              autoSave: true
            }
          }
        ]
      },
      {
        title: '备注',
        items: [
          {
            key: 'comment',
            label: '备注',
            component: 'TextArea',
            props: { placeholder: '请输入……', maxLength: 500, rows: '2', disabled }
          },
          {
            key: 'detailReviewSignature',
            component: 'Element',
            render: ({ item, value, props }) => {
              if (
                value.detailReviewSignature &&
                !value.medicalWorkerApproverSignature &&
                !value.approverSignatureList
              ) {
                return (
                  <div>
                    备件审核人签名
                    <img width="95%" src={`${urls.obj}${value.detailReviewSignature}`} />
                  </div>
                )
              }
            }
          },
          {
            key: 'medicalWorkerApproverSignature',
            component: 'Element',
            render: ({ item, value, props }) => {
              if (value.medicalWorkerApproverSignature) {
                return (
                  <div>
                    医工签名
                    <img width="95%" src={`${urls.obj}${value.medicalWorkerApproverSignature}`} />
                  </div>
                )
              }
            }
          },
          {
            key: 'approverSignatureList',
            component: 'Element',
            render: ({ item, value, props }) => {
              if (value.approverSignatureList && value.approverSignatureList.length > 0) {
                return (
                  <div>
                    核心组签名
                    {value.approverSignatureList.map(signature => {
                      return <img width="95%" src={`${urls.objDownload}/${signature}`} />
                    })}
                    {/*<img width="95%" src={`${urls.objDownload}/${value.detailReviewSignature}`} />*/}
                  </div>
                )
              }
            }
          },
          {
            key: 'partsSignature',
            component: 'Element',
            render: ({ item, value, props }) => {
              if (value.partsSignature) {
                return (
                  <div>
                    备件提交人签名
                    <img width="95%" src={`${urls.objDownload}/${value.partsSignature}`} />
                  </div>
                )
              }
            }
          }
        ]
      }
    ]
    this.state.partsConfig = partsConfig
    this.setState(this.state)
  }

  validateQuantity(form) {
    if (form.apartsQuantity.toString().includes('.')) {
      return {
        msg: '备件数量不能为小数'
      }
    }
  }

  eventBind(e) {
    if (e.target.name) {
      let state = this.state
      state.coworker[e.target.name] = e.target.value
      this.setState(state)
    }
  }

  validateTime(form) {
    let estime = form.estimatedArriveTime
    if (new Date(estime.replace('T', ' ').replace(/-/g, '/')) < this.state.initTime) {
      return {
        msg: '预计到达时间不能小于当前时间'
      }
    }
  }

  setClosedPrice = (value, event, onChange) => {
    if (value.apartsQuantity && value.partsPrice) {
      value.closedPrice = value.apartsQuantity * value.partsPrice
      value.closedPriceFlag = value.partsPrice
      onChange(value)
    }
  }

  eventBindForm = (e, event, callback) => {
    if (event && ['apartsQuantity', 'partsPrice'].includes(event.target.name)) {
      this.setClosedPrice(e, event, callback)
    }

    let state = this.state
    if (this.state.assetId) {
      // this.setState({ coworker: { ...this.state.coworker, ...e} })
    } else {
      // e.estimatedArriveTime = e.estimatedArriveTime.replace('T', ' ')
      if (e.estimatedArriveTime && e.estimatedArriveTime.length != 19)
        e.estimatedArriveTime = e.estimatedArriveTime + ':00'
      if (e.applyTime && e.applyTime.length != 19) e.applyTime = e.applyTime + ':00'
      // if (e.invoiceTime && e.invoiceTime.length != 19)
      //   e.invoiceTime = e.invoiceTime + ':00'
      if (e.arrived) {
        if (!e.arrivedTime) e.arrivedTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      } else {
        e.arrivedTime = ''
      }
      if (e.closed) {
        if (!e.closedTime) e.closedTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      } else {
        e.closedTime = ''
      }
      state.cost = e
    }
    this.cost = state.cost
    // this.setState(state)
  }

  cancel() {
    this.props.onResult(null)
  }

  // submitWithSignature() {
  //   sign('请签名后再保存', (objectId) => {
  //     let val = { partsSignature: objectId }
  //     this.submit(val)
  //   })
  // }

  submit = val => {
    console.log('in save', val)
    val = val || {}
    if (this.state.assetId) {
      console.log('in save coworker', this.state.coworker)
      const { cowokerUserId } = this.state.coworker
      if (!cowokerUserId) {
        GeneralDialog.alert('协作者不能为空', '表单校验不通过')
      } else {
        const coworker = {...this.state.coworker, ...val}
        if (coworker.startTime && coworker.endTime) {
          coworker.startTime = coworker.startTime.replace('T', ' ')
          coworker.endTime = coworker.endTime.replace('T', ' ')
          // 补齐秒:00
          if (coworker.startTime.length === 16) coworker.startTime += ':00'
          if (coworker.endTime.length === 16) coworker.endTime += ':00'
        }
        this.props.onResult(coworker)
      }
    } else {
      this.state.cost.estimatedArriveTime = this.state.cost.estimatedArriveTime
        ? this.state.cost.estimatedArriveTime.replace('T', ' ')
        : ''
      this.state.cost.applyTime = this.state.cost.applyTime ? this.state.cost.applyTime.replace('T', ' ') : ''
      // this.state.cost.invoiceTime = this.state.cost.invoiceTime?this.state.cost.invoiceTime.replace('T', ' '):''
      this.state.cost.arrivedTime = this.state.cost.arrivedTime ? this.state.cost.arrivedTime.replace('T', ' ') : ''
      this.state.cost.closedTime = this.state.cost.closedTime ? this.state.cost.closedTime.replace('T', ' ') : ''
      this.state.cost.purchaseTime = this.state.cost.purchaseTime ? this.state.cost.purchaseTime.replace('T', ' ') : ''
      this.state.cost.deliverTime = this.state.cost.deliverTime ? this.state.cost.deliverTime.replace('T', ' ') : ''

      let data = { ...this.cost, ...this.state.cost, ...val }

      if (this.state.cost.partsType == '1' || this.state.cost.partsType == '2') {
        if (this.state.partsConfig && this.state.partsConfig.enableIntegrationWithWorkOrder) {
          data.parts = this.state.partsIdKeyObj[this.state.cost.partsItemId]
        } else {
          data.parts = this.state.cost.parts
        }
      }
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          data[key] && data[key].length > 0 && ~data[key].indexOf('T')
            ? (data[key] = data[key].replace('T', ' '))
            : data[key]
        }
      }
      rest
        .post(urls.updatePrice + '/' + this.props.assetUid, data, false, {
          extraHeaders: { taskSource: this.state.woType == 'workOrder' ? 'workOrderAddCostTask' : 'pmOrderAddCostTask' }
        })
        .then(res => {
          if (res && res.id) {
            this.props.dispatch({ type: 'device/get/extraSuppliers' })
            this.props.onResult(res)
          } else {
            message.warn('保存失败', 2)
          }
        })
    }
  }

  renderForm(style) {
    let estimatedArriveTime = this.state.cost.estimatedArriveTime
      ? this.state.cost.estimatedArriveTime.replace(' ', 'T')
      : ''
    let applyTime = this.state.cost.applyTime ? this.state.cost.applyTime.replace(' ', 'T') : ''
    // let invoiceTime = this.state.cost.invoiceTime?this.state.cost.invoiceTime.replace(' ', 'T'):''
    let arrivedTime = this.state.cost.arrivedTime ? this.state.cost.arrivedTime.replace(' ', 'T') : ''
    let closedTime = this.state.cost.closedTime ? this.state.cost.closedTime.replace(' ', 'T') : ''
    let purchaseTime = this.state.cost.purchaseTime ? this.state.cost.purchaseTime.replace(' ', 'T') : ''
    let deliverTime = this.state.cost.deliverTime ? this.state.cost.deliverTime.replace(' ', 'T') : ''
    return (
      // <Dialog title="费用明细" show style={style}>
      this.state.showCostForm && (
        <GeneralForm
          groups={this.state.groups}
          value={{
            ...this.state.cost,
            estimatedArriveTime,
            applyTime,
            arrivedTime,
            closedTime,
            purchaseTime,
            deliverTime
          }}
          buttons={this.state.buttons}
          onChange={this.eventBindForm}
        />
      )
      // {/* </Dialog> */}
    )
  }

  renderCoworker() {
    return (
      <GeneralForm
        groups={this.state.coworkGroups}
        value={this.state.coworker}
        buttons={this.state.buttons}
        onChange={this.eventBindForm}
      />
    )
  }

  render() {
    const style = {
      width: '98%',
      maxWidth: 'none',
      height: '600px',
      overflow: 'auto'
    }
    return this.state.assetId ? this.renderCoworker() : this.renderForm(style)
  }
}

export default WoCost
