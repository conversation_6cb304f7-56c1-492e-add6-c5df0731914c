import React from 'react'
import { browserHistory as history } from 'react-router'
import { APMList as PanelList, Dialog, WoCard } from '../../../components'
import { route_urls, urls, rest } from '../../../constants'
import { Button<PERSON><PERSON>, <PERSON><PERSON> } from 'react-weui'
import { badgeStyle } from '../ConsoleSRList'
export default class ScanReportList extends React.Component {

  constructor(props) {
    super(props)
    let status = 1
    if (props.location && props.location.state && props.location.state.status != undefined) {
      status = props.location.state.status
    }
    const assetId = (props.location.state && props.location.state.asset) ? props.location.state.asset.id : props.location.query.id
    this.state = {
      isValid: true,
      assetStatus: undefined,
      request: {
        title: <span style={{ color: 'red' }}>正在进行中的报修列表</span>,
        url: urls.request,
        query: {
          status,
          page: 0,
          assetId,
          // submittedBy: 0
        },
        itemHeader: { key: 'srNum', label: '报修编号' },
        itemContent: [{
          key: 'assetName', label: '资产名称'
        }, {
          key: 'requestorName', label: '报修人'
        }],
        badge: { key: 'casePriority', label: '紧急程度', type: 'casePriority' },
        badgeStyle
      }
    }
    this.renderCreateWo = this.renderCreateWo.bind(this)
    this.eventClick = this.eventClick.bind(this)
  }

  componentWillMount() {
    const { id } = this.props.location.state.asset || this.props.location.query
    rest.get(urls.assets + '/' + id).then(asset => this.setState({ isValid: asset.isValid, assetStatus: asset.status }))
  }

  renderCreateWo() {
    const { state, query } = this.props.location
    const assetId = (state && state.asset) ? state.asset.id : query.id
    history.push(route_urls.wx.createWo + '?id=' + assetId)
  }

  eventClick(wo) {
    if (this.props.location.state.userId == 0)
      return
    history.push({ pathname: route_urls.wx.workorder + '/' + wo.id, query: { from: 'ScanReportList' } })
  }

  render() {
    return (
      <div>
        <ButtonArea>
          <Dialog title="有正在进行中的报修" desc="确认新增一个报修?">
            <Button type="primary" onClick={this.renderCreateWo} disabled={!this.state.isValid || this.state.assetStatus == 4}>新增报修</Button>
          </Dialog>
        </ButtonArea>
        <PanelList component={WoCard} {...this.state.request} onClick={this.eventClick} />
      </div>
    )
  }
}
