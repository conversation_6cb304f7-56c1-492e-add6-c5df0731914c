import React from 'react'
import { Preview, Form as GeneralForm, Dialog, ImageUploader } from '../../../components'
import { query, urls, rest, util } from '../../../constants'
import * as cancelUtil from './cancelUtil'
import { connect } from 'react-redux'
import { Button, Cell, Cells } from 'react-weui'
import { DEFAULT_FILE_TYPE } from '../../../components/ImageUploader'
import { downloadBlobFile } from '../../../constants/util'

class ViewWo extends React.Component {

  constructor(props) {
    super(props)

    this.state = {
      preview: undefined,
      feedbackPreview: {
        title: '反馈',
        data: {
          comment: props && props.feedbackComment,
          rating: props && props.feedbackRating
        },
        brief: [
          { key: 'rating', label: '评分' },
          { key: 'comment', label: '备注' }
        ]
      },
      cancelForm: {
        desc: '',
        assetStatus: ''
      },
      cancelValue: {
        currentStepId: this.props.step,
        woId: this.props.woId
      },
      cancelFormTemplate: undefined,
      showCancel: props.roleList.filter(role => role.name == 'AssetHead').length > 0,
      updateFormTemplate: undefined,
      updateForm: {
        repairResult: this.props.repairResult,
        currentStepId: props.step
      },
      attachmentsAfterClosed: [],
      roleList: new Set(props.myself.userRoleList.map(r => r.name))
    }
  }

  componentWillMount() {
    let cancelFormTemplate = cancelUtil.generateCancelFormTemplate(this.state.cancelValue)
    let updateFormTemplate = this.state.cancelValue.currentStepId == 7 || this.state.cancelValue.currentStepId == 6 ?
      this.generateUpdateFormTemplate() : undefined
    this.generatePreview().then((preview) => {
      this.setState({
        preview, cancelFormTemplate, updateFormTemplate
      })
    })
  }

  componentDidMount() {
    if (this.refs && this.refs.rater) {
      let starts = this.refs.rater.children
      let sArray = Array.from(starts)
      const num = this.props && this.props.feedbackRating
      sArray.map((val, idx) => {
        if (idx < num) {
          val.className = 'weui-rater-box checked'
        }
      })
    }
  }

  generateUpdateFormTemplate() {
    return {
      buttons: [
        { type: 'primary', label: '修改维修结果', validation: true, confirm: '确定修改维修结果？', onClick: val => this.updateRepairResult(val) }
      ],
      groups: [{
        items: [{
          key: 'repairResult',
          type: 'repairResult',
          label: '维修结果',
          component: 'Select',
          rule: { required: true }
        }]
      }]
    }
  }
  updateRepairResult(val) {
    rest.post(urls.workorder + '/' + this.props.woId + '/action?type=updateResult', { ...this.state.updateForm, ...val }, false, { extraHeaders: { "taskSource": "modifyRepairResultTask" } }).then(res => {
      if (res && res == "success") {
        Dialog.toast('修改维修结果成功', 2000, 'success')
      }
    }).catch((error) => {
      Dialog.toast('修改维修结果错误', 2000, 'error')
    })
  }

  async generatePreview() {
    const {
      assetGroup, caseType, step, estimatedCloseTime, ackId,
      ackName, currentPersonName, currentPersonId,
      patProblems, patActions, patTests, repairResult, woCloseTime, comment
    } = this.props

    let name = ''
    if (util.isNotUndefined(assetGroup) && util.isNotUndefined(caseType)) {
      name = await query.getFaultName(assetGroup, caseType)
    }
    let ownerPhone = ''
    let res = await rest.get(urls.users + '/' + (step == 5 ? ackId : currentPersonId))
    if (res) {
      ownerPhone = res.telephone
    }

    let preview = {
      title: '故障明细',
      data: {
        step: step,
        faultName: name,
        responsor: (typeof step !== 'undefined') && (step == 5 ? ackName : currentPersonName),
        phonenumber: ownerPhone,
        tobefixedtime: estimatedCloseTime,
        patTests: patTests,
        comment: comment,
        patActions: patActions,
        patProblems: patProblems,
        repairResult: repairResult,
        woCloseTime: woCloseTime
      },
      brief: [
        { key: 'faultName', label: '故障类别' },
        { key: 'woCloseTime', label: '完成时间' },
        { key: 'responsor', label: '当前负责人' },
        { key: 'phonenumber', label: '负责人电话', tel: true }
      ]
    }
    if (step !== 7) {
      preview.brief.push({ key: 'tobefixedtime', label: '预估解决时间' })
    } else {
      preview.brief.push({ key: 'patProblems', label: '原因描述' })
      preview.brief.push({ key: 'patActions', label: '解决方案' })
      preview.brief.push({ key: 'patTests', label: '交付前检测' })
      preview.brief.push({ key: 'comment', label: '最新进展' })
    }
    if (!this.state.showCancel) {
      preview.brief.push({ key: 'repairResult', label: '维修结果', type: 'repairResult' })
    }

    return preview
  }

  uploadAttachmentAfterCloseList() {
    let attachments = this.state.attachmentsAfterClosed
    rest.post(urls.workorder + '/' + this.props.woId + '/action?type=closed', { ...this.state.updateForm, attachments }, false, { extraHeaders: { "taskSource": "attachUploadTask" } }).then(res => {
      if (res && res == "success") {
        util.reload()
      }
    }).catch((error) => {
      Dialog.toast('关单后附件上传失败', 2000, 'error')
    })
  }
  deleteFile(file) {
    let newFiles = this.state.attachmentsAfterClosed
    newFiles = newFiles.filter(item => item.objectStorageId !== file.objectStorageId)
    this.setState({ attachmentsAfterClosed: newFiles })
  }

  render() {
    return (
      <div>
        <Preview {...this.state.preview} />
        {this.props && this.props.step && this.props.step > 6 && <Preview {...this.state.feedbackPreview} />}
        {this.state.showCancel && this.state.cancelFormTemplate && <GeneralForm
          value={this.state.cancelForm}
          buttons={this.state.cancelFormTemplate.buttons}
          groups={this.state.cancelFormTemplate.groups}
        />}
        {this.state.showCancel && this.state.updateFormTemplate && <GeneralForm
          value={this.state.updateForm}
          buttons={this.state.updateFormTemplate.buttons}
          groups={this.state.updateFormTemplate.groups}
        />}
        {this.state.roleList.has('AssetStaff') && <Cells>
          <ImageUploader
            title='附件上传'
            maxCount={20}
            autoSave
            files={this.state.attachmentsAfterClosed}
            allowFile={true}
            accept={DEFAULT_FILE_TYPE}
            previewMode={false}
            onChange={attachmentsAfterClosed => this.setState({ attachmentsAfterClosed })}
            deleteFile={this.deleteFile.bind(this)}
          />
          <Cell>
            <Button
              disabled={this.state.attachmentsAfterClosed.length === 0}
              onClick={() => this.uploadAttachmentAfterCloseList()}>
              附件保存
            </Button>
          </Cell>
          {this.props.step == 7 && (
            <Cell>
              <Button
                onClick={() => {
                  const url = `${urls.reportDownload}/repair?id=${this.props.woId}`;
                  downloadBlobFile(url);
                }}
                type="primary"
              >
                下载维修报告
              </Button>
            </Cell>
          )}
        </Cells>}

      </div>
    )
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
  }
}

export default connect(mapStateToProps)(ViewWo)
