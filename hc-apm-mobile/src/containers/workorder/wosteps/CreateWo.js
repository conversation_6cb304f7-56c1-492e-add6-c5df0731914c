import React from 'react'
import { Input, Form, FormCell, CellHeader, Label, CellBody, CellsTitle } from 'react-weui'
import { Preview, Form as GeneralForm, Dialog } from '../../../components'
import { urls, rest, route_urls, util, transformPreview } from '../../../constants'
import { systemActions } from '../../../actions'
import { browserHistory as history } from 'react-router'
import { connect } from 'react-redux'
// import { stat } from 'fs'
import { Modal } from 'antd'

const moment = require('moment')

class CreateWo extends React.Component {

  constructor(props) {
    super(props)
    const assetId = props.location.query.id
    this.defaultWorkOrder = {
      srRepairType: props.location.query.srRepairType || 2,
      assetId,
      priority: 3,
      assetStatus: 3,
      confirmedDownTime: util.transformDateTimeAPIFaced(this.defaultTime()),
      requestReason: '设备故障',
      attachments: null,
      userName: '',
      telephone: '',
      wechatId: this.props.myself.userAccount.id == 0 ? this.props.wxUser && this.props.wxUser.openId : '',
      corpUserId: this.props.myself.userAccount.id == 0 ? this.props.wxUser && this.props.wxUser.corpUserId : '',
      nickName: this.props.myself.userAccount.id == 0 ? this.props.wxUser && this.props.wxUser.nickname : ''
    }

    this.state = {
      submitting: false,
      brief: [],
      form: {
        ...this.defaultWorkOrder,
        confirmedDownTime: util.transformDateTimeComponentFaced(this.defaultWorkOrder.confirmedDownTime),
        voiceAttachment: null
      },
      formTemplate: undefined,
      assetInfo: props.location.state || {},
      message: '',
      assetLocation: props.userOrgInfo && props.userOrgInfo.name,
      attrItems: undefined,
      pageWorkOrder: undefined,
      pageWorkOrderValue: undefined,
      requestReasonComp: '设备故障',
      keys: [],
      voiceOrReasonRequired: true,
      telReportStart: (this.props.workflowConfig && this.props.workflowConfig.telReportStart) || '24:00:00',
      telReportEnd: (this.props.workflowConfig && this.props.workflowConfig.telReportEnd) || '00:00:00',
    }

    this.renderAssetLocation = this.renderAssetLocation.bind(this)
  }

  componentDidMount() {
    const { location, myself } = this.props
    if (location && !(location.state && location.state.id) && (location.query && location.query.id)) {
      rest.get(urls.assets + '/' + location.query.id).then(async asset => {
        let res = await rest.get(urls.customConfig + '?range=pageWorkOrder&siteUid=' + asset.siteUID)
        this.getCustomCofing(res)
        this.setState(Object.assign({}, this.state, {
          form: Object.assign({}, this.state.form, {
            assetId: asset.id
          }),
          assetInfo: asset
        }), async () => {
          const brief = this.getBrief()
          let formTemplate = this.generateFormTemplate()
          const attributes = await rest.get(urls.deptAudit + `/Attributes/attrDescs?assetId=${asset.id}`)
          const attrItems = this.getAttributes(attributes)
          if (attrItems.items.length > 0) {
            formTemplate.groups.push(attrItems)
            const attrKeyVal = this.setFormData(attrItems)
            this.state.form = { ...this.state.form, ...attrKeyVal }
            this.state.attrItems = attrItems.items.map(item => {
              return {
                key: item.key, label: item.label, value: 0
              }
            })
          }
          this.state.form.requestorName = myself.userAccount.name
          this.state.form.requestorPhone = myself.userAccount.telephone

          let userInfoTemplate = this.getUserInfoTemplate()
          if (userInfoTemplate) {
            formTemplate.groups.push(userInfoTemplate)
          }
          this.setState(Object.assign({}, this.state, {
            formTemplate, brief
          }))
          this.setMyOfficeToAssetLocation()
        })
      }).catch((error) => {
        history.replace({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
      })
    }
  }

  componentWillUnmount() {
    if (this.state.toastTimer) clearTimeout(this.state.toastTimer)
  }

  getCustomCofing(res) {
    if (!res || !res.pageWorkOrder) return
    const pageWorkOrderTemp = res.pageWorkOrder.group
    const assetShowFields = res.pageWorkOrder.assetShowFields
    const assetInfoFields = pageWorkOrderTemp[0].assetInfoFields
    const keys = []
    assetInfoFields.map(field => {
      let f = assetShowFields.find(f => f.nameKey == field)
      if (f) keys.push(f)
    })
    let pageWorkOrder = {}
    if (pageWorkOrderTemp[1].show)
      pageWorkOrder = { ...pageWorkOrderTemp[1] }
    if (pageWorkOrderTemp[2].show)
      pageWorkOrder = { ...pageWorkOrder, ...pageWorkOrderTemp[2] }
    const pageWorkOrderValue = { ...pageWorkOrderTemp[1], ...pageWorkOrderTemp[2] }

    this.state.form.requestReason = pageWorkOrderValue.requestReason.defaultValue
    this.state.form.priority = pageWorkOrderValue.priority.defaultValue
    this.state.form.assetStatus = pageWorkOrderValue.assetStatus.defaultValue
    if (pageWorkOrderValue.repairReportSendPlace) {
      this.state.form.repairReportSendPlace = pageWorkOrderValue.repairReportSendPlace.defaultValue
    }
    this.state.pageWorkOrder = pageWorkOrder
    this.state.pageWorkOrderValue = pageWorkOrderValue
    this.state.requestReasonComp = pageWorkOrderValue.requestReason.defaultValue
    this.state.keys = keys
    this.state.voiceOrReasonRequired = pageWorkOrderTemp.filter(item => item.type == 'descriptions')[0].voiceOrReasonRequired
    this.setState(this.state)
  }

  setMyOfficeToAssetLocation() {
    const { clinicalDeptName, assetDeptName } = this.state.assetInfo
    const { myself } = this.props
    if (assetDeptName) {
      this.setState({ assetLocation: assetDeptName })
    } else if (myself.clinicalDeptName) {
      this.setState({ assetLocation: myself.clinicalDeptName })
    } else {
      this.setState({ assetLocation: clinicalDeptName })
    }
  }

  getBrief() {
    return this.state.keys.map(item => transformPreview(item))
  }

  generateFormTemplate() {
    let pageWorkOrder = this.state.pageWorkOrder
    const groupsTemplate = [{
      keyIdx: 'priority',
      title: "选择",
      items: [
        {
          key: 'priority',
          type: 'casePriority',
          label: '紧急程度',
          component: 'Select',
          first: false,
          rule: { required: true }
        }]
    }, {
      keyIdx: 'assetStatus',
      title: '选择',
      items: [{
        key: 'assetStatus',
        label: '资产状态',
        options: [{
          key: 2,
          value: '停机'
        }, {
          key: 3,
          value: '限制功能使用'
        }],
        component: 'Select',
        rule: { required: true }
      }, {
        key: 'confirmedDownTime',
        label: '停机时间',
        component: 'DateTime',
        hide: (form) => {
          return form.assetStatus != 2
        },
        placeholder: '请选择停机时间...',
        rule: { required: true }
      }, {
        key: 'repairReportSendPlace',
        type: 'repairReportSendPlace',
        component: 'Select',
        hide: () => !pageWorkOrder.repairReportSendPlace || (pageWorkOrder.repairReportSendPlace && !pageWorkOrder.repairReportSendPlace.enable),
        rule: { required: !!pageWorkOrder.repairReportSendPlace && pageWorkOrder.repairReportSendPlace.required }
      }]
    }, {
      keyIdx: 'attachments',
      title: '故障',
      items: [{
        key: 'attachments',
        component: 'ImageUploader',
        label: '故障、视频图片',
        props: {
          title: "故障图片、视频上传",
          allowVideo: true,
          maxCount: 5,
          files: this.state.form.attachments,
          autoSave: true
        },
        rule: { required: this.state.pageWorkOrderValue.attachments.required }
      }]
    }, {
      keyIdx: 'voiceAttachment',
      title: "故障语音描述",
      items: [{
        key: 'voiceAttachment',
        label: '故障语音描述',
        component: 'Audio',
        rule: this.state.voiceOrReasonRequired ? { custom: this.validateAudioText.bind(this) } : { required: this.state.pageWorkOrderValue.voiceAttachment.required }
      }]
    }, {
      keyIdx: 'requestReason',
      title: "故障文字描述",
      items: [{
        key: 'requestReason',
        label: '故障文字描述',
        component: 'TextArea',
        rule: this.state.voiceOrReasonRequired ? { custom: this.validateAudioText.bind(this) } : { required: this.state.pageWorkOrderValue.requestReason.required },
        props: {
          placeholder: this.state.voiceOrReasonRequired ? '语音和文字至少一项有内容' : '请输入', maxLength: 200, rows: "3", onClick: (form, e, cb) => {
            if (this.state.requestReasonComp == form.requestReason) {
              form.requestReason = ''
              e.target.value = ''
              cb(form)
            }
          }
        }
      }]
    }]

    let groups = []
    for (let key in pageWorkOrder) {
      let item = groupsTemplate.find(group => group.keyIdx == key && pageWorkOrder[key].enable)
      if (item) {
        let pri = groups.find(group => group.keyIdx == 'priority')
        if (item.keyIdx == 'assetStatus' && pri)
          pri.items = pri.items.concat(item.items)
        else
          groups.push(item)
      }
    }

    if (util.isZKB()) {
      groups.push({
        keyIdx: 'clientInfo',
        title: '医院客户信息',
        items: [{
          key: 'customerName',
          component: 'Text',
          label: '医院客户姓名',
          rule: { required: true }
        }, {
          key: 'customerTel',
          component: 'Text',
          label: '医院客户电话',
          rule: { required: true, phoneNumber: true }
        }, {
          key: 'customerPosition',
          component: 'Text',
          label: '医院客户职务'
        }]
      })
    }

    if (this.props.myself.userAccount.id !== 0) {
      groups.push({
        keyIdx: 'requestorName',
        title: '报修人信息',
        items: [
          { key: 'requestorName', label: '报修人', props: { disabled: true } },
          { key: 'requestorPhone', label: '报修人电话' }
        ]
      })
    }

    let formTemplate = {
      buttons: [
        {
          type: 'primary',
          label: this.state.assetInfo.clinicalDeptId ? '提交' : '设备无所属科室，无法报修',
          validation: true,
          onClick: val => {
            if (this.state.submitting) {
              return
            }
            Dialog.confirm(this.renderAssetLocation(), '确定提交报修').onDismiss(confirmed => {
              if (confirmed) {
                this.eventSubmit(val)
              }
            })
          },
          props: { disabled: this.state.assetInfo.clinicalDeptId ? false : true }
        }
      ],
      groups
    }

    return formTemplate
  }

  renderAssetLocation() {
    return (
      <Form>
        <CellsTitle>请同时确认设备当前位置</CellsTitle>
        <FormCell>
          <CellHeader>
            <Label style={{ color: '#000' }}>当前位置</Label>
          </CellHeader>
          <CellBody>
            <Input type="text" defaultValue={this.state.assetLocation} onChange={e => this.setState({ assetLocation: e.target.value })} />
          </CellBody>
        </FormCell>
      </Form>
    )
  }

  getAttributes(attributes) {
    const items = attributes.map(attr => {
      return {
        key: attr.id,
        label: attr.name,
        component: 'Number',
        rule: { required: attr.required }
      }
    })
    return {
      title: '其他属性',
      items
    }
  }
  setFormData(attrItems) {
    let attrs = {}
    attrItems.items.map(attr => attrs[attr.key] = '')
    return attrs
  }

  getUserInfoTemplate() {
    if (this.props.myself.userAccount.id === 0) {
      return {
        title: "用户信息",
        items: [{
          key: 'userName',
          label: '用户姓名',
          rule: { required: true }
        }, {
          key: 'telephone',
          label: '联系电话',
          rule: { required: true }
        }]
      }
    }
    return null
  }

  defaultTime() {
    return moment().format('YYYY-MM-DD HH:mm:ss')
  }

  eventSubmit = async (val) => {
    let attributes = this.getAttributeIfNeed(val)
    val = { ...this.state.form, ...val, assetLocation: this.state.assetLocation, attributes }

    if (this.props.myself.userAccount.id == 0) {
      val.requestorName = val.userName
      val.requestorPhone = val.telephone
    }

    if (val.voiceAttachment) {
      val.attachments = val.attachments ? val.attachments : []
      val.attachments.push(val.voiceAttachment)
    }

    if (val.confirmedDownTime && val.assetStatus == 2) {
      val.confirmedDownTime = util.transformDateTimeAPIFaced(val.confirmedDownTime)
    } else {
      val.confirmedDownTime = ''
    }
    val.reportSource = '2'
    const { repairSource } = this.props.location.query
    val.repairSource = repairSource || 'Accounts'
    await this.setState({ submitting: true })
    rest.post(urls.request, val).then(res => {
      if (res && res.id) {  // 服务正常返回userAccount对象
        if (this.getUserInfoTemplate() && res.tempToken) { // 如果发现新创建的用户token则设置token并重新获取用户信息
          rest.setToken(res.tempToken)
          systemActions.myself()
        }
        return Promise.resolve(res)
      }
      return Promise.resolve(res) // 如果返回非userAccount对象
    }).then(res => {
      this.setState({ submitting: false })
      if (res === 'zkb400') { // zkb设备发现异常返回特征码进行外修确认
        Dialog.confirm('如需报修服务请联系GE HealthCare客户服务中心************，或点击400报修按钮自行报修', '温馨提示，该设备已出保', { cancel: '取消', ok: '400报修' }).onDismiss(confirmed => {
          if (confirmed) {
            util.goOneClickRepair(this.state.assetInfo)
          }
        })
      } else if (res) {
        const now = moment().format('HH:mm:ss')
        const telReportStart = this.state.telReportStart
        const telReportEnd = this.state.telReportEnd

        let next = route_urls.home_wx
        if (window.gfc && typeof window.gfc.setToken === 'function') { // 如果发现在app里，则将新的用户token传给app，app后返回微工作台
          window.gfc.setToken(res.tempToken)
          next = null
        }

        if (telReportStart && telReportEnd
          && ((telReportStart <= telReportEnd && now >= telReportStart && now <= telReportEnd)
            || (telReportStart >= telReportEnd && (now >= telReportStart || now <= telReportEnd)))) {
          this.showTelReportDialog(function() {
            history.replace({ pathname: route_urls.msg, query: { code: 201 }, state: { desc: '报修', next } })
          })
        } else {
          history.replace({ pathname: route_urls.msg, query: { code: 201 }, state: { desc: '报修', next } })
        }
      }

    }).catch((error) => {
      this.setState({ submitting: false })
      history.replace({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
    })
  }

  showTelReportDialog = (onOk) => {
    Modal.warning({
      maskClosable: false,
      title: "电话报修时段",
      okText: '关闭',
      onOk,
      content: <div>如遇紧急维修，请拨打值班电话<br /></div>
    })
  }

  getAttributeIfNeed(val) {
    let attributes = ''
    if (this.state.attrItems) {
      let form = { ...this.state.form, ...val }
      let keyVal = this.state.attrItems.map(item => {
        if (form[item.key])
          item.value = form[item.key]
        return item
      })
      attributes = JSON.stringify(keyVal)
    }
    return attributes
  }

  validateAudioText(form) {
    if (!form.voiceAttachment && !form.requestReason) {
      return {
        msg: '语音和文字至少一项有内容'
      }
    }
  }

  render() {
    const { myself } = this.props
    let showBadge = true
    if (tempOrGuest(myself)) {
      showBadge = false
    }
    return (
      <div className="input">
        <Preview title="资产基本信息"
          data={this.state.assetInfo}
          brief={this.state.brief}
          badge={showBadge && <div onClick={() => history.push(route_urls.wx.device + '/' + this.props.location.query.id)}>查看档案</div>}
        />
        {this.state.formTemplate && <GeneralForm
          value={this.state.form}
          buttons={this.state.formTemplate.buttons}
          groups={this.state.formTemplate.groups}
        />}
      </div>
    )
  }

}

function tempOrGuest(myself) {
  if (myself.userAccount.id == 0 || myself.userRoleList.find(role => role.name == 'Guest')) {
    return true
  } else {
    return false
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    wxUser: state.preload.wxUserInfo,
    userOrgInfo: state.common.get('userOrgInfo'),
    workflowConfig: state.workflowConfig.get('workflowConfig')
  }
}

export default connect(mapStateToProps)(CreateWo)
