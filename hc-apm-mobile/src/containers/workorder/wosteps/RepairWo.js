import React from 'react'
import { connect } from 'dva'
import {
  CellsTitle,
  CellHeader,
  CellBody,
  Button,
  Label,
  Cell,
  CellFooter,
  Icon,
  Cells,
  Toast,
  Dialog,
  Form,
  FormCell,
  Select,
  TextArea
} from 'react-weui'
import { urls, rest, route_urls, util } from '../../../constants'
import { Form as GeneralForm, genShortcut, Dialog as APMDialog, ImageUploader } from '../../../components'
import AssignApproverDialog from '../../pmorder/AssignApproverDialog'
import { browserHistory as history } from 'react-router'
import { WoCost } from './index'
import { message, Icon as AntdIcon, Modal } from 'antd'
import UrlPattern from 'url-pattern'
import moment from 'moment'
import { sumBy } from 'lodash'
import WorkHoursInput from '../../../components/WorkHoursInput'
import { isAppOffline } from '@/actions/jsApi'
import { scan } from '@components/QrCode'

const APPROVE_STATUS = {
  0: 'Unassign',
  1: 'Pending',
  2: 'Approved',
  3: 'Rejected',
  4: 'Partial'
}

@connect(state => ({
  config: state.common.get('mobileMaintainPageConfig').mobileMaintainPage
}))
export default class RepairWo extends React.Component {
  constructor(props) {
    super(props)

    this.state = {
      assetId: props.assetId,
      assetGroup: props.assetGroup,
      coworkerEntryType: props.coworkerEntryType || 0,
      form: {
        externalQuotation: props.externalQuotation,
        assetId: props.assetId,
        currentStepId: props.step,
        assetStatus:
          props.repairResult == 1
            ? 1
            : props.repairResult == 2
              ? 3
              : props.repairResult == 3
                ? 4
                : props.repairResult == 4
                  ? 3
                  : '',
        confirmedUpTime: this.transformDateTimeComponentFaced(this.getCurrentDate()),
        desc: '',
        assigneeId: '',
        //add 2017-6-12
        workOrderType: props.workOrderType,
        caseType: props.caseType,
        patProblems: props.patProblems,
        patActions: props.patActions,
        patTests: props.patTests,
        comment: props.comment,
        reAssignDesc: '工作需要',
        manHours: props.manHours || '',
        otherExpense: props.otherExpense || '',
        repairResult: props.repairResult,
        externalExpense: props.externalExpense || '',
        excludes: [props.currentLoginPersonId],
        attachments: [],
        mvsType: props.mvsType,
        subStatus: props.subStatus,
        statusStartTime: this.transformDateTimeComponentFaced(props.statusStartTime),
        statusCloseTime: this.transformDateTimeComponentFaced(props.statusCloseTime)
      },
      asset: {
        id: props.assetId
      },
      message: '',
      reAssignMsg: '',
      costs: props.v2_workOrder_detail_list || [],
      coworkers: props.v2_workOrder_coworker_list || [],
      show: false,
      showCo: false,
      showAs: false,
      showToast: false,
      showCreate: false,
      showApproval: false,
      toastTimer: null,
      checkinDesc: '',
      checkinTime: this.props.checkinTime,
      checkin: this.props.checkin,
      enableCheckInTime: false,
      enableSignNeedScanQrCode: false,
      index: -1,
      totalPrice: 0,
      downTime: this.props.confirmedDownTime,
      acceptStepStartTime: props.acceptStepStartTime,
      manHoursRequired: true,
      renderDiv: false,
      multiTenant: (props.multiTenant || props.mvsRoleType == 4) && props.mvsEquip == 1,
      reAssignDescCompare: '工作需要',
      showAck: false,
      ackFormTempalte: undefined,
      repairResultChange: props.repairResult,
      partTemplate: undefined,
      subStatus: props.subStatus,
      statusStartTime: props.statusStartTime,
      statusCloseTime: props.statusCloseTime,
      hasConfigMultiLevelApproval: false,
      isMultiVerifyBtnDisabled: props.approvalStatus === '5', //分为两个是因为后期还会添加新的状态
      isMultiSaveOrModifyBtnDisabled: props.approvalStatus === '5',
      workHourSection: {
        required: true,
        entryType: props.entryType === undefined ? 0 : props.entryType,
        manHours: props.manHours || '',
        workHoursList: props.workHoursList
      }
    }
    this.repairWo = this.repairWo.bind(this)
    this.checkInWo = this.checkInWo.bind(this)
    this.newCreate = this.newCreate.bind(this)
    this.showCreateSet = this.showCreateSet.bind(this)
    this.addCost = this.addCost.bind(this)
    this.renderCosts = this.renderCosts.bind(this)
    this.deleteCost = this.deleteCost.bind(this)
    this.cloneCost = this.cloneCost.bind(this)
    this.deleteCoworker = this.deleteCoworker.bind(this)
    this.renderCoworkers = this.renderCoworkers.bind(this)
    this.approval = this.approval.bind(this)
    this.saveRepairWo = this.saveRepairWo.bind(this)
    this.showReAssign = this.showReAssign.bind(this)
    this.renderApprovalReason = this.renderApprovalReason.bind(this)
    this.reAssignToTempUser = this.reAssignToTempUser.bind(this)
  }

  componentWillMount() {
    this.props.dispatch({ type: 'device/get/extraSuppliers' })
    this.getWorkflowConfig()
  }

  componentWillUnmount() {
    if (this.state.toastTimer) clearTimeout(this.state.toastTimer)
  }

  onWorkHoursOutput = output => {
    let workHourSection = {
      entryType: output.entryType,
      manHours: output.hours,
      workHoursList: output.workHoursList,
      errorMsgForWorkhours: output.errorMsg
    }
    this.setState({ workHourSection }, () => {
      let patTemplate = this.state.patTemplate
      if (this.state.workHourSection.entryType === 1 && patTemplate.groups[0].items[0].key === 'manHours') {
        patTemplate.groups[0].items.shift()
      } else if (this.state.workHourSection.entryType === 0 && patTemplate.groups[0].items[0].key !== 'manHours') {
        patTemplate.groups[0].items.unshift({
          key: 'manHours',
          label: '工时(小时)',
          component: 'Number',
          props: null,
          rule: {
            required: this.state.manHoursRequired,
            min: 0
          }
        })
      }
      this.setState({ patTemplate })
    })
  }

  currentStepId4CheckingContract = async (config) => {
    if (this.props.step === 4) {
      if (config.enableMaintenanceContract) {
        const res = await rest.get(`${urls.queryMaintenanceContract}/${this.props.businessUid}`)
        if (Array.isArray(res) && res.length === 0) {
          const onCancel = () => {
            history.goBack()
          }
          Modal.confirm({
            title: '请确认',
            content: '设备没有维保合同，是否继续维修?',
            okText: '继续',
            cancelText: '返回',
            onCancel,
            onOk: () => console.log('正常维修')
          })
        }
      }
    }
  }

  async getWorkflowConfig() {
    const wfConfig = await rest.get(urls.workflowsConfig + `/${this.props.assetId}`) || {}
    // const approvalsConfig = await rest.get(urls.approvalsConfig + '/workOrder' + `/${this.props.woId}`)

    const cfg = await this.props.dispatch({ type: 'workflowConfig/get/configItem', siteUid: this.props.siteUID, update: false })
    if (cfg) {
      this.setState({ enableCheckInTime: cfg.enableCheckInTime })
      this.setState({ enableSignNeedScanQrCode: cfg.enableSignNeedScanQrCode })
      this.currentStepId4CheckingContract(cfg)
      wfConfig.autoAck = cfg.autoAck
      wfConfig.showWorkOrderType = cfg.showWorkOrderType
    }

    // this.state.hasConfigMultiLevelApproval = approvalsConfig.enable
    this.state.wfConfig = wfConfig
    this.state.manHoursRequired = wfConfig && wfConfig.hourRequired
    this.state.patTemplate = this.generatePATTemplate(wfConfig)
    this.state.partsButtons = this.partsButtons(
      this.state.costs.filter(item => !item.approved).length > 0 ? false : true
    )
    this.state.reassignFormTemplate = this.generateReAssignForm()
    this.state.renderDiv = true
    this.setState(this.state)
  }

  transformDateTimeAPIFaced(data) {
    if (data) {
      return data.replace('T', ' ')
    }
    return ''
  }

  transformDateTimeComponentFaced(data) {
    if (data) {
      return data.replace(' ', 'T')
    }
    return ''
  }

  submitToMultiLevelApproval(val) {
    //如果配置了多节审核直接走多级审核
    rest
      .post(urls.approvalsSubmit + `/workOrder/${this.props.woId}`, undefined, true, {
        extraHeaders: { taskSource: 'multiLevelApprovalTask' }
      })
      .then(res => {
        if (res && res.id) {
          //message.success('维修多级审核提交成功', 2)
          history.replace({
            pathname: route_urls.msg,
            query: { code: 201 },
            state: { desc: '维修多级审核提交成功', next: route_urls.home_wx }
          })
        }
      })
  }

  partsButtons(disabled) {
    return {
      buttons: isAppOffline()
        ? []
        : [{
          type: 'primary',
          label: '提交审核',
          validation: true,
          onClick: val => {
            this.getApprovalConfig(val)
          },
          props: { disabled: disabled || this.props.approvalStatus === '5' }
        }]
    }
  }

  getApprovalConfig = async val => {
    const approvalsConfig = await rest.get(urls.approvalsConfig + '/workOrder' + `/${this.props.woId}`, null, true)
    if (approvalsConfig && approvalsConfig.enable) {
      this.submitToMultiLevelApproval(val)
    } else {
      this.getApproverInfo()
    }
  }

  async getApproverInfo() {
    let assetUid = this.props.assetInfo.uid
    const pmOrderApproverData = await rest.list(new UrlPattern(urls.pmOrderApprover).stringify({ assetUid }), null, true)
    if (pmOrderApproverData.bizStatusCode === 'OK' && pmOrderApproverData.message === 'successful') {
      this.setState({ showApproval: true })
    } else {
      message.warn(pmOrderApproverData.message)
    }
  }

  generatePATTemplate(wfConfig) {
    const { pageWorkOrder } = this.props.config
    let { showWorkOrderType } = wfConfig
    showWorkOrderType && (showWorkOrderType = JSON.parse(showWorkOrderType))
    let that = this
    let groups = [
      {
        title: '工时与其他',
        items: [
          {
            key: 'manHours',
            label: '工时(小时)',
            component: 'Number',
            rule: {
              required: this.state.manHoursRequired,
              min: 0,
              custom: form => {
                if (form.manHours.toString().length > 3) form.manHours = form.manHours.toString().slice(0, 3)
              }
            }
          },
          {
            key: 'otherExpense',
            label: '其他费用(元)',
            component: 'Number',
            rule: { min: 0, max: 99999999, required: wfConfig && wfConfig.otherExpenses }
          },
          {
            key: 'externalExpense',
            label: '外修费用(元)',
            component: 'Number',
            rule: { min: 0, max: 99999999, required: wfConfig && wfConfig.externalExpenses }
          }
        ]
      },
      {
        title: '故障明细',
        items: [
          {
            key: 'caseType',
            label: '故障类别',
            component: 'SelectFaulty',
            props: {
              type: this.state.assetGroup
            },
            rule: { required: wfConfig && wfConfig.isCasetypeRequired ? true : false }
          }
        ]
      },
      showWorkOrderType && showWorkOrderType.rendered && {
        title: showWorkOrderType.label,
        items: [
          {
            key: 'workOrderType',
            label: showWorkOrderType.label,
            component: 'Select',
            type: 'workOrderType',
            rule: { required: showWorkOrderType && showWorkOrderType.required }
          }
        ]
      },
      {
        title: '原因描述',
        items: [
          {
            key: 'patProblems',
            label: '原因描述',
            component: 'TextArea',
            props: { placeholder: '请输入……', maxLength: 500, rows: '2' },
            rule: { required: wfConfig && wfConfig.patProblems }
          }
        ]
      },
      {
        title: '解决方案',
        items: [
          {
            key: 'patActions',
            label: '解决方案',
            component: 'TextArea',
            props: { placeholder: '请输入……', maxLength: 500, rows: '2' },
            rule: { required: wfConfig && wfConfig.patActions }
          }
        ]
      },
      {
        title: '交付前检测',
        items: [
          {
            key: 'patTests',
            label: '交付前检测',
            component: 'TextArea',
            props: { placeholder: '请输入……', maxLength: 500, rows: '2' },
            rule: { required: wfConfig && wfConfig.patTests }
          }
        ]
      },
      {
        title: '最新进展',
        items: [
          {
            key: 'comment',
            label: '最新进展',
            component: 'TextArea',
            props: { placeholder: '请输入……', maxLength: 500, rows: '2' },
          }
        ]
      },
      {
        items: [
          {
            key: 'attachments',
            component: 'ImageUploader',
            props: {
              title: (
                <span>
                  {wfConfig && wfConfig.workOrderImagesUpload ? (
                    <AntdIcon type="exclamation-circle" style={{ color: 'red' }} />
                  ) : (
                    ''
                  )}{' '}
                  附件上传
                </span>
              ),
              maxCount: 5,
              allowFile: true,
              accept: ".jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf, .txt, .ps",
              autoSave: true
            },
            rule: { required: wfConfig && wfConfig.workOrderImagesUpload }
          }
        ]
      },
      pageWorkOrder.group[0].externalQuotation && pageWorkOrder.group[0].externalQuotation.enable && {
        items: [
          { key: 'externalQuotation', label: '外部报价', component: 'Number' }
        ]
      },
      {
        items: [
          {
            key: 'mvsType',
            type: 'mvsType',
            label: 'MVS工单类型',
            component: 'Select',
            first: false,
            hide: () => {
              return !that.state.multiTenant
            },
            rule: { required: that.state.multiTenant }
          },
          {
            key: 'repairResult',
            type: 'repairResult',
            label: '维修结果',
            component: 'Select',
            first: false,
            rule: { required: true }
          },
          {
            key: 'subStatus',
            label: '维修子状态',
            options: [
              {
                key: 1,
                value: '等待备件'
              },
              {
                key: 2,
                value: '返厂维修-有备机'
              },
              {
                key: 3,
                value: '返厂维修-无备机'
              }
            ],
            component: 'Select',
            rule: { required: false }
          },
          {
            key: 'statusStartTime',
            label: '状态开始时间',
            component: 'DateTime',
            rule: { required: false }
          },
          {
            key: 'statusCloseTime',
            label: '状态结束时间',
            component: 'DateTime',
            rule: { required: false }
          },
          {
            key: 'assetStatus',
            label: '资产状态',
            options: [
              {
                key: 1,
                value: '正常'
              },
              {
                key: 3,
                value: '限制功能使用'
              },
              {
                key: 4,
                value: '待报废'
              }
            ].filter(util.hideAssetStatus4),
            component: 'Select',
            rule: { required: true }
          },
          {
            key: 'confirmedUpTime',
            label: '恢复时间',
            component: 'DateTime',
            hide: form => {
              return form.assetStatus !== 1
            },
            placeholder: '请选择恢复时间...',
            rule: { required: true, custom: this.validateConfirmedUpTime.bind(this) }
          }
        ]
      }
    ].filter(Boolean)

    if (this.state.workHourSection.entryType === 1) {
      groups[0].items.shift()
    }

    let formTemplate = {
      buttons: [
        {
          type: 'primary',
          label: '保存',
          onClick: val => this.saveRepairWo(val),
          disabled: this.state.isMultiSaveOrModifyBtnDisabled
        },
        !isAppOffline()
          ? {
            type: 'primary',
            label: '维修完成',
            disabled: this.state.isMultiSaveOrModifyBtnDisabled,
            validation: true,
            confirm: this.state.wfConfig && this.state.wfConfig.autoAck ? '确定维修完成？' : false,
            onClick: val => this.preRepairWo()
          }
          : ''
      ],
      groups
    }

    return formTemplate
  }

  generateReAssignForm() {
    let that = this
    let reassignFormTemplate = {
      buttons: [
        { type: 'primary', label: '转单', validation: true, onClick: val => this.eventReassignWo(val) },
        {
          type: 'primary',
          label: '取消',
          onClick: val => {
            util.fixModalBug()
            this.setState({ showAs: false })
          }
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'assigneeId',
              label: '维修人员',
              component: 'SelectUser',
              rule: { required: true },
              query: {
                assetId: 'assetId',
                excludes: 'excludes'
              }
            },
            {
              key: 'reAssignDesc',
              label: '转单原因',
              component: 'TextArea',
              rule: { required: true },
              props: {
                placeholder: '请输入……',
                maxLength: 200,
                rows: '3',
                onClick: (form, e, cb) => {
                  if (that.state.reAssignDescCompare == form.reAssignDesc) {
                    form.reAssignDesc = ''
                    e.target.value = ''
                    cb(form)
                  }
                }
              }
            }
          ]
        }
      ]
    }
    return reassignFormTemplate
  }

  generateAckForm() {
    return {
      buttons: [
        {
          type: 'primary',
          label: '取消',
          onClick: val => {
            util.fixModalBug()
            this.setState({ showAck: false })
          }
        },
        { type: 'primary', label: '确定', onClick: val => this.repairWo(val) }
      ],
      groups: [
        {
          items: [
            {
              key: 'assigneeId',
              label: '验收人',
              component: 'SelectUser',
              rule: { required: true },
              props: {
                url: new UrlPattern(`${urls.asset}/${this.props.woId}/acker`).stringify({}),
                first: true
              },
              transform: user => user.id,
              query: {}
            }
          ]
        }
      ]
    }
  }

  validateConfirmedUpTime(form) {
    let { downTime, acceptStepStartTime } = this.state
    let uptime = form.confirmedUpTime && moment(form.confirmedUpTime).format('YYYY/MM/DD HH:mm:ss')
    downTime = downTime && moment(downTime).format('YYYY/MM/DD HH:mm:ss')
    acceptStepStartTime = acceptStepStartTime && moment(acceptStepStartTime).format('YYYY/MM/DD HH:mm:ss')

    if (downTime) {
      if (new Date(uptime) < new Date(downTime)) {
        return {
          msg: '恢复时间不能早于停机时间' + downTime
        }
      }
    }
    if (acceptStepStartTime) {
      if (new Date(uptime) < new Date(acceptStepStartTime)) {
        return {
          msg: '恢复时间不能早于接单时间' + acceptStepStartTime
        }
      }
    }
  }

  preRepairWo() {
    if (this.needCheckIn()) {
      message.error('请先签到')
      return
    }
    this.formatStatusTime()
    if (this.state.wfConfig && this.state.wfConfig.autoAck) {
      this.repairWo({})
    } else {
      const template = this.generateAckForm()
      util.fixModalBug()
      this.setState({
        ackFormTempalte: template,
        showAck: true
      })
    }
  }

  repairWo(val) {
    if (!this.validateAttachments()) return
    this.formatStatusTime()
    util.fixModalBug()
    this.setState({ showAck: false })
    if (!val.assigneeId) val.assigneeId = this.props.requestorId
    if (this.state.form.assetStatus == 1)
      val.confirmedUpTime = this.transformDateTimeAPIFaced(val.confirmedUpTime || this.state.form.confirmedUpTime)
    else val.confirmedUpTime = ''

    if (this.validateDateTimeSectionOverlap(this.state.workHourSection.workHoursList)) return

    let params = {}
    if (this.state.workHourSection.entryType === 1) {
      params = Object.assign(
        this.state.form,
        { stepCoworker: this.state.coworkers, ...val },
        this.state.workHourSection
      )
    } else {
      params = Object.assign(
        this.state.form,
        { stepCoworker: this.state.coworkers, ...val },
        { entryType: this.state.workHourSection.entryType }
      )
    }
    params.coworkerEntryType = this.state.coworkerEntryType
    rest
      .post(urls.workorder + '/' + this.props.woId + '/action?type=repair', params, false, {
        extraHeaders: { taskSource: 'completeWorkOrderTask' }
      })
      .then(res => {
        if (res && res == 'success') {
          // history.replace({ pathname: route_urls.msg, query: { code: 201 }, state: { desc: '维修完成', next: route_urls.home_wx } })
          history.go(-1)
          // history.replace({ pathname: route_urls.home_wx + '/consoleWoList', state: { url: urls.woPickedup, cons: { status: 0 }, from: 'repairList' } })
        }
      })
  }

  validateDateTimeSectionOverlap = timeSectionList => {
    timeSectionList.sort((A, B) => {
      let AT = new Date(A.startTime).getTime(),
        BT = new Date(B.startTime).getTime()
      return AT < BT ? -1 : AT > BT ? 1 : 0
    })
    for (let i = 0; i < timeSectionList.length; i++) {
      if (parseFloat(timeSectionList[i].hours) < 0) {
        message.error(
          `${timeSectionList[i].startTime}至${timeSectionList[i].endTime}时间段，结束时间不能小于起始时间`,
          5
        )
        return true
      }
      if (i < timeSectionList.length - 1 && timeSectionList[i].endTime > timeSectionList[i + 1].startTime) {
        message.error('不同工时时间段存在时间重叠，请重新选择')
        return true
      }
    }
  }

  validateAttachments() {
    if (this.state.wfConfig && this.state.wfConfig.workOrderImagesUpload) {
      if (this.state.form.attachments.length === 0) {
        message.error('请上传至少一个附件')
        return false
      }
    }
    return true
  }

  formatStatusTime() {
    if (this.state.form.statusStartTime) {
      let form = this.state.form
      let statusStartTime = this.state.form.statusStartTime
      statusStartTime = statusStartTime.replace('T', ' ')
      statusStartTime += ':00'
      form.statusStartTime = statusStartTime
      this.setState({ form })
    }
    if (this.state.form.statusCloseTime) {
      let form = this.state.form
      let statusCloseTime = this.state.form.statusCloseTime
      statusCloseTime = statusCloseTime.replace('T', ' ')
      statusCloseTime += ':00'
      form.statusCloseTime = statusCloseTime
      this.setState({ form })
    }
  }

  saveRepairWo(val) {
    this.formatStatusTime()
    if (this.state.form.assetStatus == 1 || val.assetStatus == 1)
      val.confirmedUpTime = this.transformDateTimeAPIFaced(val.confirmedUpTime || this.state.form.confirmedUpTime)
    else val.confirmedUpTime = ''

    if (this.validateDateTimeSectionOverlap(this.state.workHourSection.workHoursList)) return

    let params = {}
    if (this.state.workHourSection.entryType === 1) {
      params = Object.assign(
        this.state.form,
        { stepCoworker: this.state.coworkers, ...val },
        this.state.workHourSection
      )
    } else {
      params = Object.assign(
        this.state.form,
        { stepCoworker: this.state.coworkers, ...val },
        { entryType: this.state.workHourSection.entryType }
      )
    }
    params.coworkerEntryType = this.state.coworkerEntryType
    rest
      .post(urls.workorder + '/' + this.props.woId + '/action?type=saveRepair', params, false, {
        extraHeaders: { taskSource: 'workOrderSaveTask' }
      })
      .then(res => {
        if (res && res == 'success') {
          history.replace({
            pathname: route_urls.msg,
            query: { code: 201 },
            state: { desc: '信息保存成功', next: route_urls.home_wx }
          })
        }
      })
  }

  async checkInWo() {
    this.formatStatusTime()
    if (this.state.checkin == 1) {
      this.setState({ checkinDesc: '重复签到', showToast: true })
      this.state.toastTimer = setTimeout(() => {
        this.setState({ showToast: false })
      }, 2000)
      return
    }
    if (this.state.enableSignNeedScanQrCode) {
      const scannedAsset = await scan('asset')
      if (!scannedAsset || scannedAsset.id != this.state.assetId) {
        message.warn('扫码设备非当前设备，请重新扫码')
        return
      } else {
        message.success('签到成功')
      }
    }
    rest
      .post(urls.workorder + '/' + this.props.woId + '/action?type=checkIn', this.state.form, false, {
        extraHeaders: { taskSource: 'workOrderCheckIn' }
      })
      .then(res => {
        if (res && res == 'success') {
          this.setState({ checkinDesc: '签到成功', showToast: true, checkinTime: this.getCurrentDate(), checkin: 1 })
          this.state.toastTimer = setTimeout(() => {
            this.setState({ showToast: false })
          }, 2000)
        }
      })
  }

  showReAssign() {
    util.fixModalBug()
    this.setState({ showAs: true })
  }

  getCurrentDate() {
    let datetime = new Date()
    let month = datetime.getMonth() + 1
    let date = datetime.getDate()
    let hours = datetime.getHours()
    let mins = datetime.getMinutes()
    let secs = datetime.getSeconds()
    return (
      datetime.getFullYear() +
      '-' +
      (month > 9 ? month : '0' + month) +
      '-' +
      (date > 9 ? date : '0' + date) +
      ' ' +
      (hours > 9 ? hours : '0' + hours) +
      ':' +
      (mins > 9 ? mins : '0' + mins) +
      ':' +
      (secs > 9 ? secs : '0' + secs)
    )
  }

  eventReassignWo(val) {
    util.fixModalBug()
    this.setState({ showAs: false })
    val.currentStepId = this.props.step
    val.desc = val.reAssignDesc || this.state.form.reAssignDesc

    rest
      .post(urls.workorder + '/' + this.props.woId + '/action?type=reassign', val, false, {
        extraHeaders: { taskSource: 'reassignWorkOrderTask' }
      })
      .then(res => {
        if (res && res == 'success') {
          history.replace({
            pathname: route_urls.msg,
            query: { code: 201 },
            state: { desc: '转单', next: route_urls.home_wx }
          })
        } else {
          this.setState({ showMsg: '工单已被处理，请返回', showToast: true })
          this.state.toastTimer = setTimeout(() => {
            this.setState({ showToast: false })
          }, 2000)
        }
      })
      .catch(error => {
        history.replace({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
      })
  }

  approval({ approver, desc }) {
    let params = {
      ...this.state.form,
      desc,
      assigneeId: approver,
      stepDetail: this.state.costs.map(part => {
        part.approved = part.approved ? part.approved : null
        return part
      })
    }

    params.statusStartTime = params.statusStartTime
      ? moment(params.statusStartTime).format('YYYY-MM-DD HH:mm:ss')
      : params.statusStartTime
    params.statusCloseTime = params.statusCloseTime
      ? moment(params.statusCloseTime).format('YYYY-MM-DD HH:mm:ss')
      : params.statusCloseTime
    params.coworkerEntryType = this.state.coworkerEntryType
    rest
      .post(urls.workorder + '/' + this.props.woId + '/action?type=approval', params, false, {
        extraHeaders: { taskSource: 'approvalTask' }
      })
      .then(res => {
        if (res && res == 'success') {
          history.replace({
            pathname: route_urls.msg,
            query: { code: 201 },
            state: { desc: '已提交审核', next: route_urls.home_wx }
          })
        }
      })
  }

  showCreateSet() {
    this.setState({ showCreate: true })
  }

  newCreate() {
    history.replace({
      pathname: route_urls.wx.newCreate,
      state: { id: this.props.assetId, srId: this.props.srId }
    })
  }

  renderCosts() {
    const { costs, isMultiVerifyBtnDisabled } = this.state
    return costs.map((item, idx) => {
      return (
        <Cell>
          <CellHeader style={{ padding: '0px 20px 0px 0px' }}>
            {!isMultiVerifyBtnDisabled && (
              <AntdIcon type="copy" theme="twoTone" onClick={this.cloneCost.bind(this, idx, item)} />
            )}
          </CellHeader>
          <CellBody onClick={this.addCost.bind(this, 'show', idx)}>
            <div>
              {'备件名称：' +
                (item.parts ? item.parts : '无') +
                ',  备件数量：' +
                (item.apartsQuantity ? item.apartsQuantity : '0') +
                ',  备件单价(元)：' +
                (item.partsPrice ? item.partsPrice : '0') +
                ', 备件类别：' +
                (item.partsType ? util.preload(this.props.storeState).zh('partsType', item.partsType) : '')}
            </div>
            {Array.isArray(item.attachments) && item.attachments.length > 0 && (
              <ImageUploader title="附件" files={item.attachments} previewMode allowFile />
            )}
          </CellBody>
          <CellFooter>
            {!isMultiVerifyBtnDisabled && <Icon value="cancel" onClick={this.deleteCost.bind(this, idx, item)} />}
          </CellFooter>
        </Cell>
      )
    })
  }

  renderCoworkers() {
    const { coworkers } = this.state
    return coworkers.map((item, idx) => {
      let infoArr = []
      if (item.cowokerUserName) {
        infoArr.push(`协作人：${item.cowokerUserName}`)
      }
      if (item.manHours !== undefined && item.manHours !== null && item.manHours !== '') {
        infoArr.push(`工时(小时)：${item.manHours}`)
      }
      if (item.startTime) {
        infoArr.push(`开始时间：${item.startTime}`)
      }
      if (item.endTime) {
        infoArr.push(`结束时间：${item.endTime}`)
      }
      if (item.remark) {
        infoArr.push(`备注：${item.remark}`)
      }
      return (
        <Cell>
          <CellBody onClick={this.addCost.bind(this, 'showCo', idx)}>
            <p>{infoArr.join('，')}</p>
          </CellBody>
          <CellFooter>
            <Icon value="cancel" onClick={this.deleteCoworker.bind(this, idx)} />
          </CellFooter>
        </Cell>
      )
    })
  }

  renderApprovalReason() {
    return (
      <Form>
        <FormCell>
          <CellBody>
            <TextArea
              onChange={e => {
                this.state.form.desc = e.target.value
                this.setState(this.state)
              }}
              maxLength="200"
            />
          </CellBody>
        </FormCell>
      </Form>
    )
  }

  needCheckIn = () => {
    return this.state.enableCheckInTime && this.state.checkin !== 1
  }

  getTotalCost() {
    const totalCost = sumBy(this.state.costs, p => p.closedPrice)
    return Number.isNaN(totalCost) ? Number(0).toFixed(3) : Number(totalCost).toFixed(3)
  }

  cloneCost(idx) {
    let partTemplate = { ...this.state.costs[idx] }
    partTemplate.id = null
    partTemplate.approved = null
    partTemplate.parts = ''
    partTemplate.partsItemId = ''
    partTemplate.apartsQuantity = ''
    partTemplate.partsPrice = ''
    partTemplate.closedPrice = ''
    // partTemplate.warrantyDate = moment(partTemplate.warrantyDate).format('YYYY-MM-DD')
    this.setState({ partTemplate })
    this.addCost('show', -2)
  }

  deleteCost(idx, item) {
    APMDialog.confirm('确定删除备件？').onDismiss(confirmed => {
      if (confirmed) {
        rest
          .del(
            urls.updatePrice + '/' + item.id,
            { woId: this.props.woId },
            { extraHeaders: { taskSource: 'deleteWorkOrderCostTask' } }
          )
          .then(res => {
            if (res) {
              this.state.costs.splice(idx, 1)
              this.state.partsButtons = this.partsButtons(
                this.state.costs.filter(item => !item.approved).length > 0 ? false : true
              )
              this.setState(this.state)
              message.success('删除成功', 2)
            } else {
              message.error('删除失败', 2)
            }
          })
      }
    })
  }

  deleteCoworker(idx) {
    this.state.coworkers.splice(idx, 1)
    this.setState(this.state)
  }

  addCost(type, idx) {
    this.props.callback(false)
    if (type == 'show') this.setState({ show: true, index: idx })
    else this.setState({ showCo: true, index: idx })
  }

  reAssignToTempUser() {
    genShortcut(route_urls.wx.workorder + '/external/' + this.props.woId)
  }

  render() {
    if (!this.state.renderDiv) return <div />
    if (this.state.show) {
      return (
        <WoCost
          woType="workOrder"
          currentPersonId={this.props.currentLoginPersonId}
          cost={this.state.index === -2 ? this.state.partTemplate : this.state.costs[this.state.index]}
          opTypeIndex={this.state.index}
          multiTenant={this.state.multiTenant}
          assetUid={this.props.assetInfo.uid}
          woId={this.props.woId}
          siteUID={this.props.assetInfo.siteUID}
          isMultiVerifyBtnDisabled={this.state.isMultiVerifyBtnDisabled}
          onResult={cost => {
            this.props.callback(true)
            const costs = cost
              ? this.state.index == -1 || this.state.index == -2
                ? this.state.costs.concat(cost)
                : this.state.costs.slice(0, this.state.index).concat(cost, this.state.costs.slice(this.state.index + 1))
              : this.state.costs
            this.setState({
              show: false,
              costs: costs,
              index: -1
            })
            this.setState({
              partsButtons: this.partsButtons(costs.filter(item => !item.approved).length > 0 ? false : true)
            })
          }}
        />
      )
    }
    if (this.state.showCo)
      return (
        <WoCost
          woType="workOrder"
          coworkerEntryType={this.state.coworkerEntryType}
          assetId={this.state.assetId}
          assetUid={this.props.assetInfo.uid}
          opTypeIndex={this.state.index}
          coworker={this.state.coworkers[this.state.index]}
          currentPersonId={this.props.currentLoginPersonId}
          multiTenant={this.state.multiTenant}
          isMultiVerifyBtnDisabled={this.state.isMultiVerifyBtnDisabled}
          onResult={coworker => {
            console.log('coworker', coworker)
            this.props.callback(true)
            this.setState({
              showCo: false,
              coworkers: coworker
                ? this.state.index == -1
                  ? this.state.coworkers.concat(coworker)
                  : this.state.coworkers
                    .slice(0, this.state.index)
                    .concat(coworker, this.state.coworkers.slice(this.state.index + 1))
                : this.state.coworkers,
              index: -1
            })
          }}
        />
      )
    return (
      <div>
        <Cells>
          <Cell access onClick={this.checkInWo}>
            <CellHeader>
              <Label>{this.needCheckIn() && <Icon value="warn" style={{ fontSize: '14px' }} />}签到</Label>
            </CellHeader>
            <CellBody style={{ textAlign: 'right' }}>{this.state.checkinTime || (this.state.enableSignNeedScanQrCode ? '扫一扫' : '')}</CellBody>
            <CellFooter />
          </Cell>
        </Cells>

        <CellsTitle>备件明细</CellsTitle>
        <Cells>
          <Cell access onClick={this.addCost.bind(this, 'show', -1)}>
            <CellHeader>
              <Label>增加备件</Label>
            </CellHeader>
            <CellBody>总价格：{this.getTotalCost()} (元)</CellBody>
            <CellFooter />
          </Cell>
          {this.renderCosts()}
        </Cells>
        <GeneralForm buttons={this.state.partsButtons.buttons} />

        <CellsTitle>协作人工时与其他</CellsTitle>
        <Cells>
          <FormCell select selectPos="after">
            <CellHeader>
              <Label>工时类型</Label>
            </CellHeader>
            <CellBody>
              <Select
                value={this.state.coworkerEntryType}
                onChange={e => {
                  // 有coworkers，则需提示，修改类型则会清空coworkers
                  const value = e && e.target ? e.target.value : undefined
                  if (value === undefined) return
                  if (this.state.coworkers.length > 0) {
                    APMDialog.confirm('修改工时类型会清空协作人信息，是否继续？').onDismiss((confirmed) => {
                      if (confirmed) {
                        this.setState({ coworkers: [], coworkerEntryType: value })
                      } else {
                        return
                      }
                    })
                  } else {
                    // 没有coworkers，则直接修改
                    this.setState({ coworkerEntryType: value })
                  }
                }}
              >
                <option value={0}>填写总工时</option>
                <option value={1}>增加工时段</option>
              </Select>
            </CellBody>
          </FormCell>
          <Cell access onClick={this.addCost.bind(this, 'showCo', -1)}>
            <CellHeader>
              <Label>增加协作人</Label>
            </CellHeader>
            <CellBody />
            <CellFooter />
          </Cell>
          {this.renderCoworkers()}
        </Cells>

        <WorkHoursInput
          entryType={this.state.workHourSection.entryType}
          workHoursList={this.state.workHourSection.workHoursList}
          hours={this.state.workHourSection.manHours}
          approvalStatus={APPROVE_STATUS[this.props.approvalStatus]}
          onWorkHoursOutput={this.onWorkHoursOutput}
        />

        <GeneralForm
          value={this.state.form}
          buttons={this.state.patTemplate.buttons}
          groups={this.state.patTemplate.groups}
          onChange={val => {
            if (this.state.repairResultChange != val.repairResult) {
              this.state.repairResultChange = val.repairResult
              switch (Number(val.repairResult)) {
                case 1:
                  val.assetStatus = 1
                  break
                case 2:
                  val.assetStatus = 3
                  break
                case 3:
                  val.assetStatus = 4
                  break
                case 4:
                  val.assetStatus = 3
                  break
                case 5:
                  val.assetStatus = 1
                  break
              }
            }
            Object.assign(this.state.form, val)
            this.setState(this.state)
          }}
        />
        {!isAppOffline() && (
          <Cells>
            <Cell access onClick={this.showReAssign}>
              <CellHeader>
                <Label>转单</Label>
              </CellHeader>
              <CellBody />
              <CellFooter />
            </Cell>
            <Cell access onClick={this.reAssignToTempUser}>
              <CellHeader>
                <Label>短码</Label>
              </CellHeader>
              <CellBody />
              <CellFooter />
            </Cell>
            <Cell access onClick={this.showCreateSet}>
              <CellHeader>
                <Label>新建工单</Label>
              </CellHeader>
              <CellBody />
              <CellFooter />
            </Cell>
          </Cells>
        )}
        {this.state.showAs && (
          <Dialog title="转单" show={this.state.showAs} style={{ width: '98%', maxWidth: 'none' }}>
            <GeneralForm
              value={this.state.form}
              buttons={this.state.reassignFormTemplate.buttons}
              groups={this.state.reassignFormTemplate.groups}
            />
          </Dialog>
        )}
        {this.state.showCreate && (
          <Dialog
            show={this.state.showCreate}
            style={{ width: '60%', maxWidth: 'none' }}
            title=""
            buttons={[
              {
                type: 'default',
                label: '取消',
                onClick: () => this.setState({ showCreate: false })
              },
              {
                type: 'primary',
                label: '确定',
                onClick: this.newCreate
              }
            ]}
          >
            维修数据还未保存，确定要新建工单？
          </Dialog>
        )}
        {/* <ButtonArea>
          <Button type="primary" onClick={this.newCreate}>新建工单</Button>
        </ButtonArea> */}
        <Toast icon="success-no-circle" show={this.state.showToast}>
          {this.state.checkinDesc}
        </Toast>
        {this.state.showApproval && (
          <AssignApproverDialog
            key="approver-dialog"
            assetUid={this.props.uid}
            onPositive={({ approver, desc }) => {
              // util.fixModalBug()
              document.getElementsByTagName('body')[0].classList.remove('modal-open')
              this.approval({ approver, desc })
            }}
            onNegative={() => {
              // util.fixModalBug()
              document.getElementsByTagName('body')[0].classList.remove('modal-open')
              this.setState({ showApproval: false })
            }}
          />
        )}
        {this.state.showAck && (
          <Dialog title="选择验收人" show={this.state.showAck} style={{ width: '98%', maxWidth: 'none' }}>
            <GeneralForm
              value={{ assigneeId: this.props.requestorId }}
              buttons={this.state.ackFormTempalte.buttons}
              groups={this.state.ackFormTempalte.groups}
            />
          </Dialog>
        )}
      </div>
    )
  }
}
