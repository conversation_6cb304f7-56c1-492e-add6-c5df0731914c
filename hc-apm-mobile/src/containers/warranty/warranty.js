import React, { useEffect, useState, useRef } from 'react'
import './warranty.less'

const Warranty = props => {
  const { Info } = props

  const deviceInfo = {
    psiDescription: '设备类型/System Type',
    so: 'PDO号/PDO Number',
    systemId: '系统编号/System ID'
  }

  return (
    <main className="warranty-body">
      <div className="main-title">尊敬的{Info.assetWarrantyVersion ?'':':'}{Info.hospitalName}{Info.assetWarrantyVersion ?':':''}</div>
      <div className="main-body">
        非常感谢您选择GE HealthCare的医疗设备，贵院<span>{Info.psiDescription}</span>
        设备已安装调试完毕，符合临床应用的条件。依据该设备销售合同规定，其保修开始时间为<span>{Info.startDate}</span>。
        {Info.warrantyPeriod ? `为该设备提供为期${Info.warrantyPeriod}年的保修服务。` : '具体保修期与'}
        保修条款，请参见合同《保修服务条款附件》。 如果您有任何疑问，请您致电GE客户服务部电话:************，或致电GE
        HealthCare区域客户服务销售代表。 再次感谢您选择GE HealthCare公司产品。
      </div>
      <div className="main-info">
        {Object.keys(deviceInfo).map(v => {
          return (
            <div key={v}>
              <div className="info-label">{deviceInfo[v]}</div>
              <div className="info-value">{Info[v]}</div>
            </div>
          )
        })}
      </div>
    </main>
  )
}

export default Warranty
