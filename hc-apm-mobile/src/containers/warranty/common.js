import React, { useEffect, useState, useRef } from 'react'
import './warranty.less'
import { Spin } from 'antd'
import html2canvas from 'html2canvas'
import logo from './images/GE HC Logo.png'
import { rest, urls, util } from '@/constants'
import { throttle } from 'lodash'
import { setShareInfo } from '@/actions/jsApi'
import Warranty from './warranty'
import OutOfWarranty from './outofwarranty'

function CommonWarranty(props) {
  console.log('props', props)
  const { id } = props.params
  const [Info, setInfo] = useState(null)
  const [showImg, setShowImg] = useState(false)
  const [domImg, setDomImg] = useState(null)
  const viewDom = useRef()
  const type = props.location.pathname.includes('warranty') ? 'warranty' : 'outofwarranty'

  useEffect(() => {
    util.log({ type: type === 'warranty' ? '入保函查看' : '出保函查看', target: id })
  }, [])

  useEffect(() => {
    getData(id)
  }, [id])

  const getData = id => {
    const url = type === 'warranty' ? `${urls.warrantyInfo}${id}` : `${urls.outofwarrantyInfo}?objectId=${id}`
    rest
      .get(url)
      .then(res => {
        if (res) {
          setInfo(res)
          const config = {
            title: type === 'warranty' ? '入保通知函' : '出保通知函',
            desc: `${res.hospitalName} ${res.systemId} ${res.psiDescription}`
          }
          setShareInfo(config)
        } else {
          message.error('获取信息失败')
        }
      })
      .catch(err => {
        message.error('获取信息失败')
        console.log('err', err)
      })
  }

  const saveToLocal = throttle(async () => {
    const downloadId = type === 'warranty' ? Info.uploadFileId : id
    if (downloadId) {
      // 6316ec66a6ba99141d92c0e6
      const downloadUrl = await util.download(downloadId)
      console.log('downloadUrl', downloadUrl)
      if (downloadUrl) {
        util.log({ type: type === 'warranty' ? '入保函下载' : '出保函下载', target: id })
        location.href = downloadUrl
      } else {
        saveImg()
      }
    } else {
      saveImg()
    }
  }, 5000)

  const saveImg = () => {
    util.log({ type: type === 'warranty' ? '入保函下载' : '出保函下载', target: id })
    let hideDom = document.querySelector('.save-button')
    hideDom.style.display = 'none'
    const Dom = viewDom.current
    setShowImg(true)
    if (domImg) {
      return
    }
    setTimeout(() => {
      html2canvas(Dom, {
        scale: 1.5,
        height: Dom.scrollHeight,
        width: Dom.clientWidth
      })
        .then(canvas => {
          let context = canvas.getContext('2d')
          // 关闭抗锯齿
          context.mozImageSmoothingEnabled = false
          context.webkitImageSmoothingEnabled = false
          context.msImageSmoothingEnabled = false
          context.imageSmoothingEnabled = false
          const imgData = canvas.toDataURL('image/jpg')
          setDomImg(imgData)
        })
        .catch(function (error) {
          console.log(error, 'html2canvas-error')
        })
    }, 1000)
  }

  const closeImgDom = () => {
    let hideDom = document.querySelector('.save-button')
    hideDom.style.display = 'block'
    setShowImg(false)
  }
  return (
    <div id="warranty">
      {Info && (
        <div className="warranty-dom" ref={viewDom}>
          <header className="warranty-header">
            <div className='top-box'>
              <div className="logo">
                <img src={logo} alt="" />
              </div>
              {type === 'warranty' && Info.assetWarrantyVersion && <div className='version'>{Info.assetWarrantyVersion}</div>}
            </div>
            <div className="title">
              <div className="title-text">{type === 'warranty' ? '入保通知函' : '出保通知函'}</div>
            </div>
          </header>
          {type === 'warranty' && <Warranty Info={Info}></Warranty>}
          {type === 'outofwarranty' && Array.isArray(Info) && Info.length > 0 && (
            <OutOfWarranty Info={Info}></OutOfWarranty>
          )}
          <footer className="warranty-footer">
            <button className="save-button" onClick={saveToLocal}>
              保存至本地
            </button>
          </footer>
        </div>
      )}
      {!Info && <Spin style={{ margin: '100px auto', display: 'block' }} />}
      {showImg && (
        <div className="domImg-box" onClick={closeImgDom}>
          {domImg ? (
            <div>
              <img src={domImg} />
              <h3>长按保存分享</h3>
            </div>
          ) : (
            <Spin style={{ margin: '100px auto', display: 'block' }} />
          )}
        </div>
      )}
    </div>
  )
}

export default CommonWarranty
