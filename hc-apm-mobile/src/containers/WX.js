import React, { lazy } from 'react'
import { Toptips, Toast } from 'react-weui'
import { NoticeBar } from 'antd-mobile'
import { connect } from 'dva'
import { browserHistory } from 'react-router'
import { route_urls, storage, util, rest } from '../constants'
import { BackToConsole } from '@components'
import { systemActions } from '../actions'
import Advertisement from './ib/common/Advertisement'
import RegisterLogin from './ib/common/RegisterLogin'

import { getDDTicket, jsApiInit, isWxBrowser, isFeiShu, isDDBrowser } from '../actions/jsApi'
import { Loading } from '..'

const DrawerRight = lazy(() => import(/* webpackChunkName: "DrawerRight" */ './ib/common/DrawerRight'))
const { clear, wxLogin, ddLogin, fsLogin, myself, leavingAPM, callbackMessageId, removeUrlCode, reloadPageForWxAuth, reloadPageForFarEastAuth } =
  systemActions
class WX extends React.Component {
  state = {
    auth: null,
    showRegModal: true
  }

  async componentWillMount() {
    window.onbeforeunload = leavingAPM
    if (localStorage.getItem(storage.vconsole)) {
      const d = new Date()
      if (d.getMonth().toString() + d.getDate().toString() === localStorage.getItem(storage.vconsole)) {
        const Vconsole = require('vconsole')
        window.vconsole = new Vconsole()
      }
    }

    const tokenLogin = async token => {
      if (token) {
        rest.setToken(token)
        await myself().then(async res => {
          this.setState({ auth: true })
          await removeUrlCode()
        })
      } else {
        this.goLogin()
      }
    }

    let { code, token, corpId } = this.props.location.query

    if (token) {
      // url带token优先
      tokenLogin(token)
    } else if (util.myself()) {
      this.setState({ auth: true })
    } else if (isDDBrowser()) {
      if (!corpId) {
        const res = await getDDTicket()
        corpId = res.corpId
      }
      ddLogin(corpId).then(() => {
        this.setState({ auth: true })
      })
    } else if (localStorage.getItem(storage.loginAs)) {
      // loginAs登录
      tokenLogin(localStorage.getItem(storage.token))
    } else if (isWxBrowser()) {
      if (code) {
        const res = await wxLogin({ code })
        if (res) {
          this.setState({ auth: true })
        }
        await removeUrlCode()
      } else if (window.self === window.top) {
        await reloadPageForWxAuth()
      } else {
        await tokenLogin(localStorage.getItem(storage.token))
      }
    } else if (isFeiShu() || util.isFarEastSite()) {
      if (code) {
        const res = await fsLogin({ code })
        if (res) {
          this.setState({ auth: true });
          
          // 检查 STATE 参数是否存在
          const { state } = this.props.location.query;
          if (state) {
            try {
              // 直接解码 STATE 参数获取路径
              const targetPath = decodeURIComponent(state);
              // 使用 browserHistory 跳转到目标页面
              browserHistory.replace(targetPath);
            } catch (error) {
              console.error('解析 STATE 参数失败:', error);
            }
          }
        }
        await removeUrlCode()
      } else if (localStorage.getItem(storage.token)) {
        await tokenLogin(localStorage.getItem(storage.token))
      } else {
        reloadPageForFarEastAuth()
      }
    } else {
      tokenLogin(localStorage.getItem(storage.token))
    }

    if (isWxBrowser()) {
      jsApiInit() // 为微信ios环境保存首屏地址
    }
  }

  componentDidMount() {
    let { wxMessageUid } = this.props.location.query
    if (wxMessageUid) {
      callbackMessageId(wxMessageUid)
    }
    // const Vconsole = require('vconsole')
    // new Vconsole()
    // this.props.dispatch({ type: 'global.message.alert', message: '此版本为钉钉测试版本' })
  }

  goLogin() {
    if (this.goIbUserReg()) {
      const { location } = this.props
      const state = location.pathname !== route_urls.login ? { pathBeforeLogin: location } : undefined
      browserHistory.replace({ pathname: route_urls.login, state })
    }
  }

  // 在微信浏览器, ib环境中, 无绑定apm用户即将跳转登录页面时, 当url带有assetId的, 跳转至ib注册页面
  goIbUserReg = () => {
    const {
      location: { query }
    } = this.props
    if (util.isIbSite() && isWxBrowser() && query && query.assetId) {
      browserHistory.push({ pathname: `${route_urls.register}/${query.assetId}` })
      return false
    } else return true
  }

  renderToptips() {
    const { global } = this.props
    if (global.error) {
      setTimeout(() => clear(), 2500)
      return (
        <Toptips type="warn" show>
          {global.error}
        </Toptips>
      )
    }
    if (global.message) {
      return <NoticeBar marqueeProps={{ loop: true }}>{global.message}</NoticeBar>
    }

    return <Advertisement location={location.pathname} />
  }

  renderChildren() {
    const { myself, children } = this.props
    const { auth } = this.state
    const Children = (
      <React.Fragment>
        {children}
        {util.isIbSite() && <DrawerRight />}
        <BackToConsole />
      </React.Fragment>
    )

    /** 校验登录成功, 并且校验userid */
    if (auth && myself && myself.userAccount) {
      if (myself.userAccount.id) return Children
      else {
        const { state } = this.props.location
        if (state && state.byPass) {
          return (
            <React.Fragment>
              <RegisterLogin
                visible={util.isIbSite() && state.byPass === 'reg' && this.state.showRegModal}
                maskClosable={false}
                getContainer={() => document.getElementsByClassName('main')[0]}
                onCancel={() => {
                  this.setState({ showRegModal: false })
                }}
              />
              {Children}
            </React.Fragment>
          )
        } else {
          this.goLogin()
        }
      }
    } else {
      return (
        // <Toast icon="loading" show>
        //   权限认证中...
        // </Toast>
        <Loading />
      )
    }
  }

  render() {
    return (
      <div className="flex column bgColor main">
        <Toast icon="loading" show={this.props.global.loading}>
          数据加载中...
        </Toast>
        {this.renderToptips()}
        {this.renderChildren()}
        <div style={{ paddingBottom: 'env(safe-area-inset-bottom)' }} />
      </div>
    )
  }
}

function mapStateToProps(state) {
  return {
    global: state.global,
    myself: state.preload.myself
  }
}

export default connect(mapStateToProps)(WX)
