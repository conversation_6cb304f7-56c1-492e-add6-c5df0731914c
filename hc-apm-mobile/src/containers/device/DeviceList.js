import React from 'react'
import { Cell, CellHeader, CellBody, CellFooter } from 'react-weui'
import { connect } from 'react-redux'
import { Switch, Icon, Dropdown, Menu } from 'antd'
import { urls, util } from '../../constants'
import { APMList } from '../../components'
import { DeviceFilter } from './index'
import { browserHistory as history } from 'react-router'
import { GEDeviceType } from '@/containers/ib/common/AssetHeader'
// import { browserHistory as history } from 'react-router'
import { showDeviceFavorites } from '@/containers/device/DeviceFavorite'
import { isAppOffline } from '@/actions/jsApi'
const MenuItem = ({ text, onChange, defaultChecked }) => (
  <Menu.Item style={{ padding: '10px' }}>
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 5 }}>
      <span>{text}</span>
      <Switch size="small" onChange={onChange} defaultChecked={defaultChecked} />
    </div>
  </Menu.Item>
);
@connect(state => ({
  config: state.common.get('assetConfig')
}))
class DeviceList extends React.Component {
  state = {
    isDevicePerformance: this.props.route.path === "device/performance/single"
  }

  componentWillMount() {
    this.init()
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.location.query) {
      const query = { ...this.state.list.query, ...nextProps.location.query }
      const list = { ...this.state.list, query }
      this.setState({ list })
    }
  }

  init() {
    const defaultQuery = util.isSupplier()
      ? {}
      : {
          isValid: 'true',
          status: '[1,2,3,4,5]',
          assetName: '',
          assetGroupId: '',
          assetGroupSecondary: '',
          sort: null
        }
    const { listConfig, detailBrief } = this.props.config

    const itemContent = [...listConfig]
    const { withSubAssets, sort, isSpecial } = this.props.location.query
    const menu = (
      <Menu>
        {util.hasSpecialAsset() && <MenuItem text="特种设备" onChange={this.isSpecial} defaultChecked={isSpecial} />}
        <MenuItem text="子设备" onChange={this.withSubAssets} defaultChecked={withSubAssets} />
        <MenuItem text="创建时间" onChange={this.orderBy} defaultChecked={sort} />
      </Menu>
    )
    let url = urls.assets
    if (util.isSuperUser()) {
      url = url + '/getEmPowermentList'
    }
    if (this.state.isDevicePerformance) {
      url = url + '/getPerformanceAssetInfoList'
    }
    // 从调剂中心设备统计页面跳转，带borrowStatus和sharedAssetType参数
    if (this.props.location.query.sharedAssetType) {
      itemContent.push({ key: 'borrowStatusName' , label: '借用状态' })
    }
    const list = {
      title: '设备列表',
      url,
      showParent: true,
      childKey: 'subAssets',
      query: { ...defaultQuery, ...this.props.location.query },
      itemHeader: {
        key: 'name',
        label: '设备名称',
        icons: this.state.isDevicePerformance ? null : setHeaderIcons
      },
      itemContent,
      // badge: { key: 'status', label: '设备状态', type: 'assetStatus' },
      extra: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ position: 'relative', marginLeft: '5px', minWidth: '28px' }} onClick={showDeviceFavorites}>
            <Icon type="star" theme="filled" style={{ color: '#00B9E6', fontSize: '18px' }} />
            <span style={{ position: 'absolute', left: 0, bottom: '-9px', fontSize: '9px', fontWeight: 300 }}>
              收藏
            </span>
          </div>
          {!util.isSupplier() && <Dropdown overlay={menu} trigger={['click']}>
            <Icon type="unordered-list" style={{ fontSize: '20px' }} />
          </Dropdown>}
        </div>
      ),
      // <Circle color="#04DA6E" />
      badgeStyle: util.isIbTenant() ? ibBadgeStyle : badgeStyle,
      transform: setAssetStatusByOpenAlert,
      distinct: record => record.id,
      onClick: asset => {
        if (this.state.isDevicePerformance) {
          location.href = `/reports/benefitanalysis?assetUid=${asset.uid}&view=mobile`
        } else {
          this.props.router.push({
            pathname: `${this.props.routes.map(r => r.path).join('/')}/${asset.id}`,
            state: { asset }
          })
        }
      }
    }
    if (util.isIbTenant()) {
      // list.badge = { key: 'ibDeviceStatus', label: 'IB设备状态', type: 'ibDeviceStatus' }
      list.badge = asset => {
        // if (asset.installStatus === 1) return null
        const content =
          asset.ibDeviceStatus === 3
            ? { label: '维修中', style: ibBadgeStyle[3] }
            : asset.ibDeviceStatus === 2
            ? { label: '报警中', style: ibBadgeStyle[2] }
            : asset.ibDeviceStatus === 4
            ? { label: '未连通无报修', style: ibBadgeStyle[4] }
            : asset.ibDeviceStatus === 1
            ? { label: '未见异常', style: ibBadgeStyle[1] }
            : { label: '', style: {} }
        return (
          <div style={{ color: content.style.color }}>
            {content.label} {content.style.icon}
          </div>
        )
      }
    } else if (detailBrief.find(config => config.key === 'status')) {
      list.badge = { key: 'status', label: '设备状态', type: 'assetStatus' }
    }
    this.setState({ list, isIb: util.isIbTenant() })
  }

  eventChangeQuery = query => {
    history.replace({
      pathname: this.props.location.pathname,
      query: { ...this.state.list.query, ...query }
    })
  }

  isSpecial = checked => {
    this.eventChangeQuery({
      isSpecial: checked ? true : null
    })
  }

  withSubAssets = checked => {
    this.eventChangeQuery({
      withSubAssets: checked ? true : null
    })
  }

  orderBy = checked => {
    this.eventChangeQuery({
      sort: checked ? 'createdDate,desc' : null
    })
  }

  render() {
    const { query } = this.state.list
    return (
      <div>
        {!util.isSupplier() && !isAppOffline() && (
          <DeviceFilter
            style={{ marginTop: 0 }}
            query={query}
            onQuery={this.eventChangeQuery}
            isIb={this.state.isIb}
            scene={this.state.isDevicePerformance ? "performance" : "default"}
          />
        )}
        <APMList {...this.state.list}>{child => <ChildrenAsset asset={child} />}</APMList>
      </div>
    )
  }
}
const ChildrenAsset = props => {
  const { asset } = props
  const onClick = e => {
    e.stopPropagation()
    history.push(`/wx/device/${asset.id}`)
  }
  return (
    <Cell access style={{ fontSize: '80%' }} onClick={onClick}>
      <CellBody>{asset.aliasName || asset.name}</CellBody>
      <CellFooter>{asset.serialNum}</CellFooter>
    </Cell>
  )
}

const Circle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <circle cx="5" cy="5" r="5" fill={color} />
  </svg>
)
const TriAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 5 0 L 10 10 L 0 10 L 5 0" />
  </svg>
)
const DoAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 5 0 L 5 10 L 10 5 L 0 5 L 5 0" />
  </svg>
)
const DdAngle = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 0 5 L 5 10 L 10 5 L 5 0 L 0 5" />
  </svg>
)
const Square = ({ color }) => (
  <svg version="1.1" width="10" height="10">
    <path fill={color} d="M 2 2 L 8 2 L 8 8 L 8 2 L 2 2" />
    <rect x={0} y={0} width="10" height="10" stroke={color} fill={color} strokeWidth="2" />
  </svg>
)
const badgeStyle = {
  1: { color: '#04DA6E', icon: <Circle color="#04DA6E" /> },
  2: { color: '#FF2323', icon: <TriAngle color="#FF2323" /> },
  报警: { color: '#FF2323', icon: <TriAngle color="#FF2323" /> },
  3: { color: '#F37C00', icon: <DdAngle color="#F37C00" /> },
  4: { color: '#FF2323', icon: <DoAngle color="#FF2323" /> },
  5: { color: '#FF2323', icon: <Square color="#FF2323" /> }
}
const ibBadgeStyle = {
  1: { color: '#2a8c33', icon: <img style={{ marginRight: '5px' }} src={require('./images/Normal.svg')} /> },
  2: { color: '#b20a2e', icon: <img style={{ marginRight: '5px' }} src={require('./images/Alert.svg')} /> },
  3: { color: '#ec8415', icon: <img style={{ marginRight: '5px' }} src={require('./images/Maintain.svg')} /> },
  4: { color: '#2B2D2E', icon: <img style={{ marginRight: '5px' }} src={require('./images/MaintainTwo.svg')} /> }
}
const ConnectIcons = {
  1: <img src={require('./images/Unlink.svg')} />,
  2: <img src={require('./images/Link.svg')} />
}
const getDeviceTypeIcon = type => {
  const deviceType = GEDeviceType.find(item => item.key.includes(type))
  if (deviceType) {
    return deviceType.component
  } else {
    return null
  }
}
export const setHeaderIcons = asset => (
  <React.Fragment>
    {[1, 4].includes(asset.ibStatus) && <img src={require('./images/APMIB.svg')} />}
    &nbsp;
    {util.isIbSite() &&
      (asset.oneQrCode ? (
        <img src={require('./images/HasQrCode.svg')} />
      ) : (
        <img src={require('./images/NoQrcode.svg')} />
      ))}
    &nbsp;
    {asset.ibStatus !== null && asset.connectToInsite === 1 && <img src={require('./images/Unlink.svg')} />}
    {asset.ibStatus !== null && asset.connectToInsite === 2 && <img src={require('./images/Link.svg')} />}
    &nbsp;
    {asset.geDeviceType && getDeviceTypeIcon(asset.geDeviceType)}
  </React.Fragment>
)

function setAssetStatusByOpenAlert(asset) {
  if (asset && asset.hasOpenAlert) {
    asset.status = '报警'
    asset.deviceStatus = '报警'
  }
  return asset
}

export { Circle, TriAngle, DoAngle, Square, DdAngle, badgeStyle }

export { setAssetStatusByOpenAlert }

export default DeviceList
