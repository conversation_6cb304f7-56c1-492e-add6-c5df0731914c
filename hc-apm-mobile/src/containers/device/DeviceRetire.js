import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { message } from 'antd'
import { Preview, Form } from '@components'
import { rest, urls, route_urls, util } from '@constants'

const briefConfg = [
  'name', 'functionType', 'departNum', 'financingNum', 'registrationNo', 'serialNum',
  { key: 'orgPath', i18n: 'orgId' }, 'clinicalDeptName', 'locationName', { key: 'status', type: 'assetStatus' },
  'purchasePrice', 'manufactDate', { key: 'lifecycleInYear', i18n: 'lifecycle' }, 'installDate', 'comments']
  .map(item => typeof item === 'string' ? { key: item } : item)

const DeviceRetire = (props) => {
  const myself = useSelector(state => state.preload.myself);
  const [asset, setAsset] = useState()
  const [retireApplyData, setRetireApplyData] = useState()
  const [doneText, setDoneText] = useState('审批已完成')
  const [needApproval, setNeedApproval] = useState(true)

  const isClinicalStaffs = () => {
    if (myself && Array.isArray(myself.userRoleList)) {
      const roles = myself.userRoleList.map(item => item.name);
      const allowedRoles = ['NursingStaff', 'DeptHead', 'ClinicalStaff'];
      return roles.every(role => allowedRoles.includes(role));
    }
    return false;
  }
  const isClinicalByFunc = () => util.hasFunction('ASSET_MOBILE_ASSET_SCRAP_CLINICAL_BUTTON')
  const isAssetStaffByFunc = () => util.hasFunction('ASSET_MOBILE_ASSET_SCRAP_ASSET_BUTTON')
  const hasOneOfFunc = () => isClinicalByFunc() || isAssetStaffByFunc()
  const isApproval = props.params.uid ? true : false // props.params.uid 代表是审批
  const confirmLabel = isApproval ? '同意' : '申请'
  const isFromList = props.location.action === 'PUSH'

  const onCancel = () => props.router.goBack()
  const onSubmit = (data) => {
    if (isApproval) {
      rest.post(`${urls.assetScrap}/applyReview`, { ...retireApplyData, ...data, scrapId: props.params.uid }).then(res => {
        if (res) {
          message.info('审批通过')
          if (isFromList) {
            props.router.goBack()
          } else {
            props.router.push({ pathname: route_urls.home_wx })
          }
        }
      })
    } else {
      rest.post(`${urls.assetScrap}/apply`, { ...data, assetId: asset.id, assetUid: asset.uid }).then(res => {
        if (res) {
          message.info('申请成功')
          props.router.goBack()
        }
      })
    }
  }

  const buttons = isApproval && (!needApproval || !hasOneOfFunc()) ? [] : [ // 审批页面如果审批完成或者临床人员, 不显示按钮
    !isApproval && hasOneOfFunc() && { type: 'default', label: '取消', onClick: onCancel },
    isApproval && isAssetStaffByFunc() && {
      type: 'warn',
      label: '拒绝',
      confirm: `确定拒绝?`,
      disabled: 'unchanged',
      validation: true,
      signature: 'auditSignature',
      onClick: val => onSubmit({ ...val, approval: false })
    },
    hasOneOfFunc() &&{
      type: 'primary',
      label: confirmLabel,
      confirm: `确定${confirmLabel}?`,
      disabled: 'unchanged',
      validation: true,
      signature: 'auditSignature',
      onClick: val => isApproval ? onSubmit({ ...val, approval: true }) : onSubmit(val)
    }
  ].filter(Boolean)

  const formGroups = [
    !isApproval && isClinicalByFunc() && {
      title: '报废理由 (必填)', items: [
        { key: 'applyReason', label: '报废理由', component: 'TextArea', rule: { required: true } },
        {
          key: 'attachments', label: '报废图片', component: 'ImageUploader', props: {
            title: '报废图片',
            maxCount: 5,
            autoSave: true
          },
        }
      ]
    },
    isApproval && {
      title: '报废理由', items: [
        { key: 'applyReason', label: '报废理由', component: 'Element', render: ({ value }) => <pre>{value.applyReason}</pre> },
        { key: 'attachments', label: '报废图片', component: 'ImageUploader', props: { title: '报废图片', previewMode: true }, }
      ]
    },
    // 审批页面的文本框只读显示: 当审批完成或者临床科室人员查看
    isApproval && (!needApproval || !isAssetStaffByFunc()) && {
      title: '鉴定意见', items: [
        { key: 'approvalReason', label: '鉴定意见', component: 'Element', render: ({ value }) => <pre>{value.approvalReason}</pre> },
        { key: 'equipmentDisposalMethod', label: '处置方式', component: 'Label', type: 'equipmentDisposalMethod', props: { disabled: true } },
        { key: 'customDisposalMethod', label: '其他方式', component: 'Label', props: { disabled: true }, hide: val => val.equipmentDisposalMethod != 0 },
      ]
    },
    // 审批页面的输入框: 临床科室人员不显示, 审批完成也不显示
    (!isApproval || needApproval) && isAssetStaffByFunc() && {
      title: '鉴定意见 (必填)', items: [
        { key: 'reviewReason', label: '鉴定意见', component: 'TextArea', rule: { required: true } },
        { key: 'equipmentDisposalMethod', label: '处置方式', component: 'Select', type: 'equipmentDisposalMethod' },
        { key: 'customDisposalMethod', label: '其他方式', hide: val => val.equipmentDisposalMethod != 0 },
      ]
    }
  ].filter(Boolean)

  useEffect(() => {
    // console.log('props', props, myself)

    // 资产台账传递的asset
    if (props.location.state && props.location.state.asset) {
      setAsset(props.location.state.asset)
    }
    // 获取申请信息
    if (props.params.uid) {
      rest.get(`${urls.assetScrap}/getById/${props.params.uid}`).then(res => {
        if (res) {
          res.currentStatus === 2 && setDoneText('鉴定通过，待多级审批')
          setNeedApproval(res.currentStatus === 1)
          setRetireApplyData(res)
          if (res.assetUid) {
            rest.get(`${urls.assets}/${res.assetId}`).then(setAsset)
          }
        }
      })
    } else {
      setRetireApplyData({})
    }
  }, [])

  if (!asset && !isApproval)
    return <div>无报废资产信息</div>
  else {
    return (
      <div>
        {asset && <Preview title="设备基本信息" data={asset} brief={briefConfg} />}
        {retireApplyData && <Form title="报废申请" value={retireApplyData} groups={formGroups} buttons={buttons} />}
        {isApproval && !needApproval && <div style={{ textAlign: 'center', margin: 10 }}>{doneText}</div>}
      </div>
    )
  }

}

export default DeviceRetire
