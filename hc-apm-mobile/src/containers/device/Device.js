import React from 'react'
import { connect } from 'react-redux'
import { browserHistory as history } from 'react-router'
import { urls, rest, route_urls, fsoUtil, util } from '../../constants'
import { NavBar } from '../../components'
import Actions from './DeviceActions'
import { isAppOffline } from '@/actions/jsApi'

const filterGroups = groups => {
  if (isAppOffline()) {
    const { props } = groups
    return { props }
  } else {
    return groups
  }
}

export const isUsDevice = type => [3, 31, 32, 33].some(o => o === type)
const TAG_ICNETER = 'tagforcode_icenter'

class Device extends React.Component {
  state = {
    asset: null,
    editable: false,
    groups: {
      props: { label: '设备台账' },
      events: { label: '事件记录' }
    },
    orderCount: {
      pm: 0,
      repair: 0
    }
  }

  async componentWillMount() {
    const { dispatch } = this.props
    if (!util.isIbSite()) {
      await dispatch({ type: 'common/get/entity-gbCodes' })
      await dispatch({ type: 'device/get/entity-supplier' })
    }
    await this.load()
  }

  async componentWillReceiveProps(nextProps) {
    const { id } = this.props.params
    const { idNext } = nextProps.params
    console.log({ id, idNext })
    if (id !== idNext) {
      await this.load()
    }
  }

  async load() {
    const { id } = this.props.params
    const { state } = this.props.location
    this.getAsset(id, state && state.asset)
      .then(asset => this.setState({ asset }))
      .catch(error => {
        if (error && error.code) {
          history.replace({ pathname: route_urls.msg, query: { code: error.code }, state: { desc: error.message } })
        }
      })
    this.checkAssetStaff()
    this.alertAcknowledged(this.props.location.query.alertId)
  }

  async getAsset(id, asset) {
    const { multiTenant } = this.props.myself
    if (!asset && util.isSupplier()) {
      asset = await rest.get(`${urls.assets}/${id}`)
    } else if (!fsoUtil.isFSOType(this.props.myself.userAccount.geUserType)) {
      if (multiTenant) {
        if (!asset) {
          asset = await rest.get(`${urls.assets}/${id}`)
        }
        const access = await rest.get(urls.mvsAccessAvailable, { assetUid: asset.uid })
        if (!access) {
          throw { code: 401, message: '当前用户无权查看此设备' }
        }
      } else if (!asset) {
        const query = { id }
        if (util.isIbTenant()) {
          Object.assign(query, { withSubAssets: true })
        }
        const { data: assets } = await rest.list(urls.assets, query)
        if (assets.length < 1) {
          throw { code: 400, message: '无效设备或无权查看' }
        } else {
          asset = assets[0]
        }
      }
    } else if (!asset) {
      asset = await rest.get(`${urls.assets}/${id}`)
    }
    // add lifecycleInYear cause rest service no expose this field
    if (asset && asset.lifecycle) {
      asset.lifecycleInYear = asset.lifecycle / 12
    }
    asset && asset.id && this.showThirdTab(asset)
    return asset
  }

  showThirdTab = async asset => {
    const { pm, repair } = (util.isIbSite() &&
      (await rest.get(`${urls.assetEvents}/getOrderCount?assetUid=${asset.uid}`))) || {
      pm: 0,
      repair: 0
    }
    this.setState({ orderCount: { pm, repair } })

    const opendEvents = pm + repair
    const { groups } = this.state
    groups.events.count = opendEvents
    if (isUsDevice(asset.geDeviceType) && [1, 4, 6].includes(asset.ibStatus) && hasTag(asset.tags, TAG_ICNETER)) {
      groups.us = { label: '使用情况' } // 指定的超声显示使用情况
    } else if (
      (!isUsDevice(asset.geDeviceType) && // 不是超声的情况
        [1, 4, 5, 6, 9].includes(asset.ibStatus) && // ibStatus1, 4, 5, 6以外的不显示
        ![101].includes(asset.geDeviceType)) || // WS不显示
      // && !(asset.geDeviceType >= 200 && asset.geDeviceType < 300)  // LCS不显示
      asset.tubeWatchStatus === '1'
    ) {
      groups.monitor = { label: '健康监控' }
    }
    if (asset.specialAsset) {
      groups.special = { label: '特种设备信息'}
    }
    this.setState({ asset, groups })
  }

  checkAssetStaff() {
    const { myself } = this.props
    const { geSso } = myself.userAccount
    if (
      myself.userRoleList.find(role => role.name === 'AssetStaff' || role.name === 'AssetHead') ||
      myself.multiTenant ||
      util.isSupplier()
    ) {
      if (!geSso && !util.isIbSite()) {
        this.setState({ editable: true })
      }
    }
  }

  async alertAcknowledged(alertId) {
    if (alertId) {
      await rest.leanGet(`${urls.alertAcknowledged}/${alertId}`)
    }
  }

  eventSwitchTab(key) {
    const { routes, params } = this.props
    const path = routes[0].path + '/' + routes[1].path + '/' + key
    history.replace({
      pathname: path.replace(':id', params.id),
      state: this.props.location.state
    })
  }

  render() {
    const { myTenant } = this.props
    const { multiTenant } = this.props.myself
    const { asset, editable, orderCount, ehrStatus } = this.state
    const { initData } = this.props.location.state || {}
    const query = this.props.routes && this.props.routes[2] && this.props.routes[2].path
    const isFso = fsoUtil.isFSOType(this.props.myself.userAccount.geUserType)
    const isFe = fsoUtil.isFEType(this.props.myself.userAccount.geUserType)
    const onAssetChange = newAsset => {
      this.setState({ asset: newAsset })
      // this.props.location.state.asset = newAsset
      history.replace({ ...history.location, state: { asset: newAsset } })
    }
    const onEhrStatusChange = status => {
      this.setState({ ehrStatus: status })
    }

    if (util.isSupplier()) {
      return (
        <div>
          {this.state.asset &&
            React.cloneElement(this.props.children, {
              asset,
              editable,
              initData,
              multiTenant,
              isFso,
              isFe,
              myTenant,
              onAssetChange,
              onEhrStatusChange,
              ...this.props.children.props
            })}
        </div>
      )
    } else
      return (
        <div>
          {!isAppOffline() && (
            <Actions
              isFso={isFso}
              asset={asset}
              editable={editable}
              ehrStatus={ehrStatus}
              onAssetChange={onAssetChange}
              multiTenant={multiTenant}
              orderCount={orderCount}
            />
          )}
          <NavBar groups={filterGroups(this.state.groups)} query={query} onActive={this.eventSwitchTab.bind(this)}>
            {this.state.asset &&
              React.cloneElement(this.props.children, {
                asset,
                editable,
                initData,
                multiTenant,
                isFso,
                isFe,
                myTenant,
                onAssetChange,
                onEhrStatusChange,
                ...this.props.children.props
              })}
          </NavBar>
        </div>
      )
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    myTenant: state.preload.myself.userAccount.tenantInfo
  }
}

function hasTag(tags, keywords) {
  if (Array.isArray(tags)) {
    return tags.some(tag => tag.toLowerCase() === keywords)
  } else return false
}

export default connect(mapStateToProps)(Device)
