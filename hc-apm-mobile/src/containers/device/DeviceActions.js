import React, { Component, useState } from 'react'
import { connect } from 'react-redux'
import { message, But<PERSON>, Modal as AModal } from 'antd'
import { Modal } from 'antd-mobile'
import { browserHistory as history } from 'react-router'
import { urls, rest, route_urls, util, fsoUtil } from '../../constants'
import './device.less'
import { wrapWxLink } from '@/actions/system'
import { isNurse } from '../common/ScanQrCode'
import iconWo from '../../images/assets/报修.svg'
import iconPm from '../../images/assets/保养.svg'
import iconIn from '../../images/assets/盘点.svg'
import { GEDeviceType } from '@/containers/ib/common/AssetHeader'
// import { applyEHR } from '@/containers/ib/common/EhrApply'

@connect(state => ({
  config: state.workflowConfig,
  myself: state.preload.myself,
  tubewatchScope: state.preload.tubewatchScope
}))
class DeviceActions extends Component {
  constructor(props) {
    super(props)
    this.state = {
      ehrAppliedSuccess: false
    }
  }

  render() {
    const { asset, editable, multiTenant, isF<PERSON>, myself, myTenant, config, tubewatchScope, ehrStatus } = this.props
    //ehrStatus  0:不显示 1:显示可申请 2:显示但置灰
    if (asset && asset.isValid) {
      // ultrasound租户 YHAAO-3986
      const isUsTenant =
        myTenant && (util.isIbTenant(myTenant.id) || JSON.parse(myTenant.tenantConfig).edition === 'ultrasound')
      const woDisabled = asset.status === 4 || asset.status === 5
      const pmDisabled =
        isUsTenant || isFso || asset.status === 4 || asset.status === 5 || !editable || asset.isPm === false
      const ivDisabled = asset.isChecked === false
      const isSuper = util.isSuperUser()
      const { result, showDaily, showPower } = isNurse()
      const showNurseButtons = () => {
        if (util.isIbSite()) {
          return false
        } else {
          return result
        }
      }
      if (util.isZTB(asset)) {
        return (
          <div className="device-buttons" style={{ justifyContent: 'left', padding: '13px 20px' }}>
            <IButton
              icon={<img src={iconWo} className={woDisabled ? 'disabled' : ''} />}
              type="ghost"
              disabled={woDisabled}
              onClick={async () => await assetRepair(asset)}
            >
              联系GE HealthCare
            </IButton>
          </div>
        )
      } else if (util.isIbSite()) {
        const isFE = fsoUtil.isFEType(myself.userAccount.geUserType)
        const isTubeWatchScope = fsoUtil.tubewatchScopeType(tubewatchScope, asset)
        const isUS = GEDeviceType.find(v => v.key.includes(asset.geDeviceType))
        return (
          <div className="device-buttons">
            <IButton
              icon={<img src={iconWo} className={woDisabled && !isSuper ? 'disabled' : ''} />}
              type="ghost"
              disabled={woDisabled && !isSuper}
              onClick={async () => await assetRepair(asset)}
            >
              +报修
            </IButton>
            <IButton
              style={{ opacity: isFE || isSuper ? 1 : 0 }}
              icon={<img src={iconPm} className={isFE || isSuper ? '' : 'disabled'} />}
              type="ghost"
              disabled={!isFE && !isSuper}
              onClick={() => history.push({ pathname: route_urls.home_wx + '/report/pm/edit', state: { asset } })}
            >
              {isUS && isUS.value === 'US' ? '+微报' : '+保养报告'}
            </IButton>
            {false && isTubeWatchScope && isFE && (
              <div className={asset && asset.tubeWatchStatus !== '1' ? 'tubewatch' : 'tubewatchDis'}>
                <IButton
                  type="ghost"
                  onClick={() => {
                    if (asset && asset.tubeWatchStatus !== '1') {
                      Modal.alert('警示', '确认是否开启TubeWatch,一旦开启微信端无法关闭', [
                        { text: '取消', onPress: () => console.log('cancel') },
                        {
                          text: '确认',
                          onPress: () => {
                            const changUrl = '/gateway/hcapmassetservice/api/apm/asset/assetinfos/'

                            let params = { tubeWatchStatus: '1' }
                            rest.put(changUrl + asset.id, params, true).then(res => {
                              // util.reloadtubewatch()
                              // message.success(
                              //   '欢迎使用GE Tube Watch服务，您将享受在线球管数字化状态、诊断、预警及更换服务，提高设备使用效率！'
                              // )
                              this.props.onAssetChange(res)
                            })
                          }
                        }
                      ])
                    } else {
                      message.warning('暂不支持手机关闭该功能')
                    }
                  }}
                >
                  {asset && asset.tubeWatchStatus !== '1' ? '开启TubeWatch' : '关闭TubeWatch'}
                </IButton>
              </div>
            )}
            {/* {ehrStatus && (
              <IButton
                type="ghost"
                disabled={ehrStatus == 2 || this.state.ehrAppliedSuccess}
                onClick={() => applyEHR({ ...asset, onClose: () => this.setState({ ehrAppliedSuccess: true }) })}
              >
                申请设备健康检查
              </IButton>
            )} */}
          </div>
        )
      } else {
        const roleList = new Set(myself.userRoleList.map(r => r.name))
        const workflowConfig = config.get('workflowConfig')
        const configItem = config.get('configItem')
        const showBtnByFunc = (functions) => functions && functions.split(',').some(func => util.hasFunction(func))
        const showSeconded = () =>
          workflowConfig &&
          workflowConfig.enableSiteToSiteAllocation &&
          util.hasFunction('ADJUST_DEPTMENT_MOBILE_BUTTON') 
        const powerCtrlLabel = util.isNursePower() ? '开关机登记' : '使用登记'
        const showTransfer = () => util.hasFunction('ASSET_BATCH_MODIFY_MOBILE_TRANSFER_DEPARTMENT_BUTTON')
        const showScrap = () => (configItem && configItem.assetScrap && showBtnByFunc('ASSET_MOBILE_ASSET_SCRAP_CLINICAL_BUTTON,ASSET_MOBILE_ASSET_SCRAP_ASSET_BUTTON') ? true : false)
        const showInspection = () => asset.measureGrade !== 4 && util.hasFunction('MEASURE_MOBILE_ADD_BUTTON')
        return (
          <React.Fragment>
            <div className="device-buttons2">
              <Button shape="round" disabled={woDisabled} onClick={async () => await assetRepair(asset, this.props)}>
                <IconLabel icon={iconWo} label="报修" className={woDisabled ? 'disabled' : ''} />
              </Button>
              <Button
                shape="round"
                disabled={pmDisabled}
                onClick={() => history.push(route_urls.home_wx + '/createPmOrder/' + asset.id)}
              >
                <IconLabel icon={iconPm} label="保养" className={pmDisabled ? 'disabled' : ''} />
              </Button>
              {showBtnByFunc('ASSET_INVENTORY_MOBILE_BUTTON') && <Button shape="round" disabled={ivDisabled} onClick={() => inventoryAsset(asset)}>
                <IconLabel icon={iconIn} label="盘点" className={ivDisabled ? 'disabled' : ''} />
              </Button>}
              {showBtnByFunc('NURSESITE_USING_MOBILE_RECORD_EDIT') && (
                <Button
                  shape="round"
                  icon="control"
                  onClick={() => {
                    history.push({ pathname: route_urls.wx.nursePowerCtrl + '/' + asset.uid })
                  }}
                >
                  使用登记
                </Button>
              )}
              {showBtnByFunc('NURSE_OPERATE_MOBILE_RECORD_EDIT') && (
                <Button
                  shape="round"
                  icon="control"
                  onClick={() => {
                    history.push({ pathname: route_urls.wx.nursePowerCtrl + '/' + asset.uid })
                  }}
                >
                  开关机登记
                </Button>
              )}
              {showBtnByFunc('NURSESITE_MOBILE_BUTTON') && (
                <Button
                  shape="round"
                  icon="schedule"
                  onClick={() =>
                    history.push({
                      pathname: route_urls.wx.nurse,
                      query: { assetUid: asset.uid, group: 'todayTimeout' }
                    })
                  }
                >
                  日常检查
                </Button>
              )}
              {showSeconded() && (
                <Button
                  shape="round"
                  icon="interaction"
                  onClick={() =>
                    history.push({ pathname: route_urls.wx.secondedDetail + '/' + asset.uid, state: { asset } })
                  }
                >
                  科室借调
                </Button>
              )}
              {showTransfer() && (
                <Button
                  shape="round"
                  icon="export"
                  onClick={async () =>{
                    if (await noUnfinishedRecords(asset, { "operationType": "transfer" })) {
                      history.push({
                        pathname: route_urls.home_wx + '/device/transfer',
                        state: { asset }
                      })
                    }
                  }}
                >
                  转科申请
                </Button>
              )}
              {showScrap() && (
                <Button
                  shape="round"
                  icon="api"
                  onClick={async () => {
                    if (await noUnfinishedRecords(asset)) {
                      history.push({
                        pathname: route_urls.home_wx + '/device/retire',
                        state: { asset }
                      })
                    }
                  }}
                >
                  报废申请
                </Button>
              )}
              {showInspection() && <Button
                shape="round"
                icon="experiment"
                onClick={() =>
                  history.push({
                    pathname: route_urls.home_wx + '/inspection/create/' + asset.uid,
                    state: { asset }
                  })
                }
              >
                计量新增
              </Button>}
              {showBtnByFunc('ADVERSE_MOBILE_REPORTER_BUTTON') && (
                <Button
                  shape="round"
                  icon="medicine-box"
                  onClick={() =>
                    history.push({
                      pathname: '/wx/adverseEvents/detail',
                      query: { eventsState: 'addReport', role: 'reporter' },
                      state: { asset }
                    })
                  }
                >
                  医疗器械不良事件上报
                </Button>
              )}
            </div>
          </React.Fragment>
        )
      }
    } else return null
  }
}

const noUnfinishedRecords = async (asset, data) => {
  return new Promise(async (resolve, reject) => {
    const removeModalContainers = () => {
      const modalContainers = document.querySelectorAll('[id^="am-modal-container"]');
      modalContainers.forEach(container => container.remove());
    };
    const checkRes = await rest.get(`${urls.assetScrap}/checkAssetOrder/${asset.id}`, null, true)
    const handleIncompleteOrders = async () => {
      const res = await rest.listPost(`${urls.assetScrap}/processUnfinishedNurseSiteMaintain/${asset.id}`, data)
      if (res && res.bizStatusCode === 'OK') {
        message.success('处理完成')
        if (!checkRes.workOrderList && !checkRes.pmOrderList) {
          setTimeout(() => {
            resolve(true);
            removeModalContainers()
          }, 1000)
        } else {
          reject(false);
        }
      }
    }
    if (checkRes && (checkRes.nurseSiteMaintainIsComplete || checkRes.workOrderList || checkRes.pmOrderList)) {
      Modal.alert('当前设备有未完成工单，请进行处理', (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
          {checkRes.nurseSiteMaintainIsComplete && <Button type="primary" onClick={handleIncompleteOrders}>自动处理未完成临床助手工单</Button>}
          {Array.isArray(checkRes.workOrderList) && <div>未完成维修工单编号: {checkRes.workOrderList.join(', ')}</div>}
          {Array.isArray(checkRes.pmOrderList) && <div>未完成保养工单编号: {checkRes.pmOrderList.join(', ')}</div>}
        </div>
      ), [
        { text: '关闭', onPress: () => reject(false) }
      ])
    } else {
      resolve(true);
    }
  });
}

const IconLabel = props => {
  return (
    <React.Fragment>
      <img src={props.icon} style={{ width: '17px', verticalAlign: 'sub' }} className={props.className} />
      <span style={{ paddingLeft: '8px' }}>{props.label}</span>
    </React.Fragment>
  )
}

const IButton = props => (
  <div {...props} className={props.disabled ? 'disabled' : ''} onClick={e => !props.disabled && props.onClick(e)}>
    {props.icon}
    {props.children}
  </div>
)

function hasWxIvCard(myself) {
  if (myself.customRoles && myself.customRoles.length > 0) {
    let wxCards = []
    myself.customRoles.forEach(role => {
      wxCards = wxCards.concat(role.wxCards)
    })
    return wxCards.some(card => card === 'mgr_inv')
  } else {
    return false
  }
}

const assetRepair = async (asset, props) => {
  let res = false
  if (util.isIbSite() && asset.systemId) {
    res = await getAssetConnInfo(asset.systemId)
  }
  if (!res) {
    const isExtRepair = await util.assetExternalRepair(asset)
    if (!isExtRepair) {
      const retUser = await rest.get(urls.telReport, { assetId: asset.id })
      if (retUser && retUser.id) {
        history.push(route_urls.wx.telReport + '?userName=' + retUser.name + '&tel=' + retUser.telephone)
        return
      }

      if (!props) {
        history.push(route_urls.wx.createWo + '?id=' + asset.id)
      } else {
        const roleList = new Set(props.myself.userRoleList.map(r => r.name))
        if (roleList.has('AssetStaff') || roleList.has('AssetHead')) {
          history.push(route_urls.wx.createWo + '?id=' + asset.id)
        } else {
          if (props.orderCount.repair === 0) {
            history.push(route_urls.wx.createWo + '?id=' + asset.id)
          } else {
            history.push({ pathname: route_urls.wx.srScanList, state: { asset, userId: props.myself.userAccount.id } })
          }
        }
      }
    }
  }
}

const inventoryAsset = asset => {
  if (asset.qrCode) {
    history.push(
      route_urls.wx.inventory + '/scan?qrCode=' + asset.qrCode + '&showMode=false&id=' + asset.id + '&from=device'
    )
  } else if (asset.departNum) {
    history.push(
      route_urls.wx.inventory + '/scan?departNum=' + asset.departNum + '&showMode=false&id=' + asset.id + '&from=device'
    )
  } else {
    message.warn('设备二维码或设备编号为空, 无法盘点!')
  }
}

export const getAssetConnInfo = async systemId => {
  const res = await rest.get(urls.assetConnInfo, { systemId }, true)
  if (res && Array.isArray(res)) {
    if (util.isIbSite()) {
      const acct = res.find(item => item.envType === 'ACCT')
      if (acct && acct.assetInfo && acct.assetInfo.qrCode) {
        window.location.href = wrapWxLink(`${acct.url}/wx/scanqrcode?qrCode=${acct.assetInfo.qrCode}`)
        return true
      }
    } else {
      const ib = res.find(item => item.envType === 'IB')
      if (ib && ib.assetInfo && ib.ucmidValidByUser === false) {
        throw { code: 401, message: '您没有相关权限，请联系设备管理员' }
      }
    }
  }
  return false
}

export default DeviceActions
