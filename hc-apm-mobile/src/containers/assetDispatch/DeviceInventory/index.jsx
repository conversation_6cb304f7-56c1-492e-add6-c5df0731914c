import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, List } from 'antd-mobile'
import NavBar from '../common/NavBar'

const INVENTORY_TYPES = [
  { title: '正常可借用', type: 1 },
  { title: '正常已借出', type: 2 },
  { title: '故障不可借', type: 3 },
]

class DeviceInventory extends Component {
  state = { tab: 1 }
  componentDidMount() {
    this.props.getDeviceInventory(INVENTORY_TYPES[0].type)
  }

  onTabClick = (tab) => {
    this.setState({ tab: tab.type })
    this.props.getDeviceInventory(tab.type)
  }

  goAssetList = (device) => {
    this.props.router.push({
      pathname: '/wx/device',
      query: { sharedAssetType: device.sharedAssetType, borrowStatus: this.state.tab }
    })
  }

  render() {
    return (
      <div>
        <NavBar title="设备库存统计"/>
        <Tabs tabs={INVENTORY_TYPES} onTabClick={this.onTabClick}>
          <div>
            <br />
            <List>

              { this.props.devices
                  .map((d, index) => <List.Item key={index} onClick={() => this.goAssetList(d)}>{d.name}, 共{d.count}台</List.Item>)
              }
            </List>
          </div>
        </Tabs>
      </div>
    )
  }
}

export default connect(
  state => ({
    devices: state.allocationStats.devices
  }),
  dispatch => ({
    getDeviceInventory: (type) => dispatch({ type: 'allocationStats/get/devices-stats', payload: type })
  }),
)(DeviceInventory)
