import React, { useState, useEffect, useContext, useMemo, useCallback } from "react";
import { Table, Button, message, Modal, Popconfirm, Space, Badge } from "antd";
import { getVenderList, saveVender, deleteVender } from "@/service";
import { MyForm } from "@/components/MyForm";
import { venderContext } from "../context/index";
import "./index.less";

const PAGE_SIZE = 10;
const MODAL_WIDTH = 500;
function UserManage() {
  const ButtonGroup = Button.Group;
  const { model } = useContext(venderContext);
  const [inviteModal, setInviteModal] = useState(false);
  const inviteLink = useMemo(
    () => `${window.location.origin}/reports/venderLogin?venderCompanyId=${model.venderCompanyId}`,
    [model.venderCompanyId],
  );
  const handleCopyLink = useCallback(() => {
    navigator.clipboard
      .writeText(inviteLink)
      .then(() => message.success("链接已复制到剪贴板"))
      .catch(() => message.error("复制失败，请手动复制"));
  }, [inviteLink]);
  const { columns, userList, setPage, userTotal, formOptionObj, editStatus, setEditStatus, userData, saveItemData } =
    useUserManage();

  return (
    <div className="user-manage">
      {userList && (
        <Table
          rowKey={record => {
            return record.id;
          }}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            position: ["bottomRight"],
            size: "small",
            pageSize: PAGE_SIZE,
            total: userTotal,
            onChange: (current, size) => {
              setPage(current - 1);
            },
          }}
          title={() => {
            return (
              <div className="table-title">
                用户列表
                {model.isAdmin && (
                  <Button
                    onClick={() => {
                      setInviteModal(true);
                    }}
                    type="primary"
                  >
                    邀请用户
                  </Button>
                )}
              </div>
            );
          }}
          columns={columns}
          dataSource={userList}
        />
      )}
      <Modal
        closable={false}
        centered
        title={"编辑用户信息"}
        open={editStatus}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("user-manage")[0]}
        width={MODAL_WIDTH}
        onCancel={() => {
          setEditStatus(false);
        }}
      >
        <MyForm
          data={{
            ...userData,
          }}
          onSubmitText="保存"
          options={formOptionObj}
          oncancel={() => {
            setEditStatus(false);
          }}
          onSubmit={data => {
            saveItemData(data);
          }}
        ></MyForm>
      </Modal>

      <Modal
        className="invite-modal"
        title={"邀请注册链接"}
        open={inviteModal}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("user-manage")[0]}
        width={MODAL_WIDTH}
        onCancel={() => {
          setInviteModal(false);
        }}
      >
        <span className="url-wrap">{`${window.location.origin}/reports/venderLogin?venderCompanyId=${model.venderCompanyId}`}</span>
        <ButtonGroup>
          <Button type="primary" onClick={handleCopyLink}>
            复制链接
          </Button>
        </ButtonGroup>
      </Modal>
    </div>
  );
}

const useUserManage = () => {
  const { model, setUserInfo } = useContext(venderContext);
  const [userList, setUserList] = useState([]);
  const [page, setPage] = useState(0);
  const [userTotal, setUserTotal] = useState(0);
  const [userData, setUserData] = useState(null);
  const [editStatus, setEditStatus] = useState(false);

  const isSelf = useCallback(
    id => {
      return model.id === id;
    },
    [model],
  );

  const columns = [
    {
      title: "姓名",
      dataIndex: "venderName",
      key: "venderName",
      render: (value, item) => {
        return isSelf(item.id) ? (
          <Space>
            <Badge dot color="#6022a6"></Badge>
            <span>{value}</span>
          </Space>
        ) : (
          <span>{value}</span>
        );
      },
    },
    {
      title: "电话",
      dataIndex: "venderPhone",
      key: "venderPhone",
    },
    // {
    //     title: "邮箱",
    //     dataIndex: "reportNum",
    //     key: "reportNum",
    // },
    {
      title: "角色",
      dataIndex: "isAdmin",
      key: "isAdmin",
      render: value => {
        return <span>{value ? "管理员" : "普通用户"}</span>;
      },
    },
    {
      title: "操作",
      dataIndex: "edit",
      key: "edit",
      render: (_, item) => {
        return (
          <>
            {(model.isAdmin || isSelf(item.id)) && (
              <a
                onClick={() => {
                  editUserInfo(item);
                }}
              >
                编辑
              </a>
            )}{" "}
            {model.isAdmin && (
              <>
                /{" "}
                <Popconfirm
                  title={`确认删除用户 ${item.venderName}`}
                  onConfirm={() => {
                    deleteItem(item);
                  }}
                  onCancel={() => {
                    console.log("onCancel");
                  }}
                  okText="确认删除"
                  cancelText="取消"
                >
                  <a>删除</a>
                </Popconfirm>
              </>
            )}
          </>
        );
      },
    },
  ];

  const formOptionObj = [
    {
      type: "input",
      name: "venderName",
      attribute: {
        label: { value: "用户名", required: true },
        rule: {
          required: "请输入用户名",
        },
      },
    },
    {
      type: "input",
      name: "venderPhone",
      attribute: {
        label: { value: "联系电话", required: true },
        placeholder: "请填写联系电话",
        rule: {
          required: "请填写联系电话",
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
      },
    },
    // {
    //     type: "input",
    //     name: "email",
    //     attribute: {
    //         label: { value: "邮箱", required: true },
    //         placeholder: "请填写邮箱",
    //         rule: {
    //             required: "邮箱不能为空",
    //             pattern: {
    //                 value: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
    //                 message: "请正确填写邮箱",
    //             },
    //         },
    //     },
    // },
    {
      type: "select",
      name: "isAdmin",
      attribute: {
        label: { value: "角色", required: true },
        placeholder: "选择用户角色",
        disabled: !model.isAdmin,
        options: [
          {
            label: "管理员",
            value: true,
          },
          {
            label: "普通用户",
            value: false,
          },
        ],
      },
    },
  ];

  const getUserList = useCallback(async () => {
    try {
      setUserInfo({ webLoading: true });
      const { content, totalElements } = await getVenderList({
        venderCompanyId: model.venderCompanyId,
        page,
        pageSize: PAGE_SIZE,
      });
      setUserList(content || []);
      setUserTotal(totalElements || 0);
    } catch (error) {
      console.error("Failed to fetch getUserList", error);
      setUserList([]);
      setUserTotal(0);
    } finally {
      setUserInfo({ webLoading: false });
    }
  }, [page, model.venderCompanyId]);

  const editUserInfo = data => {
    setUserData(data);
    setEditStatus(true);
  };

  const saveItemData = useCallback(
    async data => {
      try {
        setUserInfo({ webLoading: true });
        const savestatus = await saveVender(data);
        if (savestatus.id) {
          message.success("保存成功");
          setEditStatus(false);
          await getUserList();
        } else {
          message.error(savestatus.message || "保存失败");
        }
      } catch (error) {
        message.error(error.message || "保存过程中发生错误");
      } finally {
        setUserInfo({ webLoading: false });
      }
    },
    [getUserList],
  );
  const deleteItem = async data => {
    try {
      setUserInfo({ webLoading: true });
      const deleteStatus = await deleteVender(data.id);
      console.log("deleteStatus", deleteStatus);
      if (deleteStatus && deleteStatus.bizStatusCode == "OK") {
        message.success("删除成功");
        await getUserList(model.venderCompanyId);
      } else {
        message.error("删除失败，不能删除管理员！");
      }
    } catch (error) {
      message.error("删除失败: " + error.message);
    } finally {
      setUserInfo({ webLoading: false });
    }
  };

  useEffect(() => {
    if (model.venderCompanyId) {
      getUserList();
    }
  }, [getUserList, model.venderCompanyId]);

  return {
    columns,
    userList,
    setPage,
    userTotal,
    formOptionObj,
    editStatus,
    setEditStatus,
    userData,
    saveItemData,
    deleteItem,
  };
};
export default UserManage;
