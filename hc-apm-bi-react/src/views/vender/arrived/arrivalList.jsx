import React, { useState, useEffect, useContext, useRef } from "react";
import { Table, Button, message, Upload, Modal, Select, Popconfirm, Divider } from "antd";
import {
  getByKeyAndSite,
  getArrivalList,
  getArrivalListItems,
  getArrivalContractLis,
  getArrivalContractItemList,
  getVenderDeptList,
  saveArrival,
  deleteArrival,
  getI8nMessages,
  getSiteMap,
  getStockedDept,
} from "@/service";
import rest from "@urls/axios";
import { venderContext } from "../context/index";
import { MyForm } from "@/components/MyForm";

import "./index.less";

function ArrivalList(props) {
  const ARR = ["asset", "asset2", "asset3", "asset4"];
  const { model, setUserInfo } = useContext(venderContext);
  const [arrivalList, setArrivalList] = useState([]); //到货清单列表
  const [page, setPage] = useState(0);
  const [arrivalListTotal, setArrivalListTotal] = useState(0);
  const [i8nData, setI8nData] = useState(null);
  const [siteMapData, setSiteMapData] = useState(null); //院区树
  //
  const editType = useRef(null);
  const [editState, setEditState] = useState(false);
  const [arrivalListItem, setArrivalListItem] = useState(); //清单item详情
  const assetData = useRef(null); //清单详情设备选项
  const [deptListData, setDeptListData] = useState(null); //清单详情使用科室选项
  const [deptItem, setDeptItem] = useState(null); //默认科室
  const [formConfig, setFormConfig] = useState(null); //清单item详情form表单配置
  //到货清单合同数据
  const [showContractInfo, setShowContractInfo] = useState(false);
  const [contractList, setContractList] = useState([]);
  const [contractNum, setContractNum] = useState(null);
  const [contractItemList, setContractItemList] = useState([]);
  const [contractItem, setContractItem] = useState(null);

  // 添加一个请求锁，防止短时间内重复请求
  const requestLockRef = React.useRef(false);

  const msgTypes = ["assetUsage", "assetGroup", "assetValueCategory"].join(",");

  //form表单配置
  const formOptionObj = [
    {
      type: "input",
      name: "contractNum",
      attribute: {
        placeholder: "请填写合同编号",
        rule: {
          required: "合同编号不能为空",
          maxLength: {
            value: 20,
            message: "合同编号长度不能超过20个字符",
          },
        },
        label: { value: "合同编号", required: true },
      },
    },
    {
      type: "input",
      name: "agent",
      attribute: {
        placeholder: "请填写院方经办人",
        rule: {
          maxLength: {
            value: 20,
            message: "院方经办人长度不能超过20个字符",
          },
        },
        label: { value: "院方经办人" },
      },
    },
    {
      type: "DatePicker",
      name: "boxInstallationDate",
      attribute: {
        allowClear: false,
        label: { value: "开箱安装日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "DatePicker",
      name: "usingDate",
      attribute: {
        allowClear: false,
        label: { value: "使用日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "input",
      name: "contactPerson",
      attribute: {
        placeholder: "请填写供应商联系人",
        rule: {
          maxLength: {
            value: 20,
            message: "供应商联系人长度不能超过20个字符",
          },
        },
        label: { value: "供应商联系人" },
      },
    },
    {
      type: "input",
      name: "contactPersonTel",
      attribute: {
        placeholder: "请填写供应商联系电话",
        rule: {
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
        label: { value: "供应商联系电话" },
      },
    },
    {
      type: "input",
      name: "repairer",
      attribute: {
        placeholder: "请填写维修联系人",
        rule: {
          maxLength: {
            value: 20,
            message: "维修联系人长度不能超过20个字符",
          },
        },
        label: { value: "维修联系人" },
      },
    },
    {
      type: "input",
      name: "repairerTel",
      attribute: {
        placeholder: "请填写维修联系人电话",
        rule: {
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
        label: { value: "维修联系人电话" },
      },
    },
    {
      type: "input",
      name: "invoiceNum",
      attribute: {
        placeholder: "请填写发票号",
        rule: {
          maxLength: {
            value: 50,
            message: "发票号长度不能超过50个字符",
          },
        },
        label: { value: "发票号" },
      },
    },
    {
      type: "DatePicker",
      name: "invoiceDate",
      attribute: {
        allowClear: false,
        label: { value: "发票日期" },
        format: "YYYY-MM-DD",
      },
    },

    {
      type: "Upload",
      name: "asset",
      attribute: {
        label: {
          value: "照片（出厂编号、设备整体外观、医院编号标签）",
        },
        progress: { strokeWidth: 0.1, showInfo: false },
        showUploadList: true,
        maxCount: 5,
        customRequest: () => {},
        beforeUpload: file => {
          let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
          const doesMatchFileType =
            ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
          if (!doesMatchFileType) {
            message.warning(
              `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
              4,
            );
            return Upload.LIST_IGNORE;
          }
          const isLimitedSize = file.size / 1024 / 1024 < 50;
          if (!isLimitedSize) {
            message.warning("文件必须小于 50MB!");
            return Upload.LIST_IGNORE;
          }
        },
      },
    },
    {
      type: "Upload",
      name: "asset4",
      attribute: {
        label: {
          value: "临床验收确认（确认配置清单）",
        },
        progress: { strokeWidth: 0.1, showInfo: false },
        showUploadList: true,
        maxCount: 5,
        customRequest: () => {},
        beforeUpload: file => {
          let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
          const doesMatchFileType =
            ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
          if (!doesMatchFileType) {
            message.warning(
              `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
              4,
            );
            return Upload.LIST_IGNORE;
          }
          const isLimitedSize = file.size / 1024 / 1024 < 50;
          if (!isLimitedSize) {
            message.warning("文件必须小于 50MB!");
            return Upload.LIST_IGNORE;
          }
        },
      },
    },
    {
      type: "Upload",
      name: "asset2",
      attribute: {
        label: {
          value: "用户手册",
        },
        progress: { strokeWidth: 0.1, showInfo: false },
        showUploadList: true,
        maxCount: 5,
        customRequest: () => {},
        beforeUpload: file => {
          let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
          const doesMatchFileType =
            ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
          if (!doesMatchFileType) {
            message.warning(
              `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
              4,
            );
            return Upload.LIST_IGNORE;
          }
          const isLimitedSize = file.size / 1024 / 1024 < 50;
          if (!isLimitedSize) {
            message.warning("文件必须小于 50MB!");
            return Upload.LIST_IGNORE;
          }
        },
      },
    },
    {
      type: "Upload",
      name: "asset3",
      attribute: {
        label: {
          value: "培训资料",
        },
        progress: { strokeWidth: 0.1, showInfo: false },
        showUploadList: true,
        maxCount: 5,
        customRequest: () => {},
        beforeUpload: file => {
          let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
          const doesMatchFileType =
            ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
          if (!doesMatchFileType) {
            message.warning(
              `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
              4,
            );
            return Upload.LIST_IGNORE;
          }
          const isLimitedSize = file.size / 1024 / 1024 < 50;
          if (!isLimitedSize) {
            message.warning("文件必须小于 50MB!");
            return Upload.LIST_IGNORE;
          }
        },
      },
    },

    {
      type: "select",
      name: "departNum",
      attribute: {
        label: { value: "设备", required: true },
        placeholder: "请选择设备",
        rule: {
          required: "请选择设备",
        },
        onChange: (data, formData) => {
          updateItemdata(data, formData);
        },
      },
    },
    {
      type: "inputNumber",
      name: "lifecycleInYear",
      attribute: {
        label: { value: "使用年限" },
        placeholder: "请填写使用年限",
      },
      dynamic: { filter: "departNum", value: "" },
    },
    {
      type: "treeSelect",
      name: "siteUID",
      attribute: {
        label: { value: "所属院区", required: true },
        placeholder: "所属院区",
        rule: {
          required: "请选择院区",
        },
        allowClear: true,
        treeDefaultExpandAll: true,
        onChange: (siteID, formData) => {
          updateFormConfig(siteID, formData);
        },
      },
    },
    {
      type: "select",
      name: "clinicalDeptUID",
      attribute: {
        label: { value: "所属科室" },
        placeholder: "所属科室",
        showSearch: true,
        allowClear: true,
        filterOption: (input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase()),
      },
    },
    {
      type: "input",
      name: "usingPerson",
      attribute: {
        placeholder: "请填写使用人名称",
        rule: {
          maxLength: {
            value: 20,
            message: "使用人名称长度不能超过20个字符",
          },
        },
        label: { value: "使用人名称" },
      },
    },
    {
      type: "input",
      name: "usingPersonTel",
      attribute: {
        placeholder: "请填写使用人电话",
        rule: {
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
        label: { value: "使用人电话" },
      },
    },
    {
      type: "input",
      name: "floorName",
      attribute: {
        placeholder: "请填写安装地点",
        rule: {
          maxLength: {
            value: 20,
            message: "安装地点长度不能超过20个字符",
          },
        },
        label: { value: "安装地点" },
      },
    },
    {
      type: "input",
      name: "locationName",
      attribute: {
        placeholder: "请填写详细地址",
        rule: {
          maxLength: {
            value: 50,
            message: "详细地址长度不能超过50个字符",
          },
        },
        label: { value: "详细地址" },
      },
    },
    {
      type: "input",
      name: "serialNum",
      attribute: {
        placeholder: "请填写设备SN号",
        rule: {
          maxLength: {
            value: 20,
            message: "设备SN号长度不能超过20个字符",
          },
        },
        label: { value: "设备SN号" },
      },
    },
    {
      type: "inputNumber",
      name: "annualMaintenanceTimes",
      attribute: {
        placeholder: "请填写年保养次数",
        label: { value: "年保养次数" },
        min: 0,
      },
    },
    {
      type: "DatePicker",
      name: "manufactDate",
      attribute: {
        allowClear: false,
        label: { value: "出厂日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "select",
      name: "measureDevice",
      attribute: {
        label: { value: "是否是计量设备" },
        options: [
          { label: "是", value: 1 },
          { label: "否", value: 0 },
        ],
        placeholder: "选择是否是计量设备",
      },
    },
    {
      type: "inputNumber",
      name: "meteringCycle",
      attribute: {
        placeholder: "请填写计量周期（月）",
        label: { value: "计量周期（月）" },
        min: 0,
      },
      dynamic: { filter: "measureDevice", value: 0 },
    },
    {
      type: "select",
      name: "hasRegistrationNo",
      attribute: {
        label: { value: "是否有注册证" },
        options: [
          { label: "有", value: 1 },
          { label: "无", value: 0 },
        ],
        placeholder: "选择是否有注册证",
      },
    },
    {
      type: "input",
      name: "registrationNo",
      attribute: {
        placeholder: "请填写注册证号",
        rule: {
          maxLength: {
            value: 20,
            message: "注册证号长度不能超过20个字符",
          },
        },
        label: { value: "注册证号" },
      },
      dynamic: { filter: "hasRegistrationNo", value: 0 },
    },
    {
      type: "inputNumber",
      name: "workHours",
      attribute: {
        placeholder: "请填写安装工时",
        label: { value: "安装工时" },
        min: 0,
      },
    },
  ];
  const newArrivalFormOption = [
    {
      type: "input",
      name: "contractNum",
      attribute: {
        placeholder: "请填写合同编号",
        rule: {
          required: "合同编号不能为空",
          maxLength: {
            value: 20,
            message: "合同编号长度不能超过20个字符",
          },
        },
        label: { value: "合同编号", required: true },
      },
    },
    {
      type: "treeSelect",
      name: "siteUID",
      attribute: {
        label: { value: "所属院区", required: true },
        placeholder: "所属院区",
        disabled: true,
        rule: {
          required: "请选择院区",
        },
        allowClear: true,
        treeDefaultExpandAll: true,
        onChange: (siteID, formData) => {
          updateFormConfig(siteID, formData);
        },
      },
    },
    // {
    //   type: "select",
    //   name: "clinicalDeptUID",
    //   attribute: {
    //     placeholder: "请填写所属科室",
    //     label: { value: "所属科室", required: true },
    //     showSearch: true,
    //     allowClear: true,
    //     filterOption: (input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase()),
    //     rule: {
    //       required: "请选择所属科室",
    //     },
    //   },
    // },
    {
      type: "input",
      name: "assetName",
      attribute: {
        placeholder: "请填写资产名称",
        rule: {
          required: "资产名称不能为空",
        },
        label: { value: "资产名称", required: true },
      },
    },
    {
      type: "inputNumber",
      name: "quantity",
      attribute: {
        placeholder: "请填写数量",
        rule: {
          required: "数量不能为空",
        },
        min: 0,
        label: { value: "数量", required: true },
      },
    },

    {
      type: "input",
      name: "agent",
      attribute: {
        placeholder: "请填写院方经办人",
        label: { value: "院方经办人" },
      },
    },
    {
      type: "select",
      name: "processType",
      attribute: {
        label: { value: "安装流程类型", required: true },
        options: [
          {
            value: "Easy",
            label: "简易",
          },
          {
            value: "Normal",
            label: "正常",
          },
        ],
        placeholder: "选择安装流程类型",
        rule: {
          required: "选择安装流程类型",
        },
      },
    },
    {
      type: "select",
      name: "assetGroup",
      attribute: {
        label: { value: "设备分类" },
        options: [],
        placeholder: "选择设备分类",
      },
    },
    {
      type: "DatePicker",
      name: "purchaseDate",
      attribute: {
        allowClear: false,
        label: { value: "采购日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "DatePicker",
      name: "boxInstallationDate",
      attribute: {
        allowClear: false,
        label: { value: "开箱安装日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "DatePicker",
      name: "usingDate",
      attribute: {
        allowClear: false,
        label: { value: "使用日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "input",
      name: "contactPerson",
      attribute: {
        placeholder: "请填写供应商联系人",
        rule: {
          maxLength: {
            value: 20,
            message: "供应商联系人长度不能超过20个字符",
          },
        },
        label: { value: "供应商联系人" },
      },
    },
    {
      type: "input",
      name: "contactPersonTel",
      attribute: {
        placeholder: "请填写供应商联系电话",
        rule: {
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
        label: { value: "供应商联系电话" },
      },
    },
    {
      type: "input",
      name: "repairer",
      attribute: {
        placeholder: "请填写维修联系人",
        rule: {
          maxLength: {
            value: 20,
            message: "维修联系人长度不能超过20个字符",
          },
        },
        label: { value: "维修联系人" },
      },
    },
    {
      type: "input",
      name: "repairerTel",
      attribute: {
        placeholder: "请填写维修联系人电话",
        rule: {
          pattern: {
            value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
            message: "请正确填写联系电话",
          },
        },
        label: { value: "维修联系人电话" },
      },
    },
    {
      type: "input",
      name: "invoiceNum",
      attribute: {
        placeholder: "请填写发票号",
        rule: {
          maxLength: {
            value: 50,
            message: "发票号长度不能超过50个字符",
          },
        },
        label: { value: "发票号" },
      },
    },
    {
      type: "DatePicker",
      name: "invoiceDate",
      attribute: {
        allowClear: false,
        label: { value: "发票日期" },
        format: "YYYY-MM-DD",
      },
    },
  ];

  // 上传附件服务
  const updateFiles = async filesArray => {
    if (!filesArray) {
      return null;
    }
    // 过滤本地需要上传服务器的文件
    let filterArray = filesArray.reduce((result, currentValue) => {
      if (currentValue.size) {
        result.push(currentValue);
      }
      return result;
    }, []);

    return Promise.all(
      filterArray.map(item => {
        return new Promise((resolve, reject) => {
          rest
            .file(item)
            .then(data => {
              const attachment = {
                name: data.objectName,
                objectStorageId: data.objectId,
                thumbnail: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                original: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                ...data,
              };
              resolve(attachment);
            })
            .catch(() => {
              reject("上传失败");
            });
        });
      }),
    );
  };

  //获取到货清单
  const getArrival = async tenantId => {
    setUserInfo({ webLoading: true });
    let arrivalData = await getArrivalList({
      status: -12,
      tenantUid: tenantId,
      page,
      pageSize: 10,
    });
    if (arrivalData.bizStatusCode == "OK" && arrivalData.data) {
      setArrivalList(arrivalData.data);
      setArrivalListTotal(arrivalData.rowCount);
    }
    setUserInfo({ webLoading: false });
  };

  //form表单下拉选择设备时更新设备id对应清单详情数据
  const updateItemdata = async (id, formData) => {
    const findCurrentAsset = assetData.current.filter(item => item.id == id);
    console.log("findCurrentAsset", findCurrentAsset);
    const {
      siteUID,
      clinicalDeptUID,
      usingPerson,
      usingPersonTel,
      lifecycleInYear,
      floorName,
      locationName,
      serialNum,
      annualMaintenanceTimes,
      manufactDate,
      measureDevice,
      meteringCycle,
      hasRegistrationNo,
      registrationNo,
      workHours,
    } = findCurrentAsset[0];
    if (siteUID) {
      //获取科室信息
      const itemDeptListData = await getVenderDeptList({ siteUid: siteUID });
      //组装科室select option
      itemDeptListData.map(item => {
        item.label = item.name;
        item.value = item.uid;
      });
      let formOptions;
      if (editType.current.type == "add") {
        //修改 formOptionObj 设备类型配置
        formOptions = newArrivalFormOption;
      } else {
        formOptions = formOptionObj;
      }
      formOptions.map(item => {
        switch (item.name) {
          case "clinicalDeptUID":
            item["attribute"]["options"] = itemDeptListData;
            break;
          default:
            break;
        }
      });
      setFormConfig(formOptions);
    }

    //update
    setArrivalListItem(currentValue => {
      return {
        ...formData,
        departNum: id,
        siteUID,
        clinicalDeptUID,
        usingPerson,
        usingPersonTel,
        lifecycleInYear,
        floorName,
        locationName,
        serialNum,
        annualMaintenanceTimes,
        manufactDate,
        measureDevice,
        meteringCycle,
        hasRegistrationNo,
        registrationNo,
        workHours,
      };
    });
  };
  //编辑到货清单信息
  const editArrival = async data => {
    setShowContractInfo(false);
    editType.current = {
      type: data.type,
      title: data.title,
    };
    setEditState(true);
    if (data.type == "edit") {
      const dataAttachments = data.data.attachments;
      ARR.forEach(i => (data.data[i] = dataAttachments.filter(j => j.boSubType === i)));
    }

    // 获取自定义图片title
    const photoCaption = await getByKeyAndSite({
      key: "arrivalListPhotoCaption",
      siteUid: model.hospital,
    });
    const checkCaption = await getByKeyAndSite({
      key: "arrivalListCheckCaption",
      siteUid: model.hospital,
    });
    //获取设备
    const itemAssetData = await getArrivalListItems({
      arrivalListId: data.data.id,
      page: 0,
      pageSize: 10,
      tenantUid: model.hospital,
    });
    //获取院区信息
    const siteMapDatas = siteMapData
      ? siteMapData
      : convertLabelToTitle(await getSiteMap({ tenantUid: model.hospital }));
    setSiteMapData(siteMapDatas);

    //组装设备select option
    itemAssetData.map((item, index) => {
      item.label = `${item.arrivalList.assetName}-${index + 1}`;
      item.value = item.id;
    });
    assetData.current = itemAssetData;
    //获取科室
    const itemDeptListData = deptListData ? deptListData : await getVenderDeptList({ siteUid: data.data.siteUID });

    //获取默认科室
    // const deptItemData = await getStockedDept({ orgUid: data.data.siteUID });

    // // 判断 deptItemData 是否有 uid
    // let foundDeptUid = null;
    // if (deptItemData && deptItemData.uid) {
    //   const foundDept = itemDeptListData.find(item => item.uid === deptItemData.uid);
    //   if (foundDept) {
    //     foundDeptUid = foundDept.uid;
    //   }
    // }

    //组装科室select option
    itemDeptListData.map(item => {
      item.label = item.name;
      item.value = item.uid;
    });
    setDeptListData(itemDeptListData);

    //修改form表单配置
    formOptionObj.map(item => {
      switch (item.name) {
        case "asset":
          item["attribute"]["label"]["value"] = `照片 (${photoCaption?.customerConfig.actualValue})`;
          break;
        case "asset4":
          item["attribute"]["label"]["value"] = `临床验收确认(${checkCaption?.customerConfig.actualValue})`;
          break;
        case "departNum":
          item["attribute"]["options"] = itemAssetData;
          break;
        case "clinicalDeptUID":
          item["attribute"]["options"] = itemDeptListData;
          break;
        case "siteUID":
          item["attribute"]["treeData"] = siteMapDatas;
          break;
        default:
          break;
      }
    });
    setFormConfig(formOptionObj);

    //更新form表单数据
    delete data.data.siteUID;
    console.log("save-data", data.data);
    setArrivalListItem(() => {
      return {
        ...data.data,
      };
    });
  };
  const updateFormConfig = async (siteID, formData) => {
    console.log("editType", editType.current.type);
    //获取科室信息
    const itemDeptListData = await getVenderDeptList({ siteUid: siteID });

    //获取默认科室
    const deptItemData = await getStockedDept({ orgUid: siteID });

    //组装科室select option
    itemDeptListData.map(item => {
      item.label = item.name;
      item.value = item.uid;
    });

    // 判断 deptItemData 是否有 uid
    let foundDeptUid = null;
    if (deptItemData && deptItemData.uid) {
      const foundDept = itemDeptListData.find(item => item.uid === deptItemData.uid);
      if (foundDept) {
        foundDeptUid = foundDept.uid;
      }
    }

    setDeptListData(itemDeptListData);
    let formOptions;
    if (editType.current.type == "add") {
      //修改 formOptionObj 设备类型配置
      formOptions = newArrivalFormOption;
    } else {
      formOptions = formOptionObj;
    }
    formOptions.map(item => {
      switch (item.name) {
        case "clinicalDeptUID":
          item["attribute"]["options"] = itemDeptListData;
          break;
        default:
          break;
      }
    });
    setFormConfig(formOptions);
    //update
    setArrivalListItem(currentValue => {
      return {
        ...formData,
        clinicalDeptUID: foundDeptUid,
      };
    });
  };

  //新增到货清单信息 .设置表单的基础配置
  const addNewArrival = async () => {
    editType.current = {
      type: "add",
      title: "新增到货清单信息",
    };

    setArrivalListItem(currentValue => {
      return {
        siteUID: model.hospital,
        processType: "Normal",
      };
    });
    let arrivalContractLis = await getArrivalContractLis({
      tenantUid: model.hospital,
    });
    arrivalContractLis.map(item => {
      item.label = `${item.name}/${item.contractNum}`;
      item.value = item.id;
    });

    //获取院区map信息
    const siteMapDatas = siteMapData
      ? siteMapData
      : convertLabelToTitle(await getSiteMap({ tenantUid: model.hospital }));

    setSiteMapData(siteMapDatas);

    //获取设备类型i18信息
    const i8nMessagesData = i8nData
      ? i8nData
      : await getI8nMessages({
          msgTypes,
          siteId: model.siteId,
        });

    setI8nData(i8nMessagesData);
    //组装设备分类、设备状态select option
    const { assetGroup } = i8nMessagesData;
    assetGroup.map(item => {
      item.label = item.valueZh;
      item.value = Number(item.msgKey);
    });
    //修改 formOptionObj 设备类型配置
    newArrivalFormOption.map(item => {
      switch (item.name) {
        case "assetGroup":
          item["attribute"]["options"] = assetGroup;
          break;
        case "siteUID":
          item["attribute"]["treeData"] = siteMapDatas;
          break;
        default:
          break;
      }
    });
    setContractList(arrivalContractLis);
    setShowContractInfo(true);
    setEditState(true);
    setFormConfig(newArrivalFormOption);
  };
  const getContractItemList = async id => {
    //获取订购合同里的订购项目
    let arrivalContractItemList = await getArrivalContractItemList({
      contractId: id,
    });

    arrivalContractItemList.map(item => {
      item.label = `${item.contractAssetName}/${item.contractAssetQuantity}`;
      item.value = item.id;
    });

    // 找到 id 等于指定值的对象合同
    let findContract = contractList.find(item => item.id === id);

    // 提取 num 值
    let numValue = findContract ? findContract.contractNum : null;
    setContractNum(numValue);

    setContractItem(arrivalContractItemList[0]);
    setContractItemList(arrivalContractItemList);
    setArrivalListItem({
      ...arrivalListItem,
      processType: "Normal",
      contractNum: numValue,
      contractPrice: findContract?.amount,
      assetName: arrivalContractItemList[0]?.contractAssetName,
      quantity: arrivalContractItemList[0]?.contractAssetQuantity,
    });
  };
  const selectItem = id => {
    // 找到 id 等于指定值的对象
    let findContractItem = contractItemList.find(item => item.id === id);

    setContractItem(findContractItem);
    setArrivalListItem({
      ...arrivalListItem,
      assetName: findContractItem.contractAssetName,
      quantity: findContractItem.contractAssetQuantity,
    });
  };

  //保存到货清单信息
  const saveArrivalItemdata = async data => {
    if (requestLockRef.current) {
      console.log("请求被锁定，跳过");
      return;
    }
    requestLockRef.current = true;
    const {
      agent,
      asset,
      asset2,
      asset3,
      asset4,
      boxInstallationDate,
      contactPerson,
      contactPersonTel,
      contractDate,
      contractNum,
      id,
      invoiceDate,
      invoiceNum,
      repairer,
      repairerTel,
      usingDate,
      processType,

      annualMaintenanceTimes,
      siteUID,
      clinicalDeptUID,
      departNum,
      lifecycleInYear,
      floorName,
      hasRegistrationNo,
      registrationNo,
      locationName,
      manufactDate,
      measureDevice,
      meteringCycle,
      serialNum,
      usingPerson,
      usingPersonTel,
      workHours,
    } = data;

    let newobj;
    // let clinicalDeptName = "";
    // //如果有选择过科室，把科室名也传入后端
    // if (clinicalDeptUID) {
    //   if (deptListData && deptListData.length > 0) {
    //     const foundDept = deptListData.find(item => item.value == clinicalDeptUID);
    //     clinicalDeptName = foundDept ? foundDept.name : "";
    //   }
    // }
    if (editType.current.type == "add") {
      //组装最后需要上传保存的数据

      newobj = {
        ...data,
        tenantUid: model.hospital,
        id: null,
        contractItemId: contractItem?.id,
        saveType: "submit",
      };
    } else {
      const copyasset = assetData.current.map(item => {
        return item.id == departNum
          ? {
              id: item.id,
              annualMaintenanceTimes,
              siteUID,
              clinicalDeptUID,
              lifecycleInYear,
              floorName,
              hasRegistrationNo,
              registrationNo,
              locationName,
              manufactDate,
              measureDevice,
              meteringCycle,
              serialNum,
              usingPerson,
              usingPersonTel,
              workHours,
            }
          : item;
      });
      //保存前处理上传本地附件
      const [assetAttachment, asset4Attachment, asset2Attachment, asset3Attachment] = await Promise.all([
        updateFiles(asset),
        updateFiles(asset4),
        updateFiles(asset2),
        updateFiles(asset3),
      ]);
      let assetLocalArray = asset.reduce((result, currentValue) => {
        if (!currentValue.size) {
          result.push(currentValue);
        }
        return result;
      }, []);
      let asset4LocalArray = asset4.reduce((result, currentValue) => {
        if (!currentValue.size) {
          result.push(currentValue);
        }
        return result;
      }, []);
      let asset2LocalArray = asset2.reduce((result, currentValue) => {
        if (!currentValue.size) {
          result.push(currentValue);
        }
        return result;
      }, []);
      let asset3LocalArray = asset3.reduce((result, currentValue) => {
        if (!currentValue.size) {
          result.push(currentValue);
        }
        return result;
      }, []);
      const objAttachments = [];
      if (assetAttachment) {
        assetAttachment.map(item => {
          item.boSubType = "asset";
        });
        objAttachments.push(...assetLocalArray, ...assetAttachment);
      }
      if (asset4Attachment) {
        asset4Attachment.map(item => {
          item.boSubType = "asset4";
        });
        objAttachments.push(...asset4LocalArray, ...asset4Attachment);
      }
      if (asset2Attachment) {
        asset2Attachment.map(item => {
          item.boSubType = "asset2";
        });
        objAttachments.push(...asset2LocalArray, ...asset2Attachment);
      }
      if (asset3Attachment) {
        asset3Attachment.map(item => {
          item.boSubType = "asset3";
        });
        objAttachments.push(...asset3LocalArray, ...asset3Attachment);
      }
      //组装最后需要上传保存的数据
      newobj = {
        agent,
        tenantUid: model.hospital,
        attachments: [...objAttachments],
        boxInstallationDate,
        contactPerson,
        contactPersonTel,
        contractDate,
        contractNum,
        id,
        invoiceDate,
        invoiceNum,
        repairer,
        repairerTel,
        usingDate,
        arrivalListItemListFormList: copyasset,
      };
    }

    const savestatus = await saveArrival(newobj);
    requestLockRef.current = false;
    if (savestatus.data === null) {
      message.error(savestatus.message);
    } else {
      message.success("编辑成功");
      window.location.reload(false);
    }
  };

  const deleteArrivalData = async id => {
    const deleteStatus = await deleteArrival(id);
    if (deleteStatus) {
      window.location.reload(false);
    }
  };

  useEffect(() => {
    if (model.hospital) {
      getArrival(model.hospital);
    }
  }, [model.hospital, page]);

  const columns = [
    {
      title: "到货单名称/编号",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "合同号",
      dataIndex: "contractNum",
      key: "contractNum",
    },
    {
      title: "创建人",
      dataIndex: "creatorUID",
      key: ["creator", "name"],
      render: (_, item) => {
        return item.creator ? item.creator.name : item.creatorUID;
      },
    },
    {
      title: "院方经办人",
      dataIndex: "agent",
      key: "agent",
    },
    {
      title: "资产名称",
      dataIndex: "assetName",
      key: "assetName",
    },
    {
      title: "设备型号",
      dataIndex: "functionType",
      key: "functionType",
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
    },
    {
      title: "安装流程类型",
      dataIndex: "processType",
      key: "processType",
      render: text => {
        switch (text) {
          case "Easy":
            return "简易";
          case "Normal":
            return "正常";
        }
      },
    },
    {
      title: "当前状态",
      dataIndex: "status",
      key: "status",
      render: text => {
        switch (text) {
          case 1:
            return "未完成";
          case 2:
            return "部分完成";
          case 3:
            return "已完成";
        }
      },
    },
    {
      title: "操作",
      dataIndex: "edit",
      key: "edit",
      render: (_, item) => {
        return (
          <>
            <a
              onClick={() => {
                editArrival({
                  type: "edit",
                  title: "编辑到货清单信息",
                  data: item,
                });
              }}
            >
              编辑
            </a>
            {item.status == 1 && (
              <Popconfirm
                placement="topRight"
                title={`确认删除${item.name || ""}`}
                onConfirm={() => {
                  deleteArrivalData(item.id);
                }}
                okText="Yes"
                cancelText="No"
              >
                <a style={{ marginLeft: "5px" }}>删除</a>
              </Popconfirm>
            )}
          </>
        );
      },
    },
  ];
  return (
    <div className="arrival-list">
      {arrivalList && (
        <Table
          rowKey={record => {
            return record.id;
          }}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            position: ["bottomRight"],
            size: "small",
            pageSize: 10,
            total: arrivalListTotal,
            onChange: (current, size) => {
              setPage(current - 1);
            },
          }}
          title={() => {
            return (
              <div className="table-title">
                到货清单
                <Button onClick={addNewArrival} type="primary">
                  新增到货清单
                </Button>
              </div>
            );
          }}
          columns={columns}
          dataSource={arrivalList}
        />
      )}

      <Modal
        closable={false}
        centered
        title={editType.current?.title}
        open={editState && formConfig}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("arrival-list")[0]}
        width="500"
      >
        {showContractInfo && (
          <>
            <div className="contract-info">
              选择订购合同
              <Select placeholder="请选择设备合同" onChange={getContractItemList} options={contractList} />
              <Select
                placeholder="请选择合同订购项"
                onChange={id => {
                  selectItem(id);
                }}
                value={contractItem?.id}
                options={contractItemList}
              />
            </div>
            <Divider />
          </>
        )}
        <MyForm
          data={{
            ...arrivalListItem,
          }}
          options={formConfig}
          oncancel={() => {
            setEditState(false);
          }}
          onSubmit={data => {
            saveArrivalItemdata(data);
          }}
        ></MyForm>
      </Modal>
    </div>
  );
}

// 递归将 label 字段改为 title 字段
function convertLabelToTitle(arr) {
  if (!Array.isArray(arr)) return arr;
  return arr.map(item => {
    const { label, children, ...rest } = item;
    const newItem = {
      ...rest,
      title: label,
    };
    if (children && Array.isArray(children)) {
      newItem.children = convertLabelToTitle(children);
    }
    return newItem;
  });
}

export default ArrivalList;
