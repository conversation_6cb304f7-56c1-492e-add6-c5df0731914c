import React, { useState, useEffect, useContext, useRef } from "react";
import { useParams } from "react-router-dom";
import { Descriptions, Table, Button, message, Modal, Select, TreeSelect, Divider } from "antd";

import moment from "moment";
import rest from "@urls/axios";
import { EditOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useForm } from "react-hook-form";
import {
  getContractDetail,
  getSiteList,
  getSiteMap,
  saveContractDetail,
  submitAuditContract,
  getI8nMessages,
  getByKeyAndSite,
} from "@/service";

import { venderContext } from "../context/index";
import { MyForm } from "@/components/MyForm";
import { contractAttachmentsType, contractTypeObj, formOptionObj, getTitle } from "./contractConfig.jsx";
import "./detail.less";

const formatDate = (date, pattern = "YYYY-MM-DD") => {
  return date ? moment(date).format(pattern) : null;
};

function ContractDetail(props) {
  const { model } = useContext(venderContext);
  let { contractId } = useParams();
  const [contractDetail, setContractDetail] = useState(null);
  const [siteListData, setSiteListData] = useState(null); //院区
  const [siteUID, setSiteUID] = useState(model.hospital); //选择院区
  const [contractAttachments, setContractAttachments] = useState([]);
  const [paymentPlan, setPaymentPlan] = useState([]);
  const [invoiceInfo, setInvoiceInfo] = useState([]);
  const [purchaseAsset, setPurchaseAsset] = useState([]);
  const [formOption, setFormOption] = useState(null);
  const editType = useRef(null);
  const [editState, setEditState] = useState(false);
  const editIndex = useRef();
  const [editBtnDisabled, setEditBtnDisabled] = useState(false);
  const [updateState, setUpdateState] = useState(true);
  const [loading, setLoading] = useState(false);
  const [i8nData, setI8nData] = useState(null);
  const fieldConfig = useRef({});

  const {
    register,
    setError,
    formState: { errors },
    clearErrors,
  } = useForm({
    shouldUseNativeValidation: true,
  });

  const msgTypes = ["contractAttachmentType"].join(",");
  const [contractAttachmentTypeObj, setContractAttachmentTypeObj] = useState();
  const contractAttachmentsColumns = [
    {
      title: "附件类型",
      dataIndex: "boSubType",
      key: "boSubType",
      render: text => {
        return getTitle(text, contractAttachmentTypeObj);
      },
    },
    {
      title: "文件名称",
      dataIndex: "objectName",
      key: "objectName",
    },
    {
      title: "上传时间",
      dataIndex: "createdDate",
      key: "createdDate",
      render: text => {
        return moment(text).format("YYYY-MM-DD");
      },
    },
    {
      title: "操作",
      dataIndex: "objectId",
      key: "objectId",
      render: id => {
        return (
          <Button
            onClick={() => {
              editContractDetail({
                type: "contractAttachments",
                title: "删除附件",
                model: "delete",
                index: id,
              });
            }}
            type="primary"
          >
            删除
          </Button>
        );
      },
    },
  ];
  const paymentPlanColumns = [
    {
      title: "付款金额",
      dataIndex: "paymentPrice",
      key: "paymentPrice",
    },
    {
      title: "付款期",
      dataIndex: "paymentPeriod",
      key: "paymentPeriod",
    },
    {
      title: "应付日期",
      dataIndex: "estimatedPaymentDate",
      key: "estimatedPaymentDate",
    },
    {
      title: "支付方式",
      dataIndex: "paymentMode",
      key: "paymentMode",
    },
    {
      title: "付款日期",
      dataIndex: "paymentDate",
      key: "paymentDate",
    },
    {
      title: "备注",
      dataIndex: "comments",
      key: "comments",
    },
    {
      title: "操作",
      dataIndex: "edit",
      key: "edit",
      render: (_, item, index) => {
        return (
          <>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "paymentPlan",
                  title: "删除付款方式",
                  model: "delete",
                  index: index,
                });
              }}
              type="primary"
            >
              删除
            </Button>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "paymentPlan",
                  title: "编辑付款方式",
                  model: "edit",
                  index: index,
                });
              }}
              type="primary"
            >
              编辑
            </Button>
          </>
        );
      },
    },
  ];
  const invoiceInfoColumns = [
    {
      title: "发票号",
      dataIndex: "invoiceNum",
      key: "invoiceNum",
    },
    {
      title: "发票金额",
      dataIndex: "invoicePrice",
      key: "invoicePrice",
    },
    {
      title: "开票时间",
      dataIndex: "invoiceTime",
      key: "invoiceTime",
    },
    {
      title: "支付方式",
      dataIndex: "paymentMode",
      key: "paymentMode",
    },
    {
      title: "是否付款",
      dataIndex: "paid",
      key: "paid",
      render: text => {
        return text ? "是" : "否";
      },
    },
    {
      title: "备注",
      dataIndex: "comments",
      key: "comments",
    },
    {
      title: "操作",
      dataIndex: "invoiceNum",
      key: "invoiceNum",
      render: (_, item, index) => {
        return (
          <>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "invoiceInfo",
                  title: "删除发票信息",
                  model: "delete",
                  index: index,
                });
              }}
              type="primary"
            >
              删除
            </Button>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "invoiceInfo",
                  title: "编辑发票信息",
                  model: "edit",
                  index: index,
                });
              }}
              type="primary"
            >
              编辑
            </Button>
          </>
        );
      },
    },
  ];
  const purchaseAssetColumns = [
    {
      title: "合同内设备名称",
      dataIndex: "contractAssetName",
      key: "contractAssetName",
    },
    {
      title: "单价",
      dataIndex: "contractAssetPrice",
      key: "contractAssetPrice",
    },
    {
      title: "合同内设备数量",
      dataIndex: "contractAssetQuantity",
      key: "contractAssetQuantity",
    },
    {
      title: "规格型号",
      dataIndex: "pattern",
      key: "pattern",
    },
    {
      title: "产地",
      dataIndex: "productionPlace",
      key: "productionPlace",
    },
    {
      title: "备注",
      dataIndex: "comments",
      key: "comments",
    },
    {
      title: "操作",
      dataIndex: "invoiceNum",
      key: "invoiceNum",
      render: (_, item, index) => {
        return (
          <>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "purchaseAsset",
                  title: "删除合同内设备",
                  model: "delete",
                  index: index,
                });
              }}
              type="primary"
            >
              删除
            </Button>
            <Button
              onClick={() => {
                editContractDetail({
                  type: "purchaseAsset",
                  title: "编辑合同内设备",
                  model: "edit",
                  index: index,
                });
              }}
              type="primary"
            >
              编辑
            </Button>
          </>
        );
      },
    },
  ];

  const fetchFieldData = async siteUid => {
    const keys = [
      "contractSigningDate",
      "contractDeliveryLocation",
      "contractDeliveryTime",
      "contractMaintenancePeriod",
    ];
    const promises = keys.map(key => getByKeyAndSite({ key, siteUid }));
    const results = await Promise.all(promises);

    return {
      signingDate: JSON.parse(results[0]?.customerConfig?.actualValue || "{}"),
      deliveryLocation: JSON.parse(results[1]?.customerConfig?.actualValue || "{}"),
      deliveryTime: JSON.parse(results[2]?.customerConfig?.actualValue || "{}"),
      maintenancePeriod: JSON.parse(results[3]?.customerConfig?.actualValue || "{}"),
    };
  };
  //字段过滤条件查询
  const displayMaintenanceField = async (siteUid, type = "") => {
    //只有编辑基础信息需要以下逻辑处理（根据接口定义控制表单字段）
    if (editType.current != null && type != "baseInfoForm") {
      return;
    }
    // 如果没有传入 siteUid，则使用默认值
    const useSiteUid = siteUid || model.hospital;
    setSiteUID(useSiteUid);
    const filterObject = await fetchFieldData(useSiteUid);
    fieldConfig.current = filterObject;
    let baseInfoOption = JSON.parse(JSON.stringify(formOptionObj["baseInfoForm"]));
    Object.keys(filterObject).forEach(key => {
      const filterItem = filterObject[key];
      const optionIndex = baseInfoOption.findIndex(item => item.name === key);
      if (filterItem.rendered === false && optionIndex !== -1) {
        baseInfoOption.splice(optionIndex, 1); // Remove filter item
      } else if (optionIndex !== -1) {
        const optionItem = baseInfoOption[optionIndex];
        optionItem.attribute.label.required = filterItem.required;
        optionItem.attribute.rule.required = filterItem.required ? filterItem.requiredMessage : "";
      }
    });
    setFormOption(baseInfoOption);
  };

  //获取合同信息
  const getContractInfo = async id => {
    let ContractDetail = await getContractDetail(id);
    setContractDetail({ ...ContractDetail, id: id });
    setSiteUID(ContractDetail?.site?.siteUID || ContractDetail?.siteUid);

    if (ContractDetail) {
      const { status } = ContractDetail;
      setEditBtnDisabled(status === 2 || status === 4);
      const isLoading = status === 2;
      // setLoading(isLoading);
      // setUpdateState(
      //     isLoading ? false : status === 1 || status === 3 || status === 4
      // );
    }
  };
  // 新增、编辑、删除
  const editContractDetail = async data => {
    const { index, model, title, type } = data;
    switch (model) {
      case "delete":
        deleteItem(type, index);
        break;
      case "add":
        addItem(data);
        break;
      case "edit":
        editItem(data);
        break;
      default:
        break;
    }
  };
  // 上传附件服务器，并上传供应商信息接口
  const updateFiles = async filesArray => {
    // 过滤本地需要上传服务器的文件
    let filterArray = filesArray.reduce((result, currentValue) => {
      if (currentValue.size) {
        result.push(currentValue);
      }
      return result;
    }, []);
    return Promise.all(
      filterArray.map(item => {
        return new Promise((resolve, reject) => {
          rest
            .file(item)
            .then(data => {
              const attachment = {
                objectStorageId: data.objectId,
                thumbnail: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                original: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                ...data,
              };
              resolve(attachment);
            })
            .catch(() => {
              reject("上传失败");
            });
        });
      }),
    );
  };

  const deleteItem = (type, data) => {
    switch (type) {
      case "contractAttachments":
        let updatedContractDetail = {
          ...contractDetail,
          contractAttachments: contractDetail.contractAttachments.filter(item => item.objectId != data),
        };
        setContractDetail(updatedContractDetail);
        break;
      case "paymentPlan":
        let updatedPaymentPlan = {
          ...contractDetail,
          paymentPlan: [...contractDetail.paymentPlan.slice(0, data), ...contractDetail.paymentPlan.slice(data + 1)],
        };

        setContractDetail(updatedPaymentPlan);
        break;
      case "invoiceInfo":
        let updatedinvoiceInfo = {
          ...contractDetail,
          invoiceInfo: [...contractDetail.invoiceInfo.slice(0, data), ...contractDetail.invoiceInfo.slice(data + 1)],
        };

        setContractDetail(updatedinvoiceInfo);
        break;
      case "purchaseAsset":
        let updatedpurchaseAsset = {
          ...contractDetail,
          purchaseAsset: [
            ...contractDetail.purchaseAsset.slice(0, data),
            ...contractDetail.purchaseAsset.slice(data + 1),
          ],
        };

        setContractDetail(updatedpurchaseAsset);
        break;
      default:
        break;
    }
  };
  const addItem = async data => {
    editType.current = data;
    if (data.type == "baseInfoForm") {
      const siteData = convertLabelToTitle(await getSiteMap({ tenantUid: model.hospital }));
      setSiteListData(siteData);
      displayMaintenanceField(siteUID, "baseInfoForm");
    } else if (data.type == "addContractAttachment") {
      //获取i18信息
      const i8nMessagesData = i8nData
        ? i8nData
        : await getI8nMessages({
            msgTypes,
            siteId: model.siteId,
          });
      setI8nData(i8nMessagesData);
      //组装合同类型 select option
      const { contractAttachmentType } = i8nMessagesData;
      contractAttachmentType
        .sort((a, b) => a.id - b.id)
        .map(item => {
          item.label = item.valueZh;
          item.value = item.msgKey;
        });
      setContractAttachmentTypeObj(contractAttachmentType);
      //修改 formOptionObj 附件类型下拉配置
      formOptionObj[data.type].map(item => {
        switch (item.name) {
          case "contractAttachmentsType":
            item["attribute"]["options"] = contractAttachmentType;
            break;
          default:
            break;
        }
      });
      setFormOption(formOptionObj[data.type]);
    } else {
      setFormOption(formOptionObj[data.type]);
    }
    setEditState(true);
  };
  const editItem = async data => {
    const editTypeItem = data.type != "baseInfoForm" && contractDetail[data.type][data.index];
    const newContractDetail = contractDetail;
    editIndex.current = data.index;
    let formOption = formOptionObj[data.type];
    switch (data.type) {
      case "baseInfoForm":
        const siteData = convertLabelToTitle(await getSiteMap({ tenantUid: model.hospital }));
        setSiteListData(siteData);
        break;
      case "paymentPlan":
        newContractDetail.payment = editTypeItem;
        break;
      case "invoiceInfo":
        newContractDetail.invoice = editTypeItem;
        break;
      case "purchaseAsset":
        newContractDetail.purchase = editTypeItem;
        break;
    }
    setContractDetail(newContractDetail);
    if (editTypeItem) {
      setFormOption(formOption);
    } else {
      console.log("contractDetail", contractDetail);
      displayMaintenanceField(contractDetail.site.siteUID || contractDetail?.siteUid, data.type);
    }
    setEditState(true);
    editType.current = data;
  };
  // form 表单保存
  const savecompanydata = async data => {
    if (editType.current.type == "baseInfoForm" && !siteUID) {
      setError("siteUID", {
        type: "custom",
        message: "请选择院区",
      });
      return;
    }
    switch (editType.current.type) {
      case "baseInfoForm":
        // 从 siteListData 里找出 value 等于 siteUID 的对象
        // 递归查找 siteUID 对应的节点
        const currentSiteObj = findNodeByValue(siteListData, siteUID);
        console.log("currentSiteObj", currentSiteObj);
        if (currentSiteObj) {
          data.siteName = currentSiteObj.title;
          data.siteUid = currentSiteObj.value;
        }
        data.endMonths = Math.ceil(moment(data.endDate).diff(moment(data.startDate), "months", true));
        if (["Artificial", "Full"].includes(data.contractType) && !(data.warrantyStartDate && data.warrantyEndDate)) {
          message.error("请选择维保开始和结束时间");
          return;
        }

        break;
      case "addContractAttachment":
        try {
          const addContractAttachment = await updateFiles(data.addContractAttachments);
          addContractAttachment.map(item => {
            item.boSubType = data.contractAttachmentsType;
            return item;
          });

          data.contractAttachments
            ? data.contractAttachments.push(...addContractAttachment)
            : (data.contractAttachments = addContractAttachment);
          delete data.addContractAttachments;
          delete data.contractAttachmentsType;
        } catch (error) {
          console.log("savecompanydata-error", error);
        }
        break;
      case "paymentPlan":
        const totalPrice =
          Number(contractDetail?.amount ? contractDetail.amount : 0) +
          Number(contractDetail?.supplementaryAmount ? contractDetail.supplementaryAmount : 0);
        if (editIndex.current >= 0) {
          data.paymentPlan[editIndex.current] = data.payment;
        } else {
          data.paymentPlan ? data.paymentPlan.push(data.payment) : (data.paymentPlan = [data.payment]);
        }
        // 计算付款计划总金额
        const paymentTotal = data.paymentPlan.reduce((result, currentValue) => {
          return result + currentValue.paymentPrice;
        }, 0);
        if (paymentTotal > totalPrice) {
          message.error("付款计划金额超出合同金额");
          return;
        }
        delete data.payment;
        break;
      case "invoiceInfo":
        const { paymentAttachments } = data.invoice;
        if (paymentAttachments) {
          const invoiceAttachments = await updateFiles(paymentAttachments);
          // 过滤不需上传文件
          const cloudAttachments = paymentAttachments.reduce((result, currentValue) => {
            if (!currentValue.size) {
              result.push(currentValue);
            }
            return result;
          }, []);
          data.invoice.paymentAttachments = [...cloudAttachments, ...invoiceAttachments];
        }
        if (editIndex.current >= 0) {
          data.invoiceInfo[editIndex.current] = data.invoice;
        } else {
          data.invoiceInfo ? data.invoiceInfo.push(data.invoice) : (data.invoiceInfo = [data.invoice]);
        }
        delete data.invoice;
        break;
      case "purchaseAsset":
        if (editIndex.current >= 0) {
          data.purchaseAsset[editIndex.current] = data.purchase;
        } else {
          data.purchaseAsset ? data.purchaseAsset.push(data.purchase) : (data.purchaseAsset = [data.purchase]);
        }
        delete data.purchase;
        break;
      default:
        break;
    }
    setEditState(false);

    editIndex.current = undefined;
    delete data.site;
    console.log("savecompanydata", data);
    setContractDetail(data);
  };
  //暂存
  const updateContractDetail = async (alertMessage = true) => {
    setLoading(true);
    try {
      let saveContractStatus = await saveContractDetail({
        ...contractDetail,
        existAsset: [],
      });
      // 暂存成功后才能提交审核
      if (saveContractStatus) {
        getContractInfo(saveContractStatus.id);
        switch (saveContractStatus.status) {
          // 1新建  2审核 3退回 4审核通过
          case 3:
          case 4:
            setUpdateState(true);
            setLoading(false);
            break;
          case 1:
          case 2:
            // setUpdateState(false);
            setLoading(true);
            break;
          default:
            setLoading(false);
            setUpdateState(true);
            break;
        }
        alertMessage && message.info("成功保存，可以进行提交审核。");
        return saveContractStatus.id;
      }
    } catch (error) {
      setLoading(false);
      alertMessage && message.info("失败，请重新提交");
      return null;
    }
  };

  const cancelEdit = () => {
    switch (editType.current.type) {
      case "paymentPlan":
        delete contractDetail?.payment;
        break;
      case "invoiceInfo":
        delete contractDetail?.invoice;
        break;
      case "purchaseAsset":
        delete contractDetail?.purchase;
        break;
    }
    setSiteUID();
    setEditState(false);
  };

  //提交审核
  const submitContract = async () => {
    setLoading(true);

    // setUpdateState(false);
    //需求修改，提交审核前都先调用暂存接口，获取到id后再提交
    // jira：YHAAO-22999
    const contractId = await updateContractDetail(false);
    const submitStatue = await submitAuditContract(contractId);
    //submitStatue 1新建  2审核 3退回 4审核通过
    if (submitStatue.bizStatusCode == "OK") {
      message.info("成功提交审核");
    } else {
      message.error(`提交审核失败：${submitStatue.message}`);
      setUpdateState(true);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (siteUID) {
      displayMaintenanceField(siteUID, "baseInfoForm");
      clearErrors("siteUID");
    }
  }, [siteUID]);

  useEffect(() => {
    if (contractId && contractId !== "0000") {
      getContractInfo(contractId);
    }
  }, [contractId]);

  useEffect(() => {
    setContractAttachments(contractDetail?.contractAttachments);
    setPaymentPlan(contractDetail?.paymentPlan);
    setInvoiceInfo(contractDetail?.invoiceInfo);
    setPurchaseAsset(contractDetail?.purchaseAsset);
  }, [contractDetail]);

  useEffect(() => {
    const getI18nFun = async () => {
      const i8nMessagesData = i8nData
        ? i8nData
        : await getI8nMessages({
            msgTypes,
            siteId: model.siteId,
          });
      setI8nData(i8nMessagesData);
      //组装合同类型 select option
      const { contractAttachmentType } = i8nMessagesData;
      contractAttachmentType.map(item => {
        item.label = item.valueZh;
        item.value = item.msgKey;
      });
      setContractAttachmentTypeObj(contractAttachmentType);
    };

    if (model.siteId) {
      getI18nFun();
    }
  }, [model.siteId]);

  useEffect(() => {
    setSiteUID(model.hospital);
    if (
      contractDetail &&
      (contractDetail.site.tenantUID !== model.hospital || contractDetail.siteUid !== model.hospital)
    ) {
      props.history.goBack();
    }
  }, [model.hospital]);

  return (
    <div className="contract-detail">
      <Descriptions
        title="基本资料"
        column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
        extra={
          <Button
            disabled={editBtnDisabled}
            type="link"
            onClick={() => {
              if (contractId == "0000") {
                editContractDetail({
                  type: "baseInfoForm",
                  title: "编辑基本资料",
                  model: "add",
                  index: "",
                });
              } else {
                editContractDetail({
                  type: "baseInfoForm",
                  title: "编辑基本资料",
                  model: "edit",
                  index: "",
                });
              }
            }}
          >
            <EditOutlined />
          </Button>
        }
      >
        {contractDetail && (
          <>
            <Descriptions.Item label="所属院区">
              {contractDetail?.siteName || contractDetail?.site?.name}
            </Descriptions.Item>
            <Descriptions.Item label="合同类型">
              {contractDetail && contractTypeObj[contractDetail.contractType]}
            </Descriptions.Item>
            <Descriptions.Item label="项目名称">{contractDetail?.name}</Descriptions.Item>
            <Descriptions.Item label="采购单号">{contractDetail?.purchaseNum}</Descriptions.Item>
            <Descriptions.Item label="标的名称">{contractDetail?.bidName}</Descriptions.Item>
            <Descriptions.Item label="合同编号/档案号">{contractDetail?.contractNum}</Descriptions.Item>
            <Descriptions.Item label="合同金额">{contractDetail?.amount}</Descriptions.Item>
            <Descriptions.Item label="合同开始时间">
              {contractDetail?.startDate && moment(contractDetail.startDate).format("YYYY-MM-DD")}
            </Descriptions.Item>
            <Descriptions.Item label="合同截止日期">
              {contractDetail?.endDate && moment(contractDetail.endDate).format("YYYY-MM-DD")}
            </Descriptions.Item>
            <Descriptions.Item label="合同时间（月）">{contractDetail?.endMonths}</Descriptions.Item>

            {fieldConfig.current?.signingDate?.rendered && (
              <Descriptions.Item label="合同签订日期">
                {formatDate(contractDetail?.signingDate, fieldConfig.current?.signingDate?.datePattern)}
              </Descriptions.Item>
            )}
            {fieldConfig.current?.maintenancePeriod?.rendered && (
              <Descriptions.Item label="维保年限（月）">{contractDetail?.maintenancePeriod}</Descriptions.Item>
            )}
            {fieldConfig.current?.deliveryTime?.rendered && (
              <Descriptions.Item label="交付时间">
                {formatDate(contractDetail?.deliveryTime, fieldConfig.current?.deliveryTime?.datePattern)}
              </Descriptions.Item>
            )}
            {fieldConfig.current?.deliveryLocation?.rendered && (
              <Descriptions.Item label="交付地点">{contractDetail?.deliveryLocation}</Descriptions.Item>
            )}
            <Descriptions.Item label="项目编号">{contractDetail?.reportNum}</Descriptions.Item>
            <Descriptions.Item label="外贸合同号">{contractDetail?.foreignTradeNum}</Descriptions.Item>
            <Descriptions.Item label="招标编号">{contractDetail?.bidNum}</Descriptions.Item>
            <Descriptions.Item label="维保开始时间">
              {contractDetail?.warrantyStartDate && moment(contractDetail.warrantyStartDate).format("YYYY-MM-DD")}
            </Descriptions.Item>
            <Descriptions.Item label="维保截止日期">
              {contractDetail?.warrantyEndDate && moment(contractDetail.warrantyEndDate).format("YYYY-MM-DD")}
            </Descriptions.Item>
            <Descriptions.Item label="补充协议内容">{contractDetail?.supplementary}</Descriptions.Item>
            <Descriptions.Item label="补充协议金额">{contractDetail?.supplementaryAmount}</Descriptions.Item>
            <Descriptions.Item span={3}></Descriptions.Item>
            <Descriptions.Item>
              <div className="contract-attachments">
                <Button
                  onClick={() => {
                    editContractDetail({
                      type: "addContractAttachment",
                      title: "添加合同附件",
                      model: "add",
                      index: "",
                    });
                  }}
                  type="primary"
                  style={{
                    marginBottom: 16,
                  }}
                >
                  添加附件
                </Button>
                <Table
                  rowKey={record => {
                    return record.objectId;
                  }}
                  columns={contractAttachmentsColumns}
                  dataSource={contractAttachments}
                  pagination={false}
                ></Table>
              </div>
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
      <Descriptions
        title={`付款计划 合同总额：${
          Number(contractDetail?.amount ? contractDetail.amount : 0) +
          Number(contractDetail?.supplementaryAmount ? contractDetail.supplementaryAmount : 0)
        }元（合同总额=合同金额+补充协议金额）`}
        column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
        extra={
          <Button
            type="link"
            onClick={() => {
              editContractDetail({
                type: "paymentPlan",
                title: "添加付款计划",
                model: "add",
                index: "",
              });
            }}
          >
            <PlusCircleOutlined />
          </Button>
        }
      >
        <Descriptions.Item>
          <Table
            rowKey={record => {
              return record.id;
            }}
            columns={paymentPlanColumns}
            dataSource={paymentPlan}
            pagination={false}
          ></Table>
        </Descriptions.Item>
      </Descriptions>
      <Descriptions
        title="发票信息"
        column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
        extra={
          <Button
            type="link"
            onClick={() => {
              editContractDetail({
                type: "invoiceInfo",
                title: "添加发票信息",
                model: "add",
                index: "",
              });
            }}
          >
            <PlusCircleOutlined />
          </Button>
        }
      >
        <Descriptions.Item>
          <Table
            rowKey={record => {
              return record.id;
            }}
            columns={invoiceInfoColumns}
            dataSource={invoiceInfo}
            pagination={false}
          ></Table>
        </Descriptions.Item>
      </Descriptions>

      <Descriptions
        title="添加订购设备"
        column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
        extra={
          <Button
            type="link"
            onClick={() => {
              editContractDetail({
                type: "purchaseAsset",
                title: "添加订购设备",
                model: "add",
                index: "",
              });
            }}
          >
            <PlusCircleOutlined />
          </Button>
        }
      >
        <Descriptions.Item>
          <Table
            rowKey={record => {
              return record.id;
            }}
            columns={purchaseAssetColumns}
            dataSource={purchaseAsset}
            pagination={false}
          ></Table>
        </Descriptions.Item>
      </Descriptions>

      <Descriptions title="审批信息" column={{ xs: 1, sm: 2, lg: 2, xl: 2 }} layout="vertical" bordered>
        <Descriptions.Item label="合同信息提交">
          提交人: {contractDetail?.author}
          <br />
          提交时间:
          {contractDetail?.submitTime && moment(contractDetail.submitTime).format("YYYY-MM-DD")}
        </Descriptions.Item>
        <Descriptions.Item label="合同信息审核">
          审核人: {contractDetail?.auditor}
          <br />
          审核时间: {contractDetail?.approvalTime && moment(contractDetail.approvalTime).format("YYYY-MM-DD")}
          <br />
          审核结果:{contractDetail?.approvalResult}
        </Descriptions.Item>
      </Descriptions>

      <div className="contract-footer">
        <Button
          type="primary"
          disabled={loading}
          onClick={() => {
            updateContractDetail();
          }}
        >
          暂存
        </Button>
        <Button
          disabled={!updateState}
          onClick={() => {
            submitContract();
          }}
          type="primary"
        >
          提交审核
        </Button>
      </div>

      <Modal
        closable={false}
        centered
        title={editType.current && editType.current.title}
        open={editState}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("contract-detail")[0]}
        width="500"
      >
        {editType.current?.type == "baseInfoForm" && (
          <>
            <div className="site-uid">
              <label className="form__label">
                <span>
                  所属院区
                  <span aria-label="required">*</span>
                </span>
              </label>
              <div className="input-wrap">
                <TreeSelect
                  treeData={siteListData}
                  showSearch={true}
                  disabled={editType.current.model == "add"}
                  allowClear={true}
                  filterTreeNode={(input, option) => {
                    return (option?.title ?? "").toLowerCase().includes(input.toLowerCase());
                  }}
                  value={siteUID}
                  {...register("siteUID", {
                    required: "请选择院区",
                  })}
                  onChange={e => {
                    setSiteUID(e);
                  }}
                  placeholder={"选择所属院区"}
                />
                {errors.siteUID && <p className="error-info">{errors.siteUID.message}</p>}
              </div>
            </div>
            <Divider />
          </>
        )}

        <MyForm
          data={{
            ...contractDetail,
          }}
          onSubmitText="保存"
          options={formOption}
          oncancel={() => {
            cancelEdit();
          }}
          onSubmit={data => {
            savecompanydata(data);
          }}
        ></MyForm>
      </Modal>
    </div>
  );
}

// 递归将 label 字段改为 title 字段
function convertLabelToTitle(arr) {
  if (!Array.isArray(arr)) return arr;
  return arr.map(item => {
    const { label, children, ...rest } = item;
    const newItem = {
      ...rest,
      title: label,
    };
    if (children && Array.isArray(children)) {
      newItem.children = convertLabelToTitle(children);
    }
    return newItem;
  });
}

// 递归查找 siteListData 中 value 匹配的节点
function findNodeByValue(tree, value) {
  for (const node of tree || []) {
    if (node.value === value) return node;
    if (node.children && node.children.length) {
      const found = findNodeByValue(node.children, value);
      if (found) return found;
    }
  }
  return null;
}
export default ContractDetail;
