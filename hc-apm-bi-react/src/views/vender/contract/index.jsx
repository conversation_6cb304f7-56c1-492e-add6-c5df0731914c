import React, { useState, useEffect, useContext } from "react";
import { Table, Button, message } from "antd";
import { getContractList } from "@/service";
import { venderContext } from "../context/index";
import { contractTypeOption, getTitle } from "./contractConfig.jsx";

import "./index.less";

function Contract(props) {
  const { model } = useContext(venderContext);
  const [contractList, setContractList] = useState([]);
  const [page, setPage] = useState(0);
  const [contractTotal, setContractTotal] = useState(0);
  const getContract = async tenantId => {
    let contractData = await getContractList({
      tenantUid: tenantId,
      page,
      pageSize: 10,
    });
    if (contractData) {
      setContractList(contractData.content);
      setContractTotal(contractData.totalElements);
    } else {
      setContractList([]);
    }
  };
  const editContract = id => {
    if (model.hospital) {
      props.history.push(`/vender/contract/${id}`);
    } else {
      message.warning("暂无可用租户，请绑定后操作");
    }
  };
  useEffect(() => {
    if (model.hospital) {
      getContract(model.hospital);
    }
  }, [model, page]);

  const columns = [
    {
      title: "合同编号/档案号",
      dataIndex: "contractNum",
      key: "contractNum",
    },
    {
      title: "标的名称",
      dataIndex: "bidName",
      key: "bidName",
    },
    {
      title: "项目名称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: text => {
        switch (text) {
          case 1:
            return "新建";
          case 2:
            return "审核";
          case 3:
            return "退回";
          case 4:
            return "归档";
          default:
            return "-";
            break;
        }
      },
    },
    {
      title: "项目编号",
      dataIndex: "reportNum",
      key: "reportNum",
    },
    {
      title: "合同类型",
      dataIndex: "contractType",
      key: "contractType",
      render: text => {
        return getTitle(text, contractTypeOption);
      },
    },
    {
      title: "合同开始时间",
      dataIndex: "startDate",
      key: "startDate",
    },
    {
      title: "合同截止日期",
      dataIndex: "endDate",
      key: "endDate",
    },
    {
      title: "合同总额",
      dataIndex: "amount",
      key: "amount",
    },
    {
      title: "操作",
      dataIndex: "edit",
      key: "edit",
      render: (_, item) => {
        return (
          <a
            onClick={() => {
              editContract(item.id);
            }}
          >
            查看
          </a>
        );
      },
    },
  ];
  return (
    <div className="contract">
      {contractList && (
        <Table
          rowKey={record => {
            return record.id;
          }}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            position: ["bottomRight"],
            size: "small",
            pageSize: 10,
            total: contractTotal,
            onChange: (current, size) => {
              setPage(current - 1);
            },
          }}
          title={() => {
            return (
              <div className="table-title">
                合同列表
                <Button
                  onClick={() => {
                    editContract("0000");
                  }}
                  type="primary"
                >
                  新增合同
                </Button>
              </div>
            );
          }}
          columns={columns}
          dataSource={contractList}
        />
      )}
    </div>
  );
}

export default Contract;
