/** @format */
import React, { memo, useEffect, useState, useContext, useCallback } from "react";
import { renderRoutes } from "react-router-config";
import { withRouter } from "react-router-dom";
import { useQuery } from "@/components/Hooks";
import { logo_w } from "@/images/export";
import "./venderLayout.less";
import { Layout, Menu, Spin } from "antd";
import Head from "./Header";
import { getVenderInfo } from "@/service";
import { venderRoutes } from "../menu";
import { venderContext } from "../context/index";
const { Content, Sider } = Layout;

export default memo(
  withRouter(function VenderLayout(props) {
    const { setUserInfo, model, clearUserInfo } = useContext(venderContext);
    const [query, setQuery] = useQuery();
    const route = props.route;

    //登出清空信息
    const signOut = useCallback(() => {
      localStorage.removeItem("hc-apm-token");
      localStorage.removeItem("venderInfo");
      clearUserInfo();
    }, []);

    // 当前用户状态
    const checkUse = async () => {
      const userToken = localStorage.getItem("hc-apm-token");
      const venderInfo = localStorage.getItem("venderInfo");
      if (!venderInfo || !userToken) {
        props.history.push("/venderLogin");
      } else {
        setUserInfo(JSON.parse(venderInfo));
      }
    };
    useEffect(() => {
      checkUse();
    }, []);

    useEffect(() => {
      if (props.location.pathname === "/vender" || props.location.pathname === "/vender/") {
        props.history.push("/vender/index");
      }
    }, [props.history, props.location.pathname]);
    const creatMenu = obj => {
      let newMenu = obj
        .map(item => {
          if ((item.path == "/vender/userManage" && !model.isAdmin) || !item.isMenu) {
            return null;
          }
          let menuItem = {
            key: item.path,
            icon: item.icon && React.createElement(item.icon),
            label: `${item.label}`,
          };
          if (item.routes) {
            menuItem.children = creatMenu(item.routes);
          }
          return menuItem;
        })
        .filter(item => item !== null);
      return newMenu;
    };
    return (
      <Layout className="vender">
        <Sider collapsedWidth="0">
          <div
            className="logo"
            onClick={() => {
              props.history.push("/vender/index");
            }}
          >
            <img src={logo_w} alt="ge-logo" />
            GEHC APM
          </div>
          <Menu
            mode="inline"
            selectedKeys={[`${props.location.pathname}`]}
            items={creatMenu(venderRoutes)}
            onClick={it => {
              if (it.key == "/venderLogin") {
                signOut();
              }
              props.history.push(it.key);
            }}
          />
        </Sider>
        <Layout>
          <Head />
          <Spin spinning={model.webLoading} tip="Loading..." delay={100}>
            <Content>{route && renderRoutes(props.route.routes)}</Content>
          </Spin>
        </Layout>
      </Layout>
    );
  }),
);
