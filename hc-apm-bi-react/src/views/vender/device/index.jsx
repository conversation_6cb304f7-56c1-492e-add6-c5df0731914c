import React, { useState, useEffect, useContext } from "react";
import { Table, message, Upload, Modal } from "antd";
import rest from "@urls/axios";
import { venderContext } from "../context/index";
import { getAssetList, getVenderDeptList, saveAssetItem, getI8nMessages } from "@/service";
import { MyForm } from "@/components/MyForm";
import "./index.less";

function Device() {
  const { model, setUserInfo } = useContext(venderContext);
  const [assetList, setAssetList] = useState([]); //设备管理列表
  const [page, setPage] = useState(0);
  const [assetListTotal, setAssetListTotal] = useState(0);
  //
  const [editType, setEditType] = useState(null);
  const [editState, setEditState] = useState(false);
  const [assetItem, setAssetItem] = useState(); //清单item详情
  const [deptListData, setDeptListData] = useState(null); //清单详情使用科室选项
  const [i8nData, setI8nData] = useState(null);
  const [formConfig, setFormConfig] = useState(null); //清单item详情form表单配置
  const msgTypes = ["assetStatus", "assetUsage", "assetGroup", "assetValueCategory"].join(",");
  //form表单配置
  const formOptionObj = [
    {
      type: "input",
      name: "name",
      attribute: {
        label: { value: "设备名称", required: true },
        placeholder: "设备名称",
        rule: {
          required: "请输入名称",
        },
      },
    },
    {
      type: "input",
      name: "functionType",
      attribute: {
        label: { value: "设备型号" },
        placeholder: "设备型号",
      },
    },
    {
      type: "select",
      name: "assetGroup",
      attribute: {
        label: { value: "设备分类" },
        placeholder: "选择设备分类",
      },
    },
    {
      type: "select",
      name: "clinicalDeptId",
      attribute: {
        label: { value: "使用科室" },
        placeholder: "使用科室",
      },
    },
    {
      type: "select",
      name: "status",
      attribute: {
        label: { value: "当前状态" },
        placeholder: "选择设备当前状态",
        options: [
          {
            label: "正常",
            value: 1,
          },
          {
            label: "停机",
            value: 2,
          },
          {
            label: "限制功能使用",
            value: 3,
          },
          {
            label: "待报废",
            value: 4,
          },
          {
            label: "停用",
            value: 5,
          },
        ],
      },
    },
    {
      type: "DatePicker",
      name: "installDate",
      attribute: {
        allowClear: false,
        label: { value: "启用日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "input",
      name: "comments",
      attribute: {
        label: { value: "备注" },
        placeholder: "备注",
      },
    },
    {
      type: "input",
      name: "locationName",
      attribute: {
        placeholder: "请填写安装地址",
        rule: {
          maxLength: {
            value: 50,
            message: "安装地址长度不能超过50个字符",
          },
        },
        label: { value: "安装地址" },
      },
    },
    {
      type: "input",
      name: "serialNum",
      attribute: {
        placeholder: "请填写设备SN号",
        rule: {
          maxLength: {
            value: 20,
            message: "设备SN号长度不能超过20个字符",
          },
        },
        label: { value: "设备SN号" },
      },
    },

    {
      type: "input",
      name: "departNum",
      attribute: {
        placeholder: "请填写设备编号（院方）",
        rule: {
          maxLength: {
            value: 30,
            message: "设备编号长度不能超过30个字符",
          },
        },
        label: { value: "设备编号" },
      },
    },
    {
      type: "DatePicker",
      name: "manufactDate",
      attribute: {
        allowClear: false,
        label: { value: "出厂日期" },
        format: "YYYY-MM-DD",
      },
    },
    {
      type: "Upload",
      name: "attachments",
      attribute: {
        label: {
          value: "设备照片（出厂编号、设备整体外观、医院编号标签）",
        },
        progress: { strokeWidth: 0.1, showInfo: false },
        showUploadList: true,
        maxCount: 10,
        customRequest: () => {},
        beforeUpload: file => {
          let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
          console.log("file====", file);
          const doesMatchFileType =
            ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
          if (!doesMatchFileType) {
            message.warning(
              `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
              4,
            );
            return Upload.LIST_IGNORE;
          }
          const isLimitedSize = file.size / 1024 / 1024 < 50;
          if (!isLimitedSize) {
            message.warning("文件必须小于 50MB!");
            return Upload.LIST_IGNORE;
          }
        },
      },
    },
  ];

  // 上传附件服务
  const updateFiles = async filesArray => {
    if (!filesArray) {
      return null;
    }
    // 过滤本地需要上传服务器的文件
    let filterArray = filesArray.reduce((result, currentValue) => {
      if (currentValue.size) {
        result.push(currentValue);
      }
      return result;
    }, []);

    return Promise.all(
      filterArray.map(item => {
        return new Promise((resolve, reject) => {
          rest
            .file(item)
            .then(data => {
              const attachment = {
                name: data.objectName,
                objectStorageId: data.objectId,
                thumbnail: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                original: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                ...data,
              };
              console.log("attachment===", attachment);
              resolve(attachment);
            })
            .catch(() => {
              reject("上传失败");
            });
        });
      }),
    );
  };

  //获取设备列表
  const getAssetData = async () => {
    setUserInfo({ webLoading: true });
    let assetData = await getAssetList({
      page,
      pageSize: 10,
      tenantUid: model.hospital,
      isValid: true,
      status: "[1,2,3,4,5]",
    });
    console.log("assetData", assetData);
    if (assetData && !assetData.bizStatusCode) {
      setAssetList(assetData.content);
      setAssetListTotal(assetData.totalElements);
    }
    setUserInfo({ webLoading: false });
  };

  //编辑设备信息
  const editAssetInfo = async data => {
    console.log("editAssetInfo", data);
    setEditType({
      type: data.type,
      title: data.title,
    });
    setEditState(true);
    //获取科室
    const itemDeptListData = deptListData ? deptListData : await getVenderDeptList({ siteUid: data.data.siteUID });

    //获取i18信息
    const i8nMessagesData = i8nData
      ? i8nData
      : await getI8nMessages({
          msgTypes,
          siteId: data.data.siteId,
        });
    console.log("i8nMessagesData", i8nMessagesData);
    setI8nData(i8nMessagesData);
    //组装设备分类、设备状态select option
    const { assetGroup, assetStatus } = i8nMessagesData;
    assetGroup.map(item => {
      item.label = item.valueZh;
      item.value = Number(item.msgKey);
    });
    assetStatus.map(item => {
      item.label = item.valueZh;
      item.value = Number(item.msgKey);
    });
    //组装科室select option
    itemDeptListData.map(item => {
      item.label = item.name;
      item.value = item.id;
    });
    setDeptListData(itemDeptListData);
    //修改 formOptionObj 科室下拉配置
    formOptionObj.map(item => {
      switch (item.name) {
        case "clinicalDeptId":
          item["attribute"]["options"] = itemDeptListData;
          break;
        case "assetGroup":
          item["attribute"]["options"] = assetGroup;
          break;
        case "status":
          item["attribute"]["options"] = assetStatus;
          break;
        default:
          break;
      }
    });
    setFormConfig(formOptionObj);
    //更新form表单数据
    setAssetItem(data.data);
  };

  //保存设备信息
  const saveItemdata = async data => {
    const {
      name,
      functionType,
      assetGroup,
      clinicalDeptId,
      status,
      installDate,
      comments,
      locationName,
      serialNum,
      departNum,
      manufactDate,
      attachments,
      tenantUID,
      id,
    } = data;
    console.log("attachments", attachments);
    //保存前处理上传本地附件
    const [assetAttachment] = await Promise.all([updateFiles(attachments)]);
    let assetLocalArray = attachments?.reduce((result, currentValue) => {
      if (!currentValue.size) {
        result.push(currentValue);
      }
      return result;
    }, []);

    const objAttachments = [];
    if (assetAttachment) {
      assetAttachment.map(item => {
        item.boSubType = "attachments";
      });
      objAttachments.push(...assetLocalArray, ...assetAttachment);
    }

    console.log("objAttachments", objAttachments);
    //组装最后需要上传保存的数据
    let newobj = {
      name,
      functionType,
      assetGroup,
      clinicalDeptId,
      status,
      installDate,
      comments,
      locationName,
      serialNum,
      departNum,
      manufactDate,
      attachments: [...objAttachments],
      tenantUID,
      id,
    };
    console.log("newobj", newobj);
    const savestatus = await saveAssetItem(newobj);
    console.log("savestatus", savestatus);
    if (savestatus) {
      window.location.reload(false);
    }
  };

  useEffect(() => {
    getAssetData();
  }, [model.hospital, page]);

  const columns = [
    {
      title: "设备名称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "设备型号",
      dataIndex: "functionType",
      key: "functionType",
    },

    {
      title: "使用科室",
      dataIndex: "clinicalDeptName",
      key: "clinicalDeptName",
    },
    {
      title: "启用日期",
      dataIndex: "installDate",
      key: "installDate",
    },
    {
      title: "备注",
      dataIndex: "comments",
      key: "comments",
    },
    {
      title: "操作",
      dataIndex: "edit",
      key: "edit",
      render: (_, item) => {
        return (
          <a
            onClick={() => {
              editAssetInfo({
                type: "edit",
                title: "编辑设备信息",
                data: item,
              });
            }}
          >
            编辑
          </a>
        );
      },
    },
  ];
  return (
    <div className="asset-device">
      {assetList && (
        <Table
          rowKey={record => {
            return record.id;
          }}
          onRow={record => {
            return {
              onClick: event => {
                console.log("record", record);
              },
            };
          }}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            position: ["bottomRight"],
            pageSize: 10,
            size: "small",
            total: assetListTotal,
            onChange: (current, size) => {
              setPage(current - 1);
            },
          }}
          title={() => {
            return <div className="table-title">设备管理</div>;
          }}
          columns={columns}
          dataSource={assetList}
        />
      )}
      <Modal
        closable={false}
        centered
        title={editType?.title}
        open={editState && formConfig}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("asset-device")[0]}
        width="500"
      >
        <MyForm
          data={{
            ...assetItem,
          }}
          options={formConfig}
          oncancel={() => {
            setEditState(false);
          }}
          onSubmit={data => {
            saveItemdata(data);
          }}
        ></MyForm>
      </Modal>
    </div>
  );
}

export default Device;
