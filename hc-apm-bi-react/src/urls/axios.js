import axios from "axios";
import urls from "./url";
// npm run build --public_path=path:/geapm/reports/ --base_url=path:/geapm/gateway
// process.env.npm_config_base_url: 'path:/geapm/gateway'
const baseURL = process.env.npm_config_base_url; // 来自webpack.DefinePlugin因为是非node环境不能直接调用process

const instanceReturnData = axios.create(); // 这个实例响应拦截里返回response.data.data
const instanceReturnresponse = axios.create(); // 这个实例响应拦截返回response.data
instanceReturnData.defaults.baseURL = baseURL;
instanceReturnData.defaults.timeout = 30 * 1000;
instanceReturnData.defaults.headers.post["Content-Type"] = "application/x-www-form-urlencoded";

instanceReturnData.interceptors.request.use(
  config => {
    const token = localStorage.getItem("hc-apm-token");
    if (token) {
      // 判断是否存在token，如果存在的话，则每个http header都加上token
      config.headers.authorization = token; //请求头加上token
      config.headers.uuid = "1234567890"; //与后端进行校验匹配
      config.headers.version = "v1"; //与后端进行校验匹配
    }
    return config;
  },
  err => {
    return Promise.reject(err);
  },
);
instanceReturnresponse.defaults.baseURL = baseURL;
instanceReturnresponse.defaults.timeout = 30 * 1000;
instanceReturnresponse.defaults.headers.post["Content-Type"] = "application/x-www-form-urlencoded";

instanceReturnresponse.interceptors.request.use(
  config => {
    const token = localStorage.getItem("hc-apm-token");
    if (token) {
      // 判断是否存在token，如果存在的话，则每个http header都加上token
      config.headers.authorization = token; //请求头加上token
      config.headers.uuid = "1234567890"; //与后端进行校验匹配
      config.headers.version = "v1"; //与后端进行校验匹配
    }
    return config;
  },
  err => {
    return Promise.reject(err);
  },
);
// 添加响应拦截器
instanceReturnData.interceptors.response.use(
  function (response) {
    const { data, config } = response;
    const { list } = config;
    if (data && data.bizStatusCode !== undefined && data.bizStatusCode !== "OK" && data.message) {
      return null;
    }

    if (data && typeof data.data !== "undefined" && data.data) {
      return list ? data : data.data;
    } else {
      return data;
    }

    // 对响应数据做点什么
    // if (response.status == "200" && response.statusText == "OK") {
    //     return response.data.data || response.data;
    // } else {
    //     return null;
    // }
  },
  function (error) {
    // 对响应错误做点什么
    return Promise.reject(error);
  },
);
// 添加响应拦截器
instanceReturnresponse.interceptors.response.use(
  function (response) {
    // 对响应数据做点什么
    return response.data;
  },
  function (error) {
    // 对响应错误做点什么
    return Promise.reject(error);
  },
);
const rest = {};
export const restOrigin = {};
// 封装axios请求，返回promise
rest.get = (url, data = {}, list = false) => {
  return new Promise((resolve, reject) => {
    instanceReturnData
      .get(url, { params: data, list })
      .then(res => {
        resolve(res);
      })
      .catch(error => {
        reject(error);
      });
  });
};

rest.post = (url, data) => {
  return new Promise((resolve, reject) => {
    instanceReturnData
      .post(url, data)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
rest.put = (url, data) => {
  return new Promise((resolve, reject) => {
    instanceReturnData
      .put(url, data)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

rest.delete = (url, data) => {
  return new Promise((resolve, reject) => {
    instanceReturnData
      .delete(url, data)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

rest.file = (file, { filename } = {}) => {
  const formData = new FormData();
  formData.append("file", file, filename);
  return new Promise((resolve, reject) => {
    instanceReturnData
      .post(urls.objSingle, formData)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
rest.files = files => {
  const formData = new FormData();
  formData.append("file", files);
  return new Promise((resolve, reject) => {
    instanceReturnData
      .post(urls.objMutiple, formData)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
// 封装axios请求，返回promise
restOrigin.get = (url, data = {}) => {
  return new Promise((resolve, reject) => {
    instanceReturnresponse
      .get(url, { params: data })
      .then(res => {
        resolve(res);
      })
      .catch(error => {
        reject(error);
      });
  });
};

restOrigin.post = (url, data) => {
  return new Promise((resolve, reject) => {
    instanceReturnresponse
      .post(url, data)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
restOrigin.put = (url, data) => {
  return new Promise((resolve, reject) => {
    instanceReturnresponse
      .put(url, data)
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

export default rest;
