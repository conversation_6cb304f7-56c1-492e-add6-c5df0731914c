{"name": "offline_solution", "scripts": {"start": "cross-env flavor=dev node appid_config.js && flutter run --hot", "build-android:prod": "cross-env flavor=prod node appid_config.js && flutter clean && flutter pub get && flutter build apk && cross-env env=pre node upload_to_obs.js", "build-android:dev": "cross-env flavor=dev node appid_config.js && flutter build apk && cross-env env=dev node upload_to_obs.js", "build-android:pre": "cross-env flavor=pre node appid_config.js && flutter build apk && cross-env env=pre node upload_to_obs.js", "dev": "cross-env flavor=dev node appid_config.js", "prod": "cross-env flavor=prod node appid_config.js", "build:ios": "xcodebuild -workspace ./ios/Runner.xcworkspace -scheme Runner -sdk iphoneos -configuration AppStoreDistribution archive -archivePath $PWD/build/myios/Runner.xcarchive", "build-ios:prodUpload": "cross-env flavor=prod version=add node appid_config.js && npm run build:ios && xcodebuild -exportArchive -archivePath $PWD/build/myios/Runner.xcarchive -exportOptionsPlist $PWD/ios/export_prod.plist -exportPath $PWD/build/myios/prod && xcrun altool --upload-app -f $PWD/build/myios/prod/apm.ipa -u <EMAIL> -p poos-zwik-zuyo-bten -type ios  --verbose", "build-ios:preUpload": "cross-env flavor=pre version=add node appid_config.js && npm run build:ios && xcodebuild -exportArchive -archivePath $PWD/build/myios/Runner.xcarchive -exportOptionsPlist $PWD/ios/export_prod.plist -exportPath $PWD/build/myios/prod && xcrun altool --upload-app -f $PWD/build/myios/prod/apm.ipa -u <EMAIL> -p poos-zwik-zuyo-bten -type ios  --verbose", "build-ios:devUpload": "cross-env flavor=dev node appid_config.js && npm run build:ios && xcodebuild -exportArchive -archivePath $PWD/build/myios/Runner.xcarchive -exportOptionsPlist $PWD/ios/export_dev.plist -exportPath $PWD/build/myios/dev && xcrun altool --upload-app -f $PWD/build/myios/dev/apm.ipa -u <EMAIL> -p poos-zwik-zuyo-bten -type ios  --verbose", "zzhou": "cross-env flavor=zz node appid_config.js && flutter build apk", "czhou": "cross-env flavor=cz node appid_config.js && flutter build apk"}, "author": "", "license": "MIT", "devDependencies": {"cross-env": "^7.0.3", "esdk-obs-nodejs": "^3.21.6", "fs": "0.0.1-security"}}