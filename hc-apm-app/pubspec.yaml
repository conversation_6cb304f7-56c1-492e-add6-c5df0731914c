name: offline_solution
description: offline solution for apm.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.9+1

environment:
  sdk: ">=2.12.0 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  equatable: ^2.0.3
  intl: ^0.17.0
  dio: ^4.0.6
  dio_http2_adapter: ^2.0.0
  permission_handler: ^9.2.0
  flutter_native_splash: ^2.0.5
  flutter_launcher_icons: ^0.9.2
  flutter_bloc: ^8.1.1
  image_picker: ^0.8.5+3
  animated_text_kit: ^4.2.2
  flutter_screenutil: ^5.6.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  badges: ^2.0.2
  flutter_form_builder: ^7.6.0
  dropdown_search: ^5.0.2
  day_night_time_picker: ^1.0.5
  animations: ^2.0.2
  connectivity_plus: ^2.3.2
  infinite_scroll_pagination: ^3.2.0
  bloc_concurrency: ^0.2.0
  cupertino_icons: ^1.0.2
  flutter_slidable: ^1.3.0
  shimmer: ^2.0.0
  expandable: ^5.0.1
  animate_do: ^2.0.0
  flutter_easyloading: ^3.0.3
  fluttertoast: ^8.0.8
  json_annotation: ^4.3.0
  freezed_annotation: ^2.0.3
  flutter_spinkit: ^5.1.0
  flutter_image_compress: ^1.1.0
  image_size_getter: ^2.3.0
  jpush_flutter: ^2.3.8
  tab_indicator_styler: ^2.0.0
  extended_image: ^7.0.1
  dio_cookie_manager: ^2.0.0
  cookie_jar: ^3.0.1
  background_fetch: 1.1.0
  webview_flutter: ^4.2.2
  path: ^1.8.0
  mime: ^1.0.2
  http_parser: ^4.0.0
  url_launcher: ^6.1.2
  mobile_scanner: ^3.4.0
  carousel_slider: ^4.1.1
  flutter_staggered_animations: ^1.0.0
  flutter_staggered_grid_view: ^0.6.1
  flutter_sound: ^9.2.13
  path_provider: ^2.0.10
  audio_session: ^0.1.7
  archive: ^3.3.0
  wechat_assets_picker: ^8.0.4
  flutter_html: ^3.0.0-alpha.3
  video_player: ^2.4.2
  flutter_treeview: ^1.0.7+1
  open_file: ^3.2.1
  file: ^6.1.4
  path_provider_ios: ^2.0.11
  package_info_plus: ^4.0.2
  flutter_markdown: ^0.6.15
  flutter_downloader: ^1.10.4
  file_picker: ^5.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.1.10
  hive_generator: ^1.1.2
  json_serializable: ^6.0.1
  freezed: ^2.0.3+1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0
dependency_overrides:
  webview_flutter_wkwebview: "3.5.0"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_icons:
  android: true
  ios: true
  image_path: "lib/assets/icon.png"
flutter_native_splash:
  # This package generates native code to customize Flutter's default white native splash screen
  # with background color and splash image.
  # Customize the parameters below, and run the following command in the terminal:
  # flutter pub run flutter_native_splash:create
  # To restore Flutter's default white splash screen, run the following command in the terminal:
  # flutter pub run flutter_native_splash:remove
  # color or background_image is the only required parameter.  Use color to set the background
  # of your splash screen to a solid color.  Use background_image to set the background of your
  # splash screen to a png image.  This is useful for gradients. The image will be stretch to the
  # size of the app. Only one parameter can be used, color and background_image cannot both be set.
  color: "#2271b2"
  image: lib/assets/logo.png
  fullscreen: true
# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - lib/assets/
    - lib/assets/images/
    - lib/assets/video/
    - lib/assets/wx/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: geicon
      fonts:
        - asset: lib/assets/fonts/iconfont.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
