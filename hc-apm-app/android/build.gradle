buildscript {
    ext.kotlin_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.huawei.agconnect:agcp:1.5.2.300'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
          url "${project(':background_fetch').projectDir}/libs"
        }
        maven {url 'https://developer.huawei.com/repo/'}
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
    project.configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'com.android.support'
                    && !details.requested.name.contains('multidex') ) {
                details.useVersion "27.1.1"
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
