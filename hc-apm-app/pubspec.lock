# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "4897882604d919befd350648c7f91926a9d5de99e67b455bf0917cc2362f4bb8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "47.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "690e335554a8385bc9d787117d9eb52c0c03ee207a607e593de3c9d71b1cfe80"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.7.0"
  animate_do:
    dependency: "direct main"
    description:
      name: animate_do
      sha256: "4554744e604b841e2bde710398ef3f4f8a1935a182b48727afe2a1023527b883"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  animated_text_kit:
    dependency: "direct main"
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.2"
  animations:
    dependency: "direct main"
    description:
      name: animations
      sha256: fe8a6bdca435f718bb1dc8a11661b2c22504c6da40ef934cee8327ed77934164
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.7"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: "0c8368c9b3f0abbc193b9d6133649a614204b528982bebc7026372d61677ce3a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.7"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.10.0"
  audio_session:
    dependency: "direct main"
    description:
      name: audio_session
      sha256: "8a2bc5e30520e18f3fb0e366793d78057fb64cd5287862c76af0c8771f2a52ad"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.16"
  background_fetch:
    dependency: "direct main"
    description:
      name: background_fetch
      sha256: "70924729bd7afd80473637e9acb3d199e526b6d87242f478358e5be3161c326c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: "727580d938b7a1ff47ea42df730d581415606b4224cfa708671c10287f8d3fe6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "3820f15f502372d979121de1f6b97bfcf1630ebff8fe1d52fb2b0bfa49be5b49"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.1.2"
  bloc_concurrency:
    dependency: "direct main"
    description:
      name: bloc_concurrency
      sha256: "44535c9f429cd7e91d548cf89fde1c23a8b4b3637decdb1865bb583091a00d4e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "3fbda25365741f8251b39f3917fb3c8e286a96fd068a5a242e11c2012d495777"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "757153e5d9cd88253cb13f28c2fb55a537dc31fefd98137549895b5beb7c6169"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "687cf90a3951affac1bd5f9ecb5e3e90b60487f3d9cdc359bb310f8876bb02a6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.10"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: b0a8a7b8a76c493e85f1b84bffa0588859a06197863dba8c9036b15581fd9727
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.3"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "0671ad4162ed510b70d0eb4ad6354c249f8429cab4ae7a4cec86bbc2886eb76e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.2.7+1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "598a2a682e2a7a90f08ba39c0aaa9374c5112340f0a2e275f61b59389543d166"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.6.1"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "4ad01d6e56db961d29661561effde45e519939fdaeb46c351275b182eac70189"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.17.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: "3f8fe4e504c2d33696dac671a54909743bc6a902a9bb0902306f7a2aed7e528e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.9"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      sha256: "3caf859d001f10407b8e48134c761483e4495ae38094ffcca97193f6c271f5e2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      sha256: "488d2de1e47e1224ad486e501b20b088686ba1f4ee9c4420ecbc3b9824f0b920"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.4"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      sha256: "81332be1b4baf8898fed17bb4fdef27abb7c6fd990bf98c54fd978478adf2f1a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.5"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      sha256: "535b0404b4d5605c4dd8453d67e5d6d2ea0dd36e3b477f50f31af51b0aeab9dd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.2"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  cookie_jar:
    dependency: "direct main"
    description:
      name: cookie_jar
      sha256: d1cc6516a190ba667941f722b6365d202caff3dacb38de24268b8d6ff1ec8a1d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "0b0036e8cccbfbe0555fd83c1d31a6f30b77a96b598b35a5d36dd41f718695e9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.5"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7a03456c3490394c8e7665890333e91ae8a49be43542b616e414449ac358acd4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.4"
  day_night_time_picker:
    dependency: "direct main"
    description:
      name: day_night_time_picker
      sha256: f7606d140a05fd22b2b37485371d6342ef1043d15d5b0ad254a3776d1ea81f80
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0+1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "6f07cba3f7b3448d42d015bfd3d53fe12e5b36da2423f23838efc1d5fb31a263"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.7.8"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.6"
  dio_cookie_manager:
    dependency: "direct main"
    description:
      name: dio_cookie_manager
      sha256: ed7ee3ba6cdb54599c8984d5a4ce09675c553ead6c28608eb54e38eec5b4f954
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  dio_http2_adapter:
    dependency: "direct main"
    description:
      name: dio_http2_adapter
      sha256: f4ce5e29aaf24c6c1b99b2135c8f4f03c3e2f1b7b535bbfdc859f0c5a74a8298
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  dropdown_search:
    dependency: "direct main"
    description:
      name: dropdown_search
      sha256: "55106e8290acaa97ed15bea1fdad82c3cf0c248dd410e651f5a8ac6870f783ab"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.6"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.5"
  expandable:
    dependency: "direct main"
    description:
      name: expandable
      sha256: "9604d612d4d1146dafa96c6d8eec9c2ff0994658d6d09fed720ab788c7f5afc2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.1"
  extended_image:
    dependency: "direct main"
    description:
      name: extended_image
      sha256: "75e4b0ad0f8f63eed7935ff2506c809a670f5e6dd0f61304525879d53fc41a17"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.2"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "550743b43ab093aed35ef234500fcc7a304cbac1eca47b0cc991e07e88750758"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.2"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  file:
    dependency: "direct main"
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.4"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "9d6e95ec73abbd31ec54d0e0df8a961017e165aba1395e462e5b31ea0c165daf"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.3.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "770eb1ab057b5ae4326d1c24cc57710758b9a46026349d021d6311bd27580046"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "4ada532862917bf16e3adb3891fe3a5917a58bae03293e497082203a80909412"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.3+1"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "412705a646a0ae90f33f37acfae6a0f7cbc02222d6cd34e479421c3e74d3853c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.6.0"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "1372760c6b389842b77156203308940558a2817360154084368608413835fc26"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.3"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: e74efb89ee6945bcbce74a5b3a5a3376b088e5f21f55c263fc38cbdc6237faae
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.1.3"
  flutter_downloader:
    dependency: "direct main"
    description:
      name: flutter_downloader
      sha256: "3d7313746c5f376b2209a59888a1c2766d94e09c26dc93fa23a725426e3f341c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.5"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.5"
  flutter_form_builder:
    dependency: "direct main"
    description:
      name: flutter_form_builder
      sha256: "9551c7379adc01a3a3a1100057396407c9534ea8adc937d14a0edd96fcd9e1dc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.8.0"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_image_compress:
    dependency: "direct main"
    description:
      name: flutter_image_compress
      sha256: "37f1b26399098e5f97b74c1483f534855e7dff68ead6ddaccf747029fb03f29f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.3"
  flutter_launcher_icons:
    dependency: "direct main"
    description:
      name: flutter_launcher_icons
      sha256: "559c600f056e7c704bd843723c21e01b5fba47e8824bd02422165bcc02a5de1d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: b543301ad291598523947dc534aaddc5aaad597b709d2426d3a0e0d44c5cb493
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_markdown:
    dependency: "direct main"
    description:
      name: flutter_markdown
      sha256: dc6d5258653f6857135b32896ccda7f7af0c54dcec832495ad6835154c6c77c0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.15"
  flutter_native_splash:
    dependency: "direct main"
    description:
      name: flutter_native_splash
      sha256: "6777a3abb974021a39b5fdd2d46a03ca390e03903b6351f21d10e7ecc969f12d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.16"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "950e77c2bbe1692bc0874fc7fb491b96a4dc340457f4ea1641443d0a6c1ea360"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.15"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "0a122936b450324cbdfd51be0819cc6fcebb093eb65585e9cd92263f7a1a8a39"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.7.0"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: "90787334388211e6810065550119b015e6fd3893584641194d500bf5bc7e6235"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  flutter_sound:
    dependency: "direct main"
    description:
      name: flutter_sound
      sha256: "090a4694b11ecc744c2010621c4ffc5fe7c3079d304ea014961a72c7b72cfe6c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.2.13"
  flutter_sound_platform_interface:
    dependency: transitive
    description:
      name: flutter_sound_platform_interface
      sha256: "4537eaeb58a32748c42b621ad6116f7f4c6ee0a8d6ffaa501b165fe1c9df4753"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.2.13"
  flutter_sound_web:
    dependency: transitive
    description:
      name: flutter_sound_web
      sha256: ad4ca92671a1879e1f613e900bbbdb8170b20d57d1e4e6363018fe56b055594f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.2.13"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: b39c753e909d4796906c5696a14daf33639a76e017136c8d82bf3e620ce5bb8e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.0"
  flutter_staggered_animations:
    dependency: "direct main"
    description:
      name: flutter_staggered_animations
      sha256: "81d3c816c9bb0dca9e8a5d5454610e21ffb068aedb2bde49d2f8d04f75538351"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.2"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_treeview:
    dependency: "direct main"
    description:
      name: flutter_treeview
      sha256: ce7a66452e02877700890cb674773ea0af28d914192acfb5bf55a50ce35b5819
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.7+1"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "474f7d506230897a3cd28c965ec21c5328ae5605fc9c400cd330e9e9d6ac175c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.2.2"
  freezed:
    dependency: "direct dev"
    description:
      name: freezed
      sha256: "4179d41127bc7a67dc3f58ceec1d22f1cdf10470653cb86eda2a63f81b4920c7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  freezed_annotation:
    dependency: "direct main"
    description:
      name: freezed_annotation
      sha256: c3fd9336eb55a38cc1bbd79ab17573113a8deccd0ecbbf926cca3c62803b5c2d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.1"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  hive:
    dependency: "direct main"
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  hive_generator:
    dependency: "direct dev"
    description:
      name: hive_generator
      sha256: "81fd20125cb2ce8fd23623d7744ffbaf653aae93706c9bd3bf7019ea0ace3938"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.3"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.6"
  http2:
    dependency: transitive
    description:
      name: http2
      sha256: "58805ebc6513eed3b98ee0a455a8357e61d187bf2e0fdc1e53120770f78de258"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "14c6e756644339f561321dab021215475ba4779aa962466f59ccb3ecf66b36c3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.4"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: "direct main"
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: b6951e25b795d053a6ba03af5f710069c99349de9341af95155d52665cb4607c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.9"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8179b54039b50eee561676232304f487602e2950ffb3e8995ed9034d6505ca34"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.7+4"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: b3e2f21feb28b24dd73a35d7ad6e83f568337c70afab5eabac876e23803f264b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.8"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "02cbc21fe1706b97942b575966e5fbbeaac535e76deef70d3a242e4afb857831"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: cee2aa86c56780c13af2c77b5f2f72973464db204569e1ba2dd744459a065af4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: c1134543ae2187e85299996d21c526b2f403854994026d575ae4cf30d7bb2a32
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.9.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: c3066601ea42113922232c7b7b3330a2d86f029f685bba99d82c30e799914952
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  image_size_getter:
    dependency: "direct main"
    description:
      name: image_size_getter
      sha256: c3900f04e31468a81aa23b09b1c7d09c3c952122bbe9ecdae336977b7be24c05
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0+1"
  infinite_scroll_pagination:
    dependency: "direct main"
    description:
      name: infinite_scroll_pagination
      sha256: "9517328f4e373f08f57dbb11c5aac5b05554142024d6b60c903f3b73476d52db"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  jpush_flutter:
    dependency: "direct main"
    description:
      name: jpush_flutter
      sha256: a5f6b7f622a2c48cd1c6702598f80781dd7d1069bbb7177774a10cf0efbba313
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.7"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: "3520fa844009431b5d4491a5a778603520cdc399ab3406332dcc50f93547258c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.7.0"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: f3c2c18a7889580f71926f30c1937727c8c7d4f3a435f8f5e8b0ddd25253ef5d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.5.4"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: a2c3d198cb5ea2e179926622d433331d8b58374ab8f29cdda6e863bd62fd369c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: "7ad7215c15420a102ec687bb320a7312afd449bac63bfb1c60d9787c27b9767f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: acf35edccc0463a9d7384e437c015a3535772e09714cf60e07eeef3a15870dcd
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.1.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  mobile_scanner:
    dependency: "direct main"
    description:
      name: mobile_scanner
      sha256: "2fbc3914fe625e196c64ea8ffc4084cd36781d2be276d4d5923b11af3b5d44ff"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.0"
  open_file:
    dependency: "direct main"
    description:
      name: open_file
      sha256: a5a32d44acb7c899987d0999e1e3cbb0a0f1adebbf41ac813ec6d2d8faa0af20
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "6ff267fcd9d48cb61c8df74a82680e8b82e940231bb5f68356672fde0397334a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.2"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "909b84830485dbcd0308edf6f7368bc8fd76afa26a270420f34cabea2a6467a0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "5d44fc3314d969b84816b569070d7ace0f1dea04bd94a83f74c4829615d22ad8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "1b744d3d774e5a879bb76d6cd1ecee2ba2c6960c03b1020cd35212f6aa267ac5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  path_provider_ios:
    dependency: "direct main"
    description:
      name: path_provider_ios
      sha256: "03d639406f5343478352433f00d3c4394d52dac8df3d847869c5e2333e0bbce8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.11"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: ba2b77f0c52a33db09fc8caf85b12df691bf28d983e84cf87ff6d693cfa007b3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: bced5679c7df11190e1ddc35f3222c858f328fff85c3942e46e7f5589bf9eb84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: ee0e0d164516b90ae1f970bdf29f726f1aa730d7cfc449ecc74c495378b705da
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "5749ebeb7ec0c3865ea17e3eb337174b87747be816dab582c551e1aff6f6bbf3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.2.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: a512e0fa8abcb0659d938ec2df93a70eb1df1fdea5fdc6d79a866bfd858a28fc
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.0.2+1"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "7c6b1500385dd1d2ca61bb89e2488ca178e274a69144d26bbd65e33eae7c02a9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.11.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.1.0"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: b2d81bd197323697d1b335e2e04cea2f67e11624ced77cfd02917a10afaeba73
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.7.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "57c07bf82207aee366dfaa3867b3164e4f03a238a461a11b0e8a3a510d51203d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "43798d895c929056255600343db8f049921cbec94d31ec87f1dc5c16c01935dd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.5"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: cdbe7530b12ecd9eb455bdaa2fcb8d4dad22e80b8afb4798b41479d5ce26847f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.5"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "75f6614d6dde2dc68948dffbaa4fe5dae32cd700eb9fb763fe11dfb45a3c4d0a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  recase:
    dependency: transitive
    description:
      name: recase
      sha256: e4eb4ec2dcdee52dcf99cb4ceabaffc631d7424ee55e56f280bc039737f89213
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.7"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.12"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "2d79738b6bbf38a43920e2b8d189e9a3ce6cc201f4b8fc76be5e4fe377b1c38d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.6"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "3b67aade1d52416149c633ba1bb36df44d97c6b51830c2198e934e3fca87ca1f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.3"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  tab_indicator_styler:
    dependency: "direct main"
    description:
      name: tab_indicator_styler
      sha256: "9e7e90367e20f71f3882fc6578fdcced35ab1c66ab20fcb623cdcc20d2796c76"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.16"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "06866290206d196064fd61df4c7aea1ffe9a4e7c4ccaa8fcded42dd41948005d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: eb1e00ab44303d50dd487aab67ebc575456c146c6af44422f9c13889984c00f3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.11"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "3dd2388cc0c42912eee04434531a26a82512b9cb1827e0214430c9bcbddfe025"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.38"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "9af7ea73259886b92199f9e42c116072f05ff9bea2dcb339ab935dfc957392c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.4"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "207f4ddda99b95b4d4868320a352d374b0b7e05eefad95a4a26f57da413443f5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.5"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "1c4fdc0bfea61a70792ce97157e5cc17260f61abbe4f39354513f39ec6fd73b1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.6"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: bfdfa402f1f3298637d71ca8ecfe840b4696698213d5346e9d12d4ab647ee2ea
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.3"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: cc26720eefe98c1b71d85f9dc7ef0cada5132617046369d9dc296b3ecaa5cbb4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.18"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7967065dd2b5fccc18c653b97958fdf839c5478c28e767c61ee879f4e7882422"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.7"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "3fd106c74da32f336dc7feb65021da9b0207cb3124392935f1552834f7cce822"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.7.0"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: f338a5a396c845f4632959511cad3542cdf3167e1b2a1a948ef07f7123c03608
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.9"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: f5f5b7fe8c865be8a57fe80c2dca130772e1db775b7af4e5c5aa1905069cfc6c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.9"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "1ca9acd7a0fb15fb1a990cb554e6f004465c6f37c99d2285766f08a4b2802988"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "44ce41424d104dfb7cf6982cc6b84af2b007a24d126406025bf40de5d481c74c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.16"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "789d52bd789373cc1e100fb634af2127e86c99cf9abde09499743270c5de8d00"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.2"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: bca797abba472868655b5f1a6029c1132385685ee9db4713cb0e7f33076210c6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.9.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "0ca3cfcc6781a7de701d580917af4a9efc4e3e129f8ead95a80587f0a749480a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.5.0"
  webview_flutter_wkwebview:
    dependency: "direct overridden"
    description:
      name: webview_flutter_wkwebview
      sha256: ecc9e9ea15216afc5ba3b1f14aa19414ceba526e57b19cebd970bfa91a0f4058
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.5.0"
  wechat_assets_picker:
    dependency: "direct main"
    description:
      name: wechat_assets_picker
      sha256: "7f391e1f4a2bc7344445913206008c76c8378362888a5959901f037606159c33"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.4.3"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: f0c26453a2d47aa4c2570c6a033246a3fc62da2fe23c7ffdd0a7495086dc0247
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">2.19.0 <3.0.0"
  flutter: ">=3.7.0"
