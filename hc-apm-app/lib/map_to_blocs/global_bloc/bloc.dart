import 'dart:io';
import 'dart:async';
import 'dart:convert';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:offline_solution/app/bloc_wraper.dart';
import 'package:offline_solution/config/global.dart';
import 'package:offline_solution/map_to_blocs/offline_bloc/bloc.dart';
import 'package:offline_solution/models/org_info/org_info.dart';
import 'package:offline_solution/models/user_info/user_info.dart';
import 'package:offline_solution/utils/cache.dart';
import 'package:offline_solution/generated/l10n.dart';
import 'package:offline_solution/map_to_blocs/map_blocs.dart';
import 'package:offline_solution/utils/executor/executor.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:archive/archive_io.dart';
import 'package:offline_solution/utils/utils.dart';
import 'package:offline_solution/map_to_blocs/notification_bloc/bloc/bloc.dart';
import 'package:offline_solution/widgets/update_version_alert.dart';

part 'event.dart';
part 'state.dart';

int _loadcount = 0;
final fetch = Dio();

class GlobalBloc extends Bloc<GlobalEvent, GlobalState> {
  GlobalBloc(GlobalState globalState) : super(globalState) {
    on<TranslateEvent>(_translataChange, transformer: droppable());
    on<InitDioInstanceEvent>(_initDioInstance);
    on<InitUserEvent>(_initUserEvent, transformer: restartable());
    on<SetWarningEvent>(_setWarningEvent, transformer: sequential());
    on<RemoveWarningByKey>(_removeWarningByKey, transformer: sequential());
    on<ToastErrorEvent>(_toastErrorEvent, transformer: sequential());
    on<ToastSuccessEvent>(_toastSuccessEvent, transformer: sequential());
    on<InitDirectoryEvent>(_initDirectoryEvent, transformer: droppable());
    on<InitWorkBenchEvent>(_initWorkBenchEvent, transformer: droppable());
    on<LogoutEvent>(_logoutEvent, transformer: droppable());
    on<VersionCheckEvent>(_versionCheckEvent, transformer: droppable());
  }

  _translataChange(TranslateEvent event, emit) async {
    setLocal(String _local) async {
      await S.load(Locale(_local));
      await state.cookiejar
          .saveFromResponse(Uri.parse(baseUrl), [Cookie("locale", _local)]);
      emit(state.copyWith(local: _local));
    }

    String? local = event.local;
    if (local == null) {
      var language = Intl.getCurrentLocale();
      var _local = language == "en_US" ? "zh" : "en";
      if (_local != state.local) {
        await setLocal(_local);
      }
    } else {
      var _local = local == "en_US" ? "zh" : "en";
      await setLocal(_local);
    }
  }

  _initDioInstance(InitDioInstanceEvent event, emit) {
    parseJson(String text) {
      return json.decode(text, reviver: (k, v) {
        if (v is int) {
          return v.toDouble();
        }
        return v;
      });
    }

    state.cookiejar.saveFromResponse(Uri.parse(baseUrl),
        [Cookie("locale", (state.local ?? 'en').split('_')[0])]);
    state.dio.httpClientAdapter = DefaultHttpClientAdapter()
      ..onHttpClientCreate = (httpClient) => httpClient
        ..maxConnectionsPerHost = limitFutureOfRequest
        ..badCertificateCallback = (cert, host, port) => true;
    (state.dio.transformer as DefaultTransformer).jsonDecodeCallback =
        parseJson;
    state.dio.interceptors
      ..add(CookieManager(state.cookiejar))
      ..add(InterceptorsWrapper(onRequest: (options, handler) {
        UserInfo? user = UserInfo.fromJson(getSyncBoxJsonByName(userInfoTable));
        options.headers["Authorization"] =
            options.headers["Authorization"] ?? user.data?.jwtToken?.idToken;
        if (options.extra["loading"] == true) {
          EasyLoading.show(
              maskType: EasyLoadingMaskType.black,
              status: options.extra["loadingText"]);
          _loadcount++;
        }

        handler.next(options);
      }, onResponse: (response, handler) {
        Map extra = response.requestOptions.extra;
        if (extra["loading"] == true) {
          dismissLoading();
        }
        handler.next(response);
      }, onError: (DioError e, handler) {
        Fluttertoast.showToast(
            msg: (e.response?.data is Map)
                ? e.response?.data["message"] ?? 'check your network'
                : e.message,
            gravity: ToastGravity.TOP,
            backgroundColor: Colors.white,
            textColor: Colors.red,
            fontSize: 20.sp);
        Map extra = e.requestOptions.extra;
        if (extra["loading"] == true) {
          dismissLoading();
        }
        handler.next(e);
      }));
  }

  void dismissLoading() {
    _loadcount--;
    if (_loadcount <= 0) {
      EasyLoading.dismiss();
    }
  }

  FutureOr<void> _initUserEvent(
      InitUserEvent event, Emitter<GlobalState> emit) async {
    UserInfo user =
        event.user ?? UserInfo.fromJson(getSyncBoxJsonByName(userInfoTable));
    OrgInfo org =
        event.org ?? OrgInfo.fromJson(getSyncBoxJsonByName(orgInfoTable));
    emit(state.copyWith(user: user, org: org));
  }

  FutureOr<void> _setWarningEvent(
      SetWarningEvent event, Emitter<GlobalState> emit) {
    Map<String, String> warning = event.warning;
    emit(state.copyWith(warning: {...state.warning, ...warning}));
  }

  FutureOr<void> _removeWarningByKey(
      RemoveWarningByKey event, Emitter<GlobalState> emit) {
    String key = event.key;
    var warning = {...state.warning};
    warning.remove(key);
    emit(state.copyWith(warning: {...warning}));
  }

  FutureOr<void> _toastErrorEvent(
      ToastErrorEvent event, Emitter<GlobalState> emit) {
    String error = event.error;
    Fluttertoast.showToast(
        timeInSecForIosWeb: 3,
        msg: error,
        gravity: ToastGravity.TOP,
        backgroundColor: Colors.white,
        textColor: Colors.red,
        fontSize: 30.sp);
  }

  FutureOr<void> _toastSuccessEvent(
      ToastSuccessEvent event, Emitter<GlobalState> emit) {
    String success = event.success;
    Duration? duration = event.duration;
    double? width = event.width;
    FToast().showToast(
        gravity: ToastGravity.CENTER,
        toastDuration: duration ?? Duration(seconds: 2),
        child: Container(
            padding: EdgeInsets.fromLTRB(10.sm, 10.sm, 15.sm, 15.sm),
            width: width ?? 300.sm,
            decoration: BoxDecoration(
                color: Colors.white, border: Border.all(color: basicColor)),
            child: Wrap(
              alignment: WrapAlignment.center,
              children: [
                Text(
                  success,
                  style: TextStyle(fontSize: 30.sp, color: basicColor),
                )
              ],
            )));
  }

  FutureOr<void> _initDirectoryEvent(
      InitDirectoryEvent event, Emitter<GlobalState> emit) async {
    final Directory _appDocDir = await getApplicationDocumentsDirectory();
    for (String path in directories) {
      Directory _appDocDirFolder = Directory(join(_appDocDir.path, path));
      if (!_appDocDirFolder.existsSync()) {
        _appDocDirFolder.createSync(recursive: true);
      }
    }
    emit(state.copyWith(appDirFolder: _appDocDir.path));
    add(InitWorkBenchEvent());
  }

  FutureOr<void> _versionCheckEvent(
      VersionCheckEvent event, Emitter<GlobalState> emit) async {
    // Current version
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    //设置本地版本信息
    PackageInfos _packageInfo = PackageInfos(
      appName: 'Unknown',
      packageName: 'Unknown',
      version: 'Unknown',
      buildNumber: 'Unknown',
    );
    _packageInfo.appName = packageInfo.appName;
    _packageInfo.packageName = packageInfo.packageName;
    _packageInfo.version = packageInfo.version;
    _packageInfo.buildNumber = packageInfo.buildNumber;
    emit(state.copyWith(packageInfos: _packageInfo));
    //end
    bool online = await ifonline();
    if (!online) return;

    if (Platform.isIOS && flavor == 'prod') {
      //获取App Store版本信息
      final appVersion =
          await fetch.get('http://itunes.apple.com/cn/lookup?id=1560765903');
      final appVersionJson = jsonDecode(appVersion.toString());
      bool hasCount = appVersionJson["resultCount"] != 0 ? true : false;
      print('appVersionJson==$appVersionJson');
      print('hasCount===$hasCount');
      if (hasCount) {
        // App Store上有app信息执行
        final appstoreVersion = appVersionJson["results"][0]['version'];
        final appstoretrackViewUrl =
            appVersionJson["results"][0]['trackViewUrl'];
        bool hasNewVersion =
            await compareVersion(appstoreVersion, packageInfo.version);
        if (hasNewVersion) {
          final mdContent =
              await fetch.get(apkVersionObsJson[flavor]?['releaseNotes']);
          print('mdContent==$mdContent');
          updateVersionDialog(
            state.navigate.currentContext!,
            content: mdContent.toString(),
            loadUrl: appstoretrackViewUrl,
          );
        }
      }
    } else if (Platform.isAndroid) {
      String version = packageInfo.version;
      String buildNumber = packageInfo.buildNumber;
      print('=== package version ===: $version+$buildNumber');
      // manifest.json content
      final manifestRes =
          await fetch.get(apkVersionObsJson[flavor]?['manifest']);

      var manifest = jsonDecode(manifestRes.toString());
      bool isVersionDiff = manifest["versionName"] != '$version.$buildNumber';
      // Handle show update or not
      if (isVersionDiff) {
        final mdContent =
            await fetch.get(apkVersionObsJson[flavor]?['releaseNotes']);
        updateVersionDialog(state.navigate.currentContext!,
            content: mdContent.toString());
      }
    }
  }

  FutureOr<void> _initWorkBenchEvent(
      InitWorkBenchEvent event, Emitter<GlobalState> emit) async {
    bool online = await ifonline();
    WXVersion wxversion = state.wxversion!;
    for (String element in needPreload) {
      await precacheImage(AssetImage(element), state.navigate.currentContext!);
    }
    print("localVersion====${wxversion.toJson()}");
    final versionUrl = wxVersionZip[flavor]?['releaseVersion'];
    final wxZipUrl = wxVersionZip[flavor]?['wxZip'];
    print('baseUrl=======$baseUrl');
    print('flavor=====$flavor');

    // Function to get wxVersionMap
    Map<String, String> getWxVersionMap(List<String> versionTxt) {
      Map<String, String> wxversionMap = {};
      for (var line in versionTxt) {
        wxversionMap[line.split(': ')[0]] = line.split(': ')[1];
      }
      return wxversionMap;
    }

    // Function to download wx.zip
    Future<void> downloadWxZip(String url, String savePath,
        {Function(int receivedBytes, int totalBytes)?
            onReceiveProgress}) async {
      await fetch.download(url, '$savePath/wx.zip',
          onReceiveProgress: onReceiveProgress);
    }

    // Function to write wx.zip content to file
    Future<void> writeWxZipToFile(String wxdir, String zippath) async {
      ByteData data = await rootBundle.load('lib/assets/wx/$defaultWxZipName');
      List<int> bytes =
          data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
      await File(zippath).writeAsBytes(bytes);
    }

    // Function to extract wx.zip to disk
    Future<void> extractWxZipToDisk(String zippath, String wxdir) async {
      await extractFileToDisk(zippath, wxdir);
    }

    if (online && flavor != "cz") {
      final response = await fetch.get(versionUrl);
      final versionTxt = response.toString().trim().split('\n');
      print('lineVersionTxt===$versionTxt');

      // Extract the following logic into a function
      final wxversionMap = getWxVersionMap(versionTxt);
      if (wxversion.current == null ||
          wxversion.current != wxversionMap['buildVersion']) {
        print('版本更新');
        String wxdir = join(state.appDirFolder!, wxDirectory);

        // Extract the following download functionality into a function
        await downloadWxZip(wxZipUrl, wxdir,
            onReceiveProgress: (receivedBytes, totalBytes) {});

        String zippath =
            join(state.appDirFolder!, wxDirectory, defaultWxZipName);
        print('wxdir===$wxdir');
        print('zippath===$zippath');

        if (Directory(wxdir + "/wx").existsSync()) {
          await Directory(wxdir + "/wx").delete(recursive: true);
        }

        // Extract the following file extraction logic into a function
        await extractWxZipToDisk(zippath, wxdir);
        await Future.delayed(const Duration(seconds: 5));
        emit(state.copyWith(
            inited: true,
            wxversion: WXVersion(
                current: wxversionMap['buildVersion'],
                versionList: ["default"])));
      } else {
        print("无版本更新");
        await Future.delayed(const Duration(seconds: 2));
        emit(state.copyWith(inited: true));
      }
    } else {
      if (wxversion.current == null) {
        String wxdir = join(state.appDirFolder!, wxDirectory);
        String zippath =
            join(state.appDirFolder!, wxDirectory, defaultWxZipName);

        // Extract the following file loading and writing logic into a function
        await writeWxZipToFile(wxdir, zippath);
        await extractWxZipToDisk(zippath, wxdir);
        await Future.delayed(const Duration(seconds: 5));
        emit(state.copyWith(
            inited: true,
            wxversion:
                WXVersion(current: "default", versionList: ["default"])));
      } else {
        await Future.delayed(const Duration(seconds: 2));
        emit(state.copyWith(inited: true));
      }
    }
  }

  FutureOr<void> _logoutEvent(
      LogoutEvent event, Emitter<GlobalState> emit) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    await state.executor.waitForAllTask();
    for (String boxName in asyncClearNeededBySync) {
      Map<String, dynamic> collections = getSyncBoxJsonByName(boxName);
      if (collections["keys"] is List) {
        for (var path in (collections["keys"] as List)) {
          state.executor.scheduleTask(() async {
            await clearLazyBoxByPath(path);
          });
        }
      }
    }
    await state.executor.waitForAllTask();
    for (String tableName in needClearedWhenLogout) {
      if (getSyncByName(tableName) != null) {
        await clearSyncBoxByName(tableName);
      }
    }
    for (String d in needDeletedDirectories) {
      await Directory(join(state.appDirFolder!, d)).delete(recursive: true);
    }
    for (String d in needDeletedDirectories) {
      Directory _appDocDirFolder = Directory(join(state.appDirFolder!, d));
      if (!_appDocDirFolder.existsSync()) {
        _appDocDirFolder.createSync(recursive: true);
      }
    }
    emit(state.copyWith(user: UserInfo(), org: OrgInfo(), warning: {}));
    OfflineState initialState =
        OfflineState.fromJson(getSyncBoxJsonByName(offlineStateTable));
    (mapBlocs["OfflineBloc"] as OfflineBloc)
        .add(InitOfflineStateEvent(initialState));
    add(TranslateEvent(local: Intl.getCurrentLocale()));
    NotificationBloc notificationBloc =
        (mapBlocs["NotificationBloc"] as NotificationBloc);
    notificationBloc.add(StopJPushEvent());
    EasyLoading.dismiss();
  }
}
