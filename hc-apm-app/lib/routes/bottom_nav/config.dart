import 'package:flutter/material.dart';
import 'package:offline_solution/pages/pages.dart';
import 'package:offline_solution/pages/profile/index.dart';
import 'package:offline_solution/pages/notification/index.dart';
import 'package:offline_solution/routes/bottom_nav/item.dart';

List<NavItem> navItems = [
  NavItem(
    text: '首页',
    icon: Icons.home,
    widget: HomeContainer(),
    innerWidget: true,
  ),
  NavItem(
    text: '消息',
    icon: Icons.message,
    innerWidget: true,
    widget: NotificationContainer(),
    showBadge: true,
  ),
  NavItem(
    text: '个人中心',
    icon: Icons.person,
    innerWidget: true,
    widget: PersonalProfile(),
  ),
];
