export './hex_color.dart';

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
import 'package:offline_solution/config/global.dart';
import 'package:offline_solution/interfaces/index.dart';
import 'package:offline_solution/map_to_blocs/global_bloc/bloc.dart';
import 'package:offline_solution/map_to_blocs/map_blocs.dart';
import 'package:offline_solution/map_to_blocs/offline_bloc/bloc.dart';
import 'package:offline_solution/map_to_blocs/server_bloc/bloc.dart';
import 'package:offline_solution/map_to_blocs/timer_bloc/bloc.dart';
import 'package:offline_solution/models/user_info/user_account.dart';
import 'package:offline_solution/models/user_info/user_info.dart';
import 'package:offline_solution/task/sync_task.dart';
import 'package:offline_solution/utils/cache.dart';
import 'package:path/path.dart';
import 'package:permission_handler/permission_handler.dart';

GlobalBloc? getglobalBloc() => (mapBlocs["GlobalBloc"] as GlobalBloc?);
UserAccount? getUserAccount() =>
    UserInfo.fromJson(getSyncBoxJsonByName(userInfoTable))
        .data
        ?.userProfile
        ?.userAccount;

getDataBySizeCount<T>(List<T> data, int pageSize, int pageCount) {
  int end = pageCount * pageSize + pageSize;
  int start = pageCount * pageSize;
  List<T> r = [];
  if (end > data.length) {
    if (start < data.length) {
      r = data.sublist(start);
    }
  } else {
    r = data.sublist(start, end);
  }
  return r;
}

datetimeConvertToString(DateTime? t) {
  return t?.toIso8601String();
}

Future<bool> ifonline() async {
  ConnectivityResult onlinestatus =
      await TimerState.connectivity.checkConnectivity();
  bool online = (onlinestatus == ConnectivityResult.ethernet ||
      onlinestatus == ConnectivityResult.mobile ||
      onlinestatus == ConnectivityResult.wifi);
  return online;
}

Future<Dio?> getFetch() async {
  GlobalBloc globalBloc = (mapBlocs["GlobalBloc"] as GlobalBloc);
  Dio dio = globalBloc.state.dio;
  bool online = await ifonline();
  if (online) {
    return dio;
  }
  return null;
}

/*
 默认只要有token就能使用，如需限制 Guest 用户的 goToLogin 需要传入严格模式
 微工作台需要限制guest用户，goToLogin(strict: true) 还需要判断userAccount是否等于0
 */
goToLogin([bool strict = false]) {
  UserInfo? user = UserInfo.fromJson(getSyncBoxJsonByName(userInfoTable));
  int? expirationTime = user.data?.jwtToken?.expirationTimestamp?.toInt();
  int? nowTime = (DateTime.now().millisecondsSinceEpoch).toInt();
  bool isExpirat = expirationTime?.compareTo(nowTime) == 1 ? false : true;
  bool logged = user.data?.jwtToken?.idToken != null;
  if (strict && logged) {
    logged = user.data!.userProfile!.userAccount!.id != 0;
  }
  if (!logged || isExpirat) {
    Future.delayed(const Duration(seconds: 0), () {
      (mapBlocs["GlobalBloc"] as GlobalBloc)
          .state
          .navigate
          .currentState
          ?.pushNamed('/login');
    });
    return true;
  }
  return false;
}

Map<String, String> converToMapFromDispo(String disposition) {
  List<String> plist = disposition.split(';');
  String fields = plist.where((element) => element.contains('name')).first;
  Iterable<String> filenamelist =
      plist.where((element) => element.contains('filename'));
  String filename = filenamelist.isEmpty ? '=nullName' : filenamelist.first;
  var field = fields.split('=')[1].replaceAll(r'"', "");
  var name = filename.split('=')[1].replaceAll(r'"', "");
  return {"field": field, "filename": name};
}

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
        length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))))
    .toLowerCase();

int getRandomInt(int max) {
  int r = _rnd.nextInt(max);
  return r;
}

String formatterDate({String? formatString, String? dateString}) {
  DateFormat formatter = DateFormat(formatString ?? 'yyyy-MM-dd HH:mm:ss');
  return formatter.format(DateTime.parse(dateString ?? '1988-03-12'));
}

getFirstValueFromMap(Map? m) {
  return m?.values != null
      ? m!.values.isNotEmpty
          ? m.values.first
          : null
      : null;
}

getFirstValueFromIterable(Iterable? m) {
  return m == null
      ? null
      : m.isEmpty
          ? null
          : m.first;
}

onDebounce(void Function(dynamic value) callback) {
  Timer? _debounce;
  return (value) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 600), () {
      callback(value);
    });
  };
}

String getDateTimeGMT() {
  DateFormat f = DateFormat('E, d MMM yyyy HH:mm:ss', 'en-US');
  String date = f.format(DateTime.now().toUtc()) + " GMT";
  return date;
}

List<List<T>> splitResuestsIntoChunksByLimit<T>(List<T> listObjects) {
  List<List<T>> chunks = [];
  for (var i = 0; i < listObjects.length; i += limitFutureOfRequest) {
    chunks.add(listObjects.sublist(
        i,
        i + limitFutureOfRequest > listObjects.length
            ? listObjects.length
            : i + limitFutureOfRequest));
  }
  return chunks;
}

bool isNumeric(String? s) {
  if (s == null) {
    return false;
  }
  try {
    double.parse(s);
    return true;
  } catch (e) {
    return false;
  }
}

int? toInt(dynamic s) {
  if (s is num) return s.toInt();
  try {
    return num.parse(s).toInt();
  } catch (e) {
    return null;
  }
}

double? toDouble(dynamic s) {
  if (s is num) return s.toDouble();
  try {
    return double.parse(s);
  } catch (e) {
    return null;
  }
}

class SingleFileObject {
  List<int> bytes;
  String filename;
  String contentType;
  Map<String, dynamic>? json;
  SingleFileObject(
      {required this.bytes,
      required this.filename,
      required this.contentType,
      this.json});
}

Future<SingleFileObject> extractFileBytesFromRequest(
    HttpRequest request) async {
  final boundary = request.headers.contentType!.parameters['boundary']!;
  final mimeTransformer = MimeMultipartTransformer(boundary);
  final parts = request.cast<List<int>>().transform(mimeTransformer);
  await for (MimeMultipart part in parts) {
    List<List<int>> contentList = await part.toList();
    String disposition = part.headers["content-disposition"] ?? '';
    String contentType = part.headers["content-type"] ?? "";
    Map<String, String> disConnfig = converToMapFromDispo(disposition);
    List<int> _bytes =
        contentList.reduce((value, element) => [...value, ...element]);
    if (contentType.isNotEmpty) {
      return SingleFileObject(
          bytes: _bytes,
          filename: disConnfig["filename"]!,
          contentType: contentType);
    } else {
      return SingleFileObject(
          bytes: [],
          json: {disConnfig["field"]!: utf8.decode(_bytes)},
          filename: disConnfig["filename"]!,
          contentType: contentType);
    }
  }
  return SingleFileObject(bytes: [], json: {}, filename: "", contentType: "");
}

Future<bool> handlePermission(Permission permission) async {
  if (await permission.isGranted) {
    return true;
  } else {
    PermissionStatus result = await permission.request();
    if (result == PermissionStatus.granted) {
      return true;
    }
  }
  return false;
}

dynamic getPostData(String content) {
  dynamic data;
  try {
    data = jsonDecode(content.isEmpty || content == 'null' ? "{}" : content);
  } catch (e) {
    data = content;
  }
  return data;
}

InterfaceObject getObjectHubInterfaceObjectByMap(attach) {
  Map<String, dynamic> _attatchobject = attach as Map<String, dynamic>;
  InterfaceObject object = InterfaceObject(
      interfaceType: InterfaceType.BY_PATH_OBJECTHUB,
      responseType: ResponseType.bytes,
      objectDirectory: attatchmentsDirectory,
      objectStorageId: _attatchobject["objectStorageId"],
      objectExtension: _attatchobject["objectName"] is String
          ? extension(_attatchobject["objectName"])
          : null,
      objectType: _attatchobject["objectType"] is String
          ? '${_attatchobject["objectType"]};charset=UTF-8'
          : null,
      path: '${attatchmentsFlag}${_attatchobject["objectStorageId"]}');
  return object;
}

Future<Map<dynamic, dynamic>> syncAndSetAttach(Map<dynamic, dynamic> item,
    [List<String>? attatchFields]) async {
  if (attatchFields == null) {
    attatchFields = ["attachments"];
  }
  Map<String, dynamic> offlineAttatchmentsCollection =
      getSyncBoxJsonByName(offlineTaskAttatchmentsCollection);
  for (String field in attatchFields) {
    dynamic attachments = item[field];
    if (attachments is List) {
      for (var _attach in attachments) {
        Map attach = _attach as Map;
        if ((attach["objectStorageId"] as String).startsWith(localUrlFlag)) {
          String objId = attach["objectStorageId"] as String;
          Map objMap =
              (offlineAttatchmentsCollection[objId] as Map).values.first;
          SyncTask syncTask = SyncTask.fromJson(objMap.cast());
          Response objResponse = await syncTask.sync();
          Map objPreview = objResponse.data["data"];
          attach["objectSize"] = (objPreview["objectSize"] as num?)?.toInt();
          attach["objectStorageId"] = objPreview["objectId"];
          attach["original"] = "${attatchmentsFlag}${objPreview["objectId"]}";
          attach["thumbnail"] = "${attatchmentsFlag}${objPreview["objectId"]}";
          attach.remove("objectPath");
        }
      }
    }
    if (attachments is String && attachments.startsWith(localUrlFlag)) {
      Map objMap =
          (offlineAttatchmentsCollection[attachments] as Map).values.first;
      SyncTask syncTask = SyncTask.fromJson(objMap.cast());
      Response objResponse = await syncTask.sync();
      Map objPreview = objResponse.data["data"];
      item[field] = objPreview["objectId"];
    }
  }
  return item;
}

Image renderImage(e, ServerState serverState) {
  Map _attatchMap = e as Map;
  String? _dir = (_attatchMap["objectPath"] as String?)
      ?.replaceFirst('/$localUrlFlag/', '');
  String filePath = join(getglobalBloc()!.state.appDirFolder!, _dir ?? '',
      (_attatchMap["objectStorageId"] as String));
  return _dir != null
      ? Image.file(File(filePath),
          fit: BoxFit.cover, width: 200.sm, height: 200.sm)
      : Image.network(
          'http://${serverState.httpAdress}:${serverState.port}${_attatchMap["thumbnail"] as String}',
          fit: BoxFit.cover,
          width: 200.sm,
          height: 200.sm);
}

List<String> getActiveOfflineModelNames(OfflineState offlineState) {
  List<String> _names = [];
  if (offlineState.assetsQuery.active) {
    _names.add(assetsQuery);
  }
  if (offlineState.nurseSite.active) {
    _names.add(clinicAssistant);
  }
  if (offlineState.workOrder.active) {
    _names.add(myRepairOrders);
  }
  if (offlineState.pmWorkOrder.active) {
    _names.add(myMaintainanceOrders);
  }
  if (offlineState.inventory.active) {
    _names.add(inventoryOrders);
  }
  return _names;
}

//版本号比较，判断是否提示更新app
Future<bool> compareVersion(newVersion, oldVersion) async {
  if (newVersion == null ||
      newVersion.isEmpty ||
      oldVersion == null ||
      oldVersion.isEmpty) {
    print('版本号不能为空');
    return false;
  }
  int newVersionInt, oldVersionInt;
  var newList = newVersion.split('.'), oldList = oldVersion.split('.');
  int newList_len = newList.length;
  int oldList_len = oldList.length;
  if (newList_len == 0 || oldList_len == 0) {
    return false;
  }
  for (int i = 0; i < newList_len; i++) {
    newVersionInt = int.parse(newList[i]);
    oldVersionInt = int.parse(oldList[i]);
    if (newVersionInt > oldVersionInt) {
      return true;
    } else if (newVersionInt > oldVersionInt) {
      return false;
    }
  }
  return false;
}
