import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:offline_solution/map_to_blocs/global_bloc/bloc.dart';
import 'package:offline_solution/map_to_blocs/map_blocs.dart';
import 'package:offline_solution/widgets/scan/index.dart';
import 'package:offline_solution/widgets/scan/scan_object/scan_object.dart';
import 'package:offline_solution/widgets/webview.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../param_object/param_object.dart';

part 'js.dart';
part 'handler.dart';

GlobalBloc _globalBloc = (mapBlocs["GlobalBloc"] as GlobalBloc);

GlobalKey<NavigatorState> _getNavigator() => _globalBloc.state.navigate;

String _handlerName = 'GfcScan';
String _handlerCall = '$_handlerName.postMessage';
String _qrCodeType = 'qrCode';
String _barCodeType = 'barCode';

JavascriptHandler scanHandler = JavascriptHandler(
    name: _handlerName, onMessageReceived: _onMessageReceived);
