part of 'index.dart';

FutureOr<dynamic> Function(JavaScriptMessage, WebViewController)
    _onMessageReceived = (result, controller) async {
  String jsonString = result.message;
  ParamObject params = ParamObject.fromJson(json.decode(jsonString));
  notMatch() => controller.runJavaScript("window.gfc_scan_onfail('扫码类型不匹配')");
  try {
    ScanObject? scanObject =
        await _getNavigator().currentState?.push<ScanObject>(MaterialPageRoute(
              fullscreenDialog: true,
              settings: RouteSettings(name: 'type', arguments: params.name),
              builder: (context) {
                return const BarcodeScannerWithController();
              },
            ));
    if (scanObject != null) {
      if (params.name == 'all') {
        controller.runJavaScript(
            "window.gfc_scan_onsuccess({text:'${scanObject.text}',type:'${scanObject.type == _qrCodeType ? _qrCodeType : _barCodeType}'})");
      } else if (params.name == _qrCodeType) {
        scanObject.type == _qrCodeType
            ? controller.runJavaScript(
                "window.gfc_scan_onsuccess({text:'${scanObject.text}',type:'$_qrCodeType'})")
            : notMatch();
      } else {
        scanObject.type == _qrCodeType
            ? notMatch()
            : controller.runJavaScript(
                "window.gfc_scan_onsuccess({text:'${scanObject.text}',type:'$_barCodeType'})");
      }
    } else {
      controller.runJavaScript("window.gfc_scan_onfail('扫码取消')");
    }
  } on Exception {
    controller.runJavaScript("window.gfc_scan_onfail('扫码错误')");
  }
};
