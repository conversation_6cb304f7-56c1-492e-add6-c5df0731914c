import 'package:flutter_screenutil/flutter_screenutil.dart';

Map<String, dynamic> defaultResponse = {
  "message": null,
  "referencesURLs": null,
  "data": null,
  "bizStatusCode": "OK",
  "rowCount": 0
};
String registerImage = 'lib/assets/images/register_banner.png';
List<String> bannerImages = [
  'lib/assets/images/appbar0.png',
  'lib/assets/images/appbar1.png',
  'lib/assets/images/appbar2.png'
];
String iconfamily = 'geicon';
double bannerHeight = 400.sm;
double bottomNavHeight = 200.sm;
String profileIcon = 'lib/assets/images/profile_icon.png';
double recordLimited = 120;

String audioDirectory = 'apm_audio';
String imageDirectory = 'apm_image';
String wxDirectory = 'apm_wx';
String attatchmentsDirectory = 'apm_attatchments';
String serverAttatchmentsDirectory = 'server_attatchments';
String offlineTaskAttatchmentsDirectory = 'offline_task_attatchments';
String attatchmentsFlag =
    '/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/';
String attatchmentsDownloadFlag =
    '/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/download/';
int limitFutureOfRequest = 16;

List<String> needDeletedDirectories = [
  attatchmentsDirectory,
  serverAttatchmentsDirectory,
  offlineTaskAttatchmentsDirectory,
  audioDirectory,
  imageDirectory
];

Set<String> directories = {
  audioDirectory,
  imageDirectory,
  wxDirectory,
  attatchmentsDirectory,
  serverAttatchmentsDirectory,
  offlineTaskAttatchmentsDirectory
};

//assets里的默认zip名字
String defaultWxZipName = 'wx.zip';
//微信压缩时文件夹名字要与webpack的publicPath保持一致
String wxfolderName = 'wx';

String localUrlFlag = 'geresources';

String demo1 = 'lib/assets/images/demo1.png';
String demo2 = 'lib/assets/images/demo2.png';
String demo3 = 'lib/assets/images/demo3.png';
String demo4 = 'lib/assets/images/demo4.png';
String demo5 = 'lib/assets/images/demo5.png';

String registerGuide = 'lib/assets/images/register_guide.png';
String loginGuide = 'lib/assets/images/login_guide.png';
String connectGuide = 'lib/assets/images/connect_guide.png';

List<String> needPreload = [
  registerImage,
  ...bannerImages,
  profileIcon,
  demo1,
  demo2,
  demo3,
  demo4,
  demo5,
  registerGuide,
  loginGuide,
  connectGuide
];

//offline model name
String assetsQuery = '设备查询';
String clinicAssistant = '临床助手-日常检查';
String myRepairOrders = '我的维修工单';
String myMaintainanceOrders = '我的保养工单';
String inventoryOrders = '盘点工单';
//全局环境变量
String flavor = '';

String baseUrl = '';

//更新zip包对应map
Map<String, Map> wxVersionZip = {
  "dev": {
    "releaseVersion":
        "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctstg/releaseVersion.txt",
    "wxZip": "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctstg/wx.zip"
  },
  "pre": {
    "releaseVersion":
        "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctpreprd/releaseVersion.txt",
    "wxZip": "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctpreprd/wx.zip"
  },
  "prod": {
    "releaseVersion":
        "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctprod/releaseVersion.txt",
    "wxZip": "https://apm-acct-wx.oos-cn.ctyunapi.cn/acctprod/wx.zip"
  },
  "zz": {
    "releaseVersion": "http://**************:8000/app/wx/releaseVersion.txt",
    "wxZip": "http://**************:8000/app/wx/wx.zip"
  }
};

// 更新安卓apk版本,获取版信息文件地址
Map<String, Map> apkVersionObsJson = {
  "dev": {
    "manifest":
        "https://apm-acct-android-app-stg.obs.cn-bj1.ctyun.cn/manifest.json",
    "releaseNotes":
        "https://apm-acct-android-app-stg.obs.cn-bj1.ctyun.cn/release_notes.md",
    "downloadUrl":
        "https://apm-acct-android-app-stg.obs.cn-bj1.ctyun.cn/gehc.apm.app.apk",
  },
  "prod": {
    "manifest":
        "https://apm-acct-android-app-prod.obs.cn-bj1.ctyun.cn/manifest.json",
    "releaseNotes":
        "https://apm-acct-android-app-prod.obs.cn-bj1.ctyun.cn/release_notes.md",
    "downloadUrl":
        "https://apm-acct-android-app-prod.obs.cn-bj1.ctyun.cn/gehc.apm.app.apk",
  },
  "zz": {
    "manifest": "http://**************:8000/app/bin/manifest.json",
    "releaseNotes": "http://**************:8000/app/bin/release_notes.md",
    "downloadUrl": "http://**************:8000/app/bin/gehc.apm.app.apk",
  },
};

Map<String, String> APM_SERVER = {
  "dev": "https://apm-acct-stg.gehealthcloud.cn",
  "pre": "https://apm-acct-preprd.gehcedison.cn",
  "prod": "https://hcapm.gehealthcloud.cn",
  "zz": "http://**************:8000",
  "cz": "https://apm.czfybjy.com:10000",
};

Map<String, String> WX_SERVER_ID = {
  "dev": "Bi_4oGbEkDvvrSLD93oU",
  "pre": "GhyIkMXEv5jArYHz90kI",
  "prod": "nCnx6dbEDItzrZKK93yO",
  "cz": "nCnx6dbEDItzrZKK93yO",
};
