<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
		<string>com.transistorsoft.custom</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>GE资产智管家</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>zh</string>
	</array>
	<key>CFBundleName</key>
	<string>apm</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>https</string>
		<string>http</string>
		<string>tel</string>
		<string>URL</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>NSCalendarsUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSCameraUsageDescription</key>
	<string>We need to access your camera so that you can scan the QRcode for repair</string>
	<key>NSContactsUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Using the local network</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need to access your microphone so that you can upload your voice description about repair </string>
	<key>NSMotionUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need to access your photo library so that you can upload photos related to orders</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
