PODS:
  - audio_session (0.0.1):
    - Flutter
  - background_fetch (1.1.0):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_sound (9.2.13):
    - Flutter
    - flutter_sound_core (= 9.2.13)
  - flutter_sound_core (9.2.13)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (4.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 3.0.0)
  - GoogleMLKit/MLKitCore (4.0.0):
    - MLKitCommon (~> 9.0.0)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (2.3.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - JCore (4.2.1)
  - JPush (5.0.1):
    - JCore (>= 2.0.0)
  - jpush_flutter (0.0.2):
    - Flutter
    - JCore (= 4.2.1)
    - JPush (= 5.0.1)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta4)
  - MLKitBarcodeScanning (3.0.0):
    - MLKitCommon (~> 9.0)
    - MLKitVision (~> 5.0)
  - MLKitCommon (9.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - MLKitVision (5.0.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
    - MLImage (= 1.0.0-beta4)
    - MLKitCommon (~> 9.0)
  - mobile_scanner (3.2.0):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 4.0.0)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - open_file (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - path_provider_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - ReachabilitySwift (5.0.0)
  - SDWebImage (5.18.2):
    - SDWebImage/Core (= 5.18.2)
  - SDWebImage/Core (5.18.2)
  - SDWebImageWebPCoder (0.13.0):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SwiftyGif (5.4.4)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - background_fetch (from `.symlinks/plugins/background_fetch/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - jpush_flutter (from `.symlinks/plugins/jpush_flutter/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - open_file (from `.symlinks/plugins/open_file/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - flutter_sound_core
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - JCore
    - JPush
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  background_fetch:
    :path: ".symlinks/plugins/background_fetch/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  jpush_flutter:
    :path: ".symlinks/plugins/jpush_flutter/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  open_file:
    :path: ".symlinks/plugins/open_file/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audio_session: 9bdd3bf46960d4322cb8c3cb6138295dcfe84eee
  background_fetch: 97b293539fc1a231f759ce577ea07424cfcb86c9
  connectivity_plus: 8443422d4c5a53dee0d50779ec5dbcda1071251e
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: 5f42b9d5580e30b57b4863f9d94b448016b702e5
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_downloader: 78da0da1084e709cbfd3b723c7ea349c71681f09
  flutter_image_compress: 4b058288a81f76e5e80340af37c709abafff34c4
  flutter_native_splash: 9e672d3818957718ee006a491730c09deeecace9
  flutter_sound: 49be32081884d275fe91d48262f4b1fcd86e10d3
  flutter_sound_core: 26c10e5832e76aaacfae252d8925232281c486ae
  fluttertoast: 294ad8b4b83f149520f7c13484770323c54df109
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleMLKit: 2bd0dc6253c4d4f227aad460f69215a504b2980e
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  image_picker_ios: afb77645f1e1060a27edb6793996ff9b42256909
  JCore: d994e32edb50add8a33135473acca799d882c5fa
  JPush: bf8d6011eb80c776aa40a0c4f5c25fd10335af9c
  jpush_flutter: 83ef5c84ac1802c5921bd8891cf1e21ef4c1a403
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 7bb7c4264164ade9bf64f679b40fb29c8f33ee9b
  MLKitBarcodeScanning: 04e264482c5f3810cb89ebc134ef6b61e67db505
  MLKitCommon: c1b791c3e667091918d91bda4bba69a91011e390
  MLKitVision: 8baa5f46ee3352614169b85250574fde38c36f49
  mobile_scanner: e1bec31faf98bffdc1a05130d03a70984c4d1da9
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  open_file: 898f23092cb034fda4ae3b268d782fe25d525e6b
  package_info_plus: 5076a1ce937258be9d2d41781a0e9cd71f19a82f
  path_provider_foundation: 2a68637f8a62df7f6e790a428d1bdf72cb4e2d59
  path_provider_ios: 025d0bdaa9f9ab016700ac1e07ff63415cf638e9
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  photo_manager: fb3d7277cd57e7c5e169a3634aa0fe1720fb003a
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SDWebImage: c0de394d7cf7f9838aed1fd6bb6037654a4572e4
  SDWebImageWebPCoder: af09429398d99d524cae2fe00f6f0f6e491ed102
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: b7f13d9491e07f5230bf3e056ab68678f896980a
  video_player_avfoundation: a8ea941d9519a60fa93a9908123c68e0917cbd1d
  webview_flutter_wkwebview: 75789e80e2ad25e78ac88a17e5c2f82019277c53

PODFILE CHECKSUM: 0d82e94821348223c48961938dc0697b46149f54

COCOAPODS: 1.16.2
