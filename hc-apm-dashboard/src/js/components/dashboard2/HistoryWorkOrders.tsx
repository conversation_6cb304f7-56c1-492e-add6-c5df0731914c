import React, { useState, useEffect, useRef } from "react";
import Background from "containers/dashboard2/Background";
import Ellipsus from "ellipsus";
import { translate } from "react-i18next";
import { connect } from "dva";
import { apply } from "noru-utils/lib";
import moment from "moment";
import withClientRect from "hoc/withClientRect";
import periodic from "hoc/periodic";
import config from "utils/config";
import { Observable } from "rxjs";
import { isInternationalSite } from "utils/helpers";
import { DB2_COMP_KEY as COMP_KEY, DATE_FORMAT } from "utils/constants";
import { calculateLineCount, FlipWrapper } from "./WorkOrders";
import "./HistoryWorkOrders.scss";

function HistoryWorkOrders(props: any) {
  const [rows, setRows] = useState<Array<any>>([]);
  const paginationRef = useRef(null);
  const configRef = useRef(null);
  const { t, dispatch, data, fontSize, fullscreened, clientRect } = props;

  const _toggleFullscreen = () => {
    let payload =
      fullscreened === COMP_KEY.HistoryWorkOrder
        ? undefined
        : COMP_KEY.HistoryWorkOrder;
    dispatch({ type: "dashboard2/set/fullscreened", payload });
  };
  const getData = () => {
    let conf = config.get(COMP_KEY.HistoryWorkOrder);
    let to = moment().format(DATE_FORMAT);
    let from = moment().subtract(conf.month.value, "month").format(DATE_FORMAT);
    dispatch({
      type: "dashboard2/get/history-work-orders",
      payload: { from, to, status: 2 },
    });
  };

  useEffect(() => {
    configRef.current = config.get(COMP_KEY.HistoryWorkOrder);
    getData(); // 组件挂载后立即获取数据
    // 如果需要定期刷新数据，可以设置一个定时器
    const intervalId = setInterval(() => {
      getData();
    }, CONF.interval || 60000); // 使用配置的间隔时间或默认60秒
    return () => {
      clearInterval(intervalId);
    };
  }, []);

  useEffect(() => {
    if (!data || !data.length) {
      setRows([]);
      return;
    }
    if (data) {
      let height = clientRect.height;
      let lines = calculateLineCount(height, fontSize * 1.25);
      //"计算行数:", lines, "数据长度:", data.length

      if (paginationRef.current) {
        paginationRef.current!.unsubscribe();
      }

      // 确保数据有效且有长度

      //创建新的分页订阅"
      const intervalPerPage = configRef.current!.intervalPerPage || 10000; // 提供默认值

      paginationRef.current = Observable.timer(0, intervalPerPage)
        .do((i) => {
          const pages = Math.max(1, Math.ceil(data.length / lines)); // 确保至少有1页
          const start = (i % pages) * lines;
          const end = Math.min(start + lines, data.length); // 确保不超出数组范围
          setRows(data.slice(start, end));
        })
        .subscribe({error: (error) => console.error("分页订阅错误:", error)});
    }

    return () => {
      if (paginationRef.current) {
        //"清理分页订阅"
        paginationRef.current!.unsubscribe();
      }
    };
  }, [data, clientRect, fontSize]);

  return (
    <Background
      className="history-work-order"
      style={{ fontSize: fullscreened ? "1.1rem" : "" }}
    >
      <h1 onClick={_toggleFullscreen}>{t("history_work_order")}</h1>
      <div className="table-container">
        <Table rows={rows} t={t} />
      </div>
    </Background>
  );
}

const CONF = config.get(COMP_KEY.HistoryWorkOrder);
export default apply(
  HistoryWorkOrders,
  periodic(CONF.interval, "getData"),
  withClientRect,
  translate(["common", "dashboard2"]),
  connect(({ dashboard2 }) => ({
    data: dashboard2.get("HistoryWorkOrders"),
    fontSize: dashboard2.get("BaseFontSize"),
    fullscreened: dashboard2.get("fullscreened"),
  }))
);

function Table({ rows, t }: { rows: any[]; t: any }) {
  if (isInternationalSite()) moment.locale("en");
  return (
    <table>
      <thead>
        <tr>
          <th>
            <span>{t`asset_name`}</span>
          </th>
          <th>
            <span>{t`incident_clinical_dept`}</span>
          </th>
          <th>
            <span>{t`reporter`}</span>
          </th>
          <th>
            <span>{t`repair_person`}</span>
          </th>
          <th>
            <span>{t`closed_orders_time`}</span>
          </th>
        </tr>
      </thead>
      <tbody>
        {rows.length === 0 ? (
          <tr>
            <td colSpan={5} style={{ textAlign: "center" }}>
              {t("no_data")}
            </td>
          </tr>
        ) : (
          rows.map((row, i) => {
            return (
              <tr key={String(i)}>
                <FlipWrapper className="asset-name">
                  <Ellipsus>{row.assetName || ""}</Ellipsus>
                </FlipWrapper>
                <FlipWrapper className="text-flow">
                  {row.clinicalDept}
                </FlipWrapper>
                <FlipWrapper>{row.reporter}</FlipWrapper>
                <FlipWrapper>{row.repairPersonnel}</FlipWrapper>
                <FlipWrapper className="text-flow">
                  {isInternationalSite()
                    ? row.closeTime &&
                      moment(row.closeTime).format("MMM-DD-YYYY HH:mm")
                    : row.closeTime &&
                      row.closeTime
                        .substr(0, 16)
                        .replace(new RegExp("-", "g"), "/")}
                </FlipWrapper>
              </tr>
            );
          })
        )}
      </tbody>
    </table>
  );
}
