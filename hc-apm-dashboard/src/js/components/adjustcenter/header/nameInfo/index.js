import React from "react";
import { connect } from "dva";
import { AuthConfig } from "utils/config";
import "./index.scss";

@connect((state) => {
  return {
    config: state.adjCenter,
    orgInfo: state.orgInfo,
  };
})
class NameInfo extends React.Component {
  componentDidMount() {
    let uid = AuthConfig.get("hc-apm-profile")
      ? AuthConfig.get("hc-apm-profile").userAccount
        ? AuthConfig.get("hc-apm-profile").userAccount.siteUID
        : ""
      : "";
    this.props.dispatch({
      type: "orgInfo/getOrgInfo",
      uid,
    });
  }
  render() {
    const { text, fontSize, color } = this.props.config || {};
    const { name } = this.props.orgInfo || {};
    let size = "1em";
    fontSize === "big"
      ? (size = "1.4em")
      : fontSize === "small"
      ? (size = "0.6em")
      : (size = "1em");

    return (
      <div className="adjust-name-info-content">
        <div className="adjust-name-info-hospital">
          {`${name ? name : ""} 设备调剂中心`}{" "}
        </div>
        <div
          style={{ color: color, fontSize: size }}
          className="adjust-name-info-flow"
        >
          <span>{text}</span>
        </div>
      </div>
    );
  }
}

export default NameInfo;
