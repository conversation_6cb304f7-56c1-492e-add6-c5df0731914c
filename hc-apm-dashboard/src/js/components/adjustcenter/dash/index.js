import React from "react";
import { connect } from 'dva';
import DashItem from "./dashItem";
import "./index.scss";

@connect(state => {
  return {
    dash: state.adjustDash
  }
})
class Dash extends React.Component {
  componentDidMount(){
    this.props.dispatch({
      type: 'adjustDash/startInterval'
    });
  }
  render() {
    const { dash } = this.props;
    return (
      <div className="adjust-dash-content">
        <div className="intro-block">
          <div className="monitor">
					<svg version="1.1"  xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
						 viewBox="0 0 500 200" style={{ground:'new 0 0 500 200' }} xmlSpace="preserve">
					<g>
						<polyline className="adjust-ekg" points="486.6,113.8 328.2,113.8 310.3,132.3 296,70.7 246.8,127.4 241.6,120.2 233.9,166.4 227,27.6
							213.2,118.3 211.8,112.3 205.1,126.1 198.2,108.5 194.1,124.4 184.5,92.9 174.1,113 4.3,113 	"/>
					</g>
					</svg>
        </div>
        </div>
        <div className="adjust-dash-item">
          <DashItem icon="ge-GE-Center" name="调剂中心设备总数" num={dash.total} />
        </div>
        <div className="adjust-dash-item">
          <DashItem icon="ge-GE-Asset" name="申请设备总数" num={dash.applied} />
        </div>
        <div className="adjust-dash-item">
          <DashItem icon="ge-GE-Loan" name="出借设备总数" num={dash.inRent} />
        </div>
        <div className="adjust-dash-item">
          <DashItem icon="ge-GE-Avilavle" name="可用设备总数" num={dash.available} />
        </div>
        <div className="adjust-dash-item">
          <DashItem icon="ge-repaire" name="维修设备总数" num={dash.down} />
        </div>
      </div>
    );
  }
}

export default Dash;
