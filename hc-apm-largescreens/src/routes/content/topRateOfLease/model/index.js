import request from '@/utils/$axios';

const url = '/api/apm/report/sharedAsset/rentingRateTop3';

export default {
    name: 'topRateOfLease',
    init: {
        list: []
    },
    effects: {
        getData: async ({ siteUid, dom }, { setState }) => {
            if (!siteUid) return;
            const data = await request.loading({ loadingDom: dom }).get(url, { params: { siteUid } });
            if (Array.isArray(data)) {
                setState({ list: data });
            }
        }
    }
}