import request from '../utils/$axios';

const siteListUrl = '/api/apm/report/sharedAsset/siteList';
const _modality_url = '/api/apm/report/xinhe/modalityTypeList';
const rsiteListUrl = '/api/apm/report/xinhe/siteList';

export default {
    name: 'global',
    init: {
        siteList: [],
        equipList: [],
        modality: [],
        rsiteList: []
    },
    effects: {
        async getSiteList({ dom }, { select, state, setState }) {
            const siteList = await request.loading({ loadingDom: dom }).get(siteListUrl);
            if (Array.isArray(siteList)) {
                setState({ ...state.value, siteList });
                const setSiteUid = select('siteUid')[1];
                setTimeout(() => {
                    siteList[0] && setSiteUid({ value: siteList[0].uid, name: siteList[0].name });
                }, 0);
            }
        },
        async getRadiationSiteList({ dom }, { select, state, setState }) {
            let siteList = await request.loading({ loadingDom: dom }).get(rsiteListUrl);
            siteList = [{ uid: null, name: '全部' }, ...siteList];
            if (Array.isArray(siteList)) {
                setState({ ...state.value, siteList });
                const setSiteUid = select('siteUid')[1];
                setTimeout(() => {
                    siteList[0] && setSiteUid({ value: siteList[0].uid, name: siteList[0].name });
                }, 10);
            }
        },
        async getModality(siteUID, { state, setState }) {
            if (!siteUID) return;
            const modality = await request.get(_modality_url, { params: { siteUID } });
            if (Array.isArray(modality)) {
                setState({
                    ...state.value,
                    modality: modality.filter((it) => it).concat([{ modalityId: 'all_selected', modalityType: '全部' }])
                });
            }
        },
        async getEquipList({ state, setState }) {}
    }
};
